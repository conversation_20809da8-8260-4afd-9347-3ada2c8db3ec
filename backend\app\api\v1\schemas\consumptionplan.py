"""
ConsumptionPlan 序列化模式
"""

from marshmallow import Schema, fields, validate

class ConsumptionPlanSchema(Schema):
    """ConsumptionPlan 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    area_id = fields.Integer(required=True)
    
    
    
    consumption_date = fields.Date(required=True)
    
    
    
    meal_type = fields.String(required=True)
    
    
    
    diners_count = fields.Integer()
    
    
    
    status = fields.String(required=True)
    
    
    
    created_by = fields.Integer(required=True)
    
    
    
    approved_by = fields.Integer()
    
    
    
    notes = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
import request from '@/utils/request'

const recipeingredientAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/recipeingredient',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/recipeingredient/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/recipeingredient',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/recipeingredient/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/recipeingredient/${id}`,
      method: 'delete'
    })
  }
}

export default recipeingredientAPI
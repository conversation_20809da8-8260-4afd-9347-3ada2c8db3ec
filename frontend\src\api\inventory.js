import request from '@/utils/request'

const inventoryAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/inventory',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/inventory/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/inventory',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/inventory/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/inventory/${id}`,
      method: 'delete'
    })
  }
}

export default inventoryAPI
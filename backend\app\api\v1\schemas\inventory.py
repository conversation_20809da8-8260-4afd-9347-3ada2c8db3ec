"""
Inventory 序列化模式
"""

from marshmallow import Schema, fields, validate

class InventorySchema(Schema):
    """Inventory 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    warehouse_id = fields.Integer(required=True)
    
    
    
    storage_location_id = fields.Integer(required=True)
    
    
    
    ingredient_id = fields.Integer(required=True)
    
    
    
    batch_number = fields.String(required=True)
    
    
    
    quantity = fields.Float(required=True)
    
    
    
    unit = fields.String(required=True)
    
    
    
    production_date = fields.Date(required=True)
    
    
    
    expiry_date = fields.Date(required=True)
    
    
    
    supplier_id = fields.Integer()
    
    
    
    status = fields.String(required=True)
    
    
    
    notes = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
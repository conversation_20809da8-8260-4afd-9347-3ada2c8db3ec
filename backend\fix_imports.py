#!/usr/bin/env python3
"""
修复模型文件的导入问题
"""

import os
from pathlib import Path

def fix_models_imports():
    """修复模型文件的导入"""
    print("🔧 修复模型文件的导入...")

    models_file = Path('app/models/models.py')

    if not models_file.exists():
        print("❌ 模型文件不存在")
        return False

    # 读取文件内容
    with open(models_file, 'r', encoding='utf-8') as f:
        content = f.read()

    # 修复导入语句
    content = content.replace(
        'from app import db, login_manager',
        '# 这些将在应用初始化时设置\ndb = None\nlogin_manager = None'
    )

    content = content.replace(
        'from app.utils.datetime_helper import format_datetime',
        '''def format_datetime(dt, fmt):
    """简单的日期时间格式化函数"""
    if dt and hasattr(dt, "strftime"):
        return dt.strftime(fmt)
    return str(dt) if dt else None'''
    )

    # 写回文件
    with open(models_file, 'w', encoding='utf-8') as f:
        f.write(content)

    print("✅ 模型文件导入修复完成")
    return True

def create_models_init():
    """创建模型初始化文件"""
    print("📝 创建模型初始化文件...")

    init_content = '''"""
模型包初始化文件
"""

# 导入所有模型
from .models import *
'''

    init_file = Path('app/models/__init__.py')
    with open(init_file, 'w', encoding='utf-8') as f:
        f.write(init_content)

    print("✅ 模型初始化文件创建完成")

def main():
    """主函数"""
    print("🚀 开始修复导入问题")
    print("=" * 50)

    try:
        # 修复模型文件导入
        if not fix_models_imports():
            return False

        # 创建模型初始化文件
        create_models_init()

        print("\n" + "=" * 50)
        print("🎉 导入问题修复完成！")
        print("✅ 模型文件导入已修复")
        print("✅ 模型初始化文件已创建")

        return True

    except Exception as e:
        print(f"❌ 修复过程中出错: {e}")
        return False

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)

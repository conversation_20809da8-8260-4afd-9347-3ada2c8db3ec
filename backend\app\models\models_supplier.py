"""
供应商管理模块的数据库模型
这些模型将添加到现有的models.py文件中
"""
from datetime import datetime
from app import db
from sqlalchemy.dialects.mssql import DATETIME2

# 供应商-学校关联表
class SupplierSchoolRelation(db.Model):
    """供应商-学校关联表"""
    __tablename__ = 'supplier_school_relations'

    id = db.Column(db.Integer, primary_key=1)
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'), nullable=0)
    area_id = db.Column(db.Integer, db.ForeignKey('administrative_areas.id'), nullable=0)  # 学校级别的区域
    contract_number = db.Column(db.String(100), nullable=1)  # 合同编号
    start_date = db.Column(db.Date, nullable=0)  # 合作开始日期
    end_date = db.Column(db.Date, nullable=1)  # 合作结束日期（可为空表示长期合作）
    status = db.Column(db.Integer, default=1, nullable=0)  # 0-已终止, 1-有效
    notes = db.Column(db.Text, nullable=1)  # 备注
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=0)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=0)

    # 关系
    supplier = db.relationship('Supplier', backref='school_relations')
    area = db.relationship('AdministrativeArea', backref='supplier_relations')

    def to_dict(self):
        return {
            'id': self.id,
            'supplier_id': self.supplier_id,
            'supplier_name': self.supplier.name,
            'area_id': self.area_id,
            'area_name': self.area.name,
            'contract_number': self.contract_number,
            'start_date': self.start_date.strftime('%Y-%m-%d'),
            'end_date': self.end_date.strftime('%Y-%m-%d') if self.end_date else None,
            'status': self.status,
            'notes': self.notes,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S')
        }

# 产品规格参数表
class ProductSpecParameter(db.Model):
    """产品规格参数表"""
    __tablename__ = 'product_spec_parameters'

    id = db.Column(db.Integer, primary_key=1)
    product_id = db.Column(db.Integer, db.ForeignKey('supplier_products.id'), nullable=0)
    param_name = db.Column(db.String(50), nullable=0)  # 参数名称
    param_value = db.Column(db.String(100), nullable=0)  # 参数值
    param_unit = db.Column(db.String(20), nullable=1)  # 参数单位
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=0)

    # 关系
    product = db.relationship('SupplierProduct', backref='spec_parameters')

    def to_dict(self):
        return {
            'id': self.id,
            'product_id': self.product_id,
            'param_name': self.param_name,
            'param_value': self.param_value,
            'param_unit': self.param_unit,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S')
        }

# 送货验收表
class DeliveryInspection(db.Model):
    """送货验收表"""
    __tablename__ = 'delivery_inspections'

    id = db.Column(db.Integer, primary_key=1)
    delivery_id = db.Column(db.Integer, db.ForeignKey('supplier_deliveries.id'), nullable=0)
    inspector_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=0)
    inspection_date = db.Column(DATETIME2(precision=1), nullable=0)
    inspection_result = db.Column(db.String(20), nullable=0)  # 全部通过/部分通过/全部拒收
    notes = db.Column(db.Text, nullable=1)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=0)

    # 关系
    delivery = db.relationship('SupplierDelivery', backref='inspections')
    inspector = db.relationship('User')

    def to_dict(self):
        return {
            'id': self.id,
            'delivery_id': self.delivery_id,
            'inspector_id': self.inspector_id,
            'inspector_name': self.inspector.real_name or self.inspector.username,
            'inspection_date': self.inspection_date.strftime('%Y-%m-%d %H:%M:%S'),
            'inspection_result': self.inspection_result,
            'notes': self.notes,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S')
        }

# 送货项验收表
class DeliveryItemInspection(db.Model):
    """送货项验收表"""
    __tablename__ = 'delivery_item_inspections'

    id = db.Column(db.Integer, primary_key=1)
    inspection_id = db.Column(db.Integer, db.ForeignKey('delivery_inspections.id'), nullable=0)
    delivery_item_id = db.Column(db.Integer, db.ForeignKey('delivery_items.id'), nullable=0)
    accepted_quantity = db.Column(db.Float, nullable=0)  # 验收合格数量
    rejected_quantity = db.Column(db.Float, nullable=0)  # 拒收数量
    rejection_reason = db.Column(db.String(200), nullable=1)  # 拒收原因
    notes = db.Column(db.Text, nullable=1)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=0)

    # 关系
    inspection = db.relationship('DeliveryInspection', backref='item_inspections')
    delivery_item = db.relationship('DeliveryItem', backref='inspections')

    def to_dict(self):
        return {
            'id': self.id,
            'inspection_id': self.inspection_id,
            'delivery_item_id': self.delivery_item_id,
            'accepted_quantity': self.accepted_quantity,
            'rejected_quantity': self.rejected_quantity,
            'rejection_reason': self.rejection_reason,
            'notes': self.notes,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S')
        }

# 需要修改的现有模型字段
"""
SupplierProduct 模型需要添加以下字段:
- product_code = db.Column(db.String(50), nullable=1)  # 产品编码
- product_name = db.Column(db.String(100), nullable=1)  # 产品名称（可与食材名称不同）
- shelf_status = db.Column(db.Integer, default=0, nullable=0)  # 0-待审核, 1-已审核, 2-已拒绝
- shelf_time = db.Column(DATETIME2(precision=1), nullable=1)  # 上架时间
- shelf_operator_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=1)  # 上架操作人
- description = db.Column(db.Text, nullable=1)  # 产品描述

同时添加关系:
- shelf_operator = db.relationship('User', foreign_keys=[shelf_operator_id])
"""

import request from '@/utils/request'

const supplierschoolrelationAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/supplierschoolrelation',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/supplierschoolrelation/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/supplierschoolrelation',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/supplierschoolrelation/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/supplierschoolrelation/${id}`,
      method: 'delete'
    })
  }
}

export default supplierschoolrelationAPI
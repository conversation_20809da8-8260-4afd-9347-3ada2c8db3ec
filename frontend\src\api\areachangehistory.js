import request from '@/utils/request'

const areachangehistoryAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/areachangehistory',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/areachangehistory/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/areachangehistory',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/areachangehistory/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/areachangehistory/${id}`,
      method: 'delete'
    })
  }
}

export default areachangehistoryAPI
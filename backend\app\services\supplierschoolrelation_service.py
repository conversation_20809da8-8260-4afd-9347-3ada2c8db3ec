"""
SupplierSchoolRelation 服务层
"""

from typing import Optional, Dict, Any
from sqlalchemy.orm import Session
from app.models.models_supplier import SupplierSchoolRelation
from app.utils.database import get_db_session

class SupplierSchoolRelationService:
    """SupplierSchoolRelation 服务"""

    @staticmethod
    def get_list(page: int = 1, per_page: int = 10, **filters):
        """获取SupplierSchoolRelation列表"""
        with get_db_session() as session:
            query = session.query(SupplierSchoolRelation)

            # 应用过滤条件
            for key, value in filters.items():
                if hasattr(SupplierSchoolRelation, key) and value is not None:
                    query = query.filter(getattr(SupplierSchoolRelation, key) == value)

            return query.paginate(
                page=page,
                per_page=per_page,
                error_out=False
            )

    @staticmethod
    def get_by_id(id: int) -> Optional[SupplierSchoolRelation]:
        """根据ID获取SupplierSchoolRelation"""
        with get_db_session() as session:
            return session.query(SupplierSchoolRelation).filter(SupplierSchoolRelation.id == id).first()

    @staticmethod
    def create(data: Dict[str, Any]) -> SupplierSchoolRelation:
        """创建SupplierSchoolRelation"""
        with get_db_session() as session:
            item = SupplierSchoolRelation(**data)
            session.add(item)
            session.commit()
            session.refresh(item)
            return item

    @staticmethod
    def update(id: int, data: Dict[str, Any]) -> Optional[SupplierSchoolRelation]:
        """更新SupplierSchoolRelation"""
        with get_db_session() as session:
            item = session.query(SupplierSchoolRelation).filter(SupplierSchoolRelation.id == id).first()
            if not item:
                return None

            for key, value in data.items():
                if hasattr(item, key):
                    setattr(item, key, value)

            session.commit()
            session.refresh(item)
            return item

    @staticmethod
    def delete(id: int) -> bool:
        """删除SupplierSchoolRelation"""
        with get_db_session() as session:
            item = session.query(SupplierSchoolRelation).filter(SupplierSchoolRelation.id == id).first()
            if not item:
                return False

            session.delete(item)
            session.commit()
            return True
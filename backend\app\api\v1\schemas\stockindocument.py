"""
StockInDocument 序列化模式
"""

from marshmallow import Schema, fields, validate

class StockInDocumentSchema(Schema):
    """StockInDocument 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    stock_in_id = fields.Integer(required=True)
    
    
    
    document_type = fields.String(required=True)
    
    
    
    file_path = fields.String(required=True)
    
    
    
    supplier_id = fields.Integer()
    
    
    
    notes = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
"""
StockInItem 序列化模式
"""

from marshmallow import Schema, fields, validate

class StockInItemSchema(Schema):
    """StockInItem 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    stock_in_id = fields.Integer(required=True)
    
    
    
    ingredient_id = fields.Integer(required=True)
    
    
    
    batch_number = fields.String(required=True)
    
    
    
    quantity = fields.Float(required=True)
    
    
    
    unit = fields.String(required=True)
    
    
    
    production_date = fields.Date(required=True)
    
    
    
    expiry_date = fields.Date(required=True)
    
    
    
    storage_location_id = fields.Integer(required=True)
    
    
    
    supplier_id = fields.Integer()
    
    
    
    quality_check_result = fields.String()
    
    
    
    quality_check_notes = fields.String()
    
    
    
    notes = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
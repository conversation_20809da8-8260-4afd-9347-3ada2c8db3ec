import request from '@/utils/request'

const videoguideAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/videoguide',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/videoguide/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/videoguide',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/videoguide/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/videoguide/${id}`,
      method: 'delete'
    })
  }
}

export default videoguideAPI
import request from '@/utils/request'

const recipereviewtagAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/recipereviewtag',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/recipereviewtag/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/recipereviewtag',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/recipereviewtag/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/recipereviewtag/${id}`,
      method: 'delete'
    })
  }
}

export default recipereviewtagAPI
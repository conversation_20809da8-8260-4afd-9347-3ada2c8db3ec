import request from '@/utils/request'

const recipeimprovementsuggestionAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/recipeimprovementsuggestion',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/recipeimprovementsuggestion/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/recipeimprovementsuggestion',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/recipeimprovementsuggestion/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/recipeimprovementsuggestion/${id}`,
      method: 'delete'
    })
  }
}

export default recipeimprovementsuggestionAPI
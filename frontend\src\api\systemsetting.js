import request from '@/utils/request'

const systemsettingAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/systemsetting',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/systemsetting/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/systemsetting',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/systemsetting/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/systemsetting/${id}`,
      method: 'delete'
    })
  }
}

export default systemsettingAPI
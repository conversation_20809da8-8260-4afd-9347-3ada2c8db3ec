#!/usr/bin/env python3
"""
灵活的数据库连接测试脚本
支持多种连接方式
"""

import sys
import os
import urllib.parse
from sqlalchemy import create_engine, text

def test_connection_method_1():
    """方法1: 远程 SQL Server 连接"""
    print("🔍 方法1: 测试远程 SQL Server 连接...")

    # 数据库配置
    DB_SERVER = '14.103.246.164'
    DB_PORT = '1433'
    DB_DATABASE = 'StudentsCMSSP'
    DB_USERNAME = 'StudentsCMSSP'
    DB_PASSWORD = 'Xg2LS44Cyz5Zt8.'
    DB_DRIVER = 'ODBC Driver 17 for SQL Server'

    # 构建连接字符串
    password_encoded = urllib.parse.quote_plus(DB_PASSWORD)
    driver_encoded = urllib.parse.quote_plus(DB_DRIVER)

    connection_string = f"mssql+pyodbc://{DB_USERNAME}:{password_encoded}@{DB_SERVER}:{DB_PORT}/{DB_DATABASE}?driver={driver_encoded}&TrustServerCertificate=yes"

    return test_connection(connection_string, "远程 SQL Server")

def test_connection_method_2():
    """方法2: 本地 SQL Server Express 连接"""
    print("🔍 方法2: 测试本地 SQL Server Express 连接...")

    # 使用原始项目的连接方式
    conn_str = "DRIVER={SQL Server};SERVER=(local)\\SQLEXPRESS;DATABASE=StudentsCMSSP;Trusted_Connection=yes"
    quoted_conn_str = urllib.parse.quote_plus(conn_str)
    connection_string = f"mssql+pyodbc:///?odbc_connect={quoted_conn_str}"

    return test_connection(connection_string, "本地 SQL Server Express")

def test_connection_method_3():
    """方法3: 本地 SQL Server 默认实例"""
    print("🔍 方法3: 测试本地 SQL Server 默认实例...")

    conn_str = "DRIVER={SQL Server};SERVER=(local);DATABASE=StudentsCMSSP;Trusted_Connection=yes"
    quoted_conn_str = urllib.parse.quote_plus(conn_str)
    connection_string = f"mssql+pyodbc:///?odbc_connect={quoted_conn_str}"

    return test_connection(connection_string, "本地 SQL Server 默认实例")

def test_connection_method_4():
    """方法4: 远程 SQL Server 使用 Windows 认证"""
    print("🔍 方法4: 测试远程 SQL Server (Windows 认证)...")

    conn_str = "DRIVER={ODBC Driver 17 for SQL Server};SERVER=14.103.246.164;DATABASE=StudentsCMSSP;Trusted_Connection=yes"
    quoted_conn_str = urllib.parse.quote_plus(conn_str)
    connection_string = f"mssql+pyodbc:///?odbc_connect={quoted_conn_str}"

    return test_connection(connection_string, "远程 SQL Server (Windows 认证)")

def test_connection(connection_string, method_name):
    """测试数据库连接"""
    try:
        print(f"📋 连接方式: {method_name}")
        print(f"🔗 连接字符串: {connection_string[:50]}...")

        # 创建引擎
        engine = create_engine(
            connection_string,
            echo=False,
            pool_pre_ping=True,
            pool_recycle=300,
            connect_args={'timeout': 10}
        )

        # 测试连接
        with engine.connect() as connection:
            result = connection.execute(text("SELECT 1 as test"))
            row = result.fetchone()

            if row and row[0] == 1:
                print("✅ 连接成功！")

                # 测试查询数据库信息
                try:
                    result = connection.execute(text("SELECT @@VERSION as version"))
                    version = result.fetchone()
                    if version:
                        print(f"📋 SQL Server 版本: {version[0][:50]}...")

                    # 测试查询表数量
                    result = connection.execute(text("""
                        SELECT COUNT(*) as table_count
                        FROM INFORMATION_SCHEMA.TABLES
                        WHERE TABLE_TYPE = 'BASE TABLE'
                    """))
                    table_count = result.fetchone()
                    if table_count:
                        print(f"📊 数据库表数量: {table_count[0]}")
                except Exception as e:
                    print(f"⚠️ 查询数据库信息时出错: {e}")

                return True
            else:
                print("❌ 连接测试失败")
                return False

    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 数据库连接测试工具")
    print("=" * 60)

    methods = [
        test_connection_method_1,
        test_connection_method_2,
        test_connection_method_3,
        test_connection_method_4
    ]

    success_count = 0

    for i, method in enumerate(methods, 1):
        print(f"\n{'='*20} 测试 {i}/4 {'='*20}")
        try:
            if method():
                success_count += 1
                print(f"✅ 方法 {i} 成功")
                break  # 找到一个成功的连接就停止
            else:
                print(f"❌ 方法 {i} 失败")
        except Exception as e:
            print(f"❌ 方法 {i} 异常: {e}")

        print()

    print("=" * 60)
    if success_count > 0:
        print(f"🎉 找到 {success_count} 个可用的连接方式")
        return True
    else:
        print("😞 所有连接方式都失败了")
        print("\n🛠️ 建议检查:")
        print("1. SQL Server 服务是否正在运行")
        print("2. 网络连接是否正常")
        print("3. 防火墙设置")
        print("4. SQL Server 配置是否允许远程连接")
        print("5. ODBC 驱动是否正确安装")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)

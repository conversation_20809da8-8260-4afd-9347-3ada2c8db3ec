import request from '@/utils/request'

const trainingrecordAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/trainingrecord',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/trainingrecord/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/trainingrecord',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/trainingrecord/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/trainingrecord/${id}`,
      method: 'delete'
    })
  }
}

export default trainingrecordAPI
import request from '@/utils/request'

const tracedocumentAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/tracedocument',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/tracedocument/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/tracedocument',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/tracedocument/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/tracedocument/${id}`,
      method: 'delete'
    })
  }
}

export default tracedocumentAPI
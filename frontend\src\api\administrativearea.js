import request from '@/utils/request'

const administrativeareaAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/administrativearea',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/administrativearea/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/administrativearea',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/administrativearea/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/administrativearea/${id}`,
      method: 'delete'
    })
  }
}

export default administrativeareaAPI
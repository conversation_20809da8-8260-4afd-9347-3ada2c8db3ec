import request from '@/utils/request'

const standardunitAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/standardunit',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/standardunit/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/standardunit',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/standardunit/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/standardunit/${id}`,
      method: 'delete'
    })
  }
}

export default standardunitAPI
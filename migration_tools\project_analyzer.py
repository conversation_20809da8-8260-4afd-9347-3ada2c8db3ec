#!/usr/bin/env python3
"""
学校食堂管理系统项目分析工具
分析当前项目结构、依赖关系、数据库设计等信息
"""

import os
import json
import ast
import re
from pathlib import Path
from typing import Dict, List, Set, Any
from collections import defaultdict

class ProjectAnalyzer:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.analysis_result = {
            'project_info': {},
            'dependencies': {},
            'blueprints': [],
            'models': [],
            'routes': [],
            'templates': [],
            'static_files': [],
            'database_tables': [],
            'api_endpoints': [],
            'migration_complexity': {}
        }

    def analyze(self) -> Dict[str, Any]:
        """执行完整的项目分析"""
        print("🔍 开始分析项目结构...")

        self._analyze_project_info()
        self._analyze_dependencies()
        self._analyze_blueprints()
        self._analyze_models()
        self._analyze_routes()
        self._analyze_templates()
        self._analyze_static_files()
        self._analyze_api_endpoints()
        self._calculate_migration_complexity()

        print("✅ 项目分析完成")
        return self.analysis_result

    def _analyze_project_info(self):
        """分析项目基本信息"""
        print("📋 分析项目基本信息...")

        # 读取配置文件
        config_file = self.project_root / 'config.py'
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()
                # 提取数据库配置
                if 'SQLALCHEMY_DATABASE_URI' in content:
                    self.analysis_result['project_info']['database'] = 'SQL Server'
                if 'REDIS_HOST' in content:
                    self.analysis_result['project_info']['cache'] = 'Redis'

        # 读取requirements.txt
        req_file = self.project_root / 'requirements.txt'
        if req_file.exists():
            with open(req_file, 'r', encoding='utf-8') as f:
                requirements = [line.strip() for line in f if line.strip() and not line.startswith('#')]
                self.analysis_result['project_info']['total_dependencies'] = len(requirements)

        # 统计代码行数
        total_lines = 0
        python_files = 0
        for py_file in self.project_root.rglob('*.py'):
            if '__pycache__' not in str(py_file):
                python_files += 1
                try:
                    with open(py_file, 'r', encoding='utf-8') as f:
                        total_lines += len(f.readlines())
                except:
                    pass

        self.analysis_result['project_info'].update({
            'total_python_files': python_files,
            'total_code_lines': total_lines,
            'framework': 'Flask',
            'frontend': 'Bootstrap 5.3.6 + jQuery'
        })

    def _analyze_dependencies(self):
        """分析项目依赖"""
        print("📦 分析项目依赖...")

        req_file = self.project_root / 'requirements.txt'
        if not req_file.exists():
            return

        with open(req_file, 'r', encoding='utf-8') as f:
            requirements = [line.strip() for line in f if line.strip() and not line.startswith('#')]

        # 分类依赖
        categories = {
            'web_framework': ['Flask', 'flask-'],
            'database': ['SQLAlchemy', 'pyodbc', 'alembic'],
            'frontend': ['bootstrap', 'jquery'],
            'auth': ['Flask-Login', 'Flask-WTF'],
            'api': ['Flask-RESTful', 'Flask-Cors'],
            'utils': ['requests', 'pillow', 'qrcode', 'reportlab'],
            'data': ['pandas', 'numpy', 'openpyxl']
        }

        categorized = defaultdict(list)
        for req in requirements:
            package_name = req.split('==')[0].lower()
            categorized_flag = False

            for category, keywords in categories.items():
                if any(keyword.lower() in package_name for keyword in keywords):
                    categorized[category].append(req)
                    categorized_flag = True
                    break

            if not categorized_flag:
                categorized['others'].append(req)

        self.analysis_result['dependencies'] = dict(categorized)

    def _analyze_blueprints(self):
        """分析Flask蓝图"""
        print("🗺️ 分析Flask蓝图...")

        # 从__init__.py中提取蓝图注册信息
        init_file = self.project_root / 'app' / '__init__.py'
        if init_file.exists():
            with open(init_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 使用正则表达式提取蓝图注册
            blueprint_pattern = r'app\.register_blueprint\(([^,]+)(?:,\s*url_prefix=[\'"]([^\'"]*)[\'"])?\)'
            matches = re.findall(blueprint_pattern, content)

            for match in matches:
                blueprint_name = match[0].strip()
                url_prefix = match[1] if match[1] else '/'
                self.analysis_result['blueprints'].append({
                    'name': blueprint_name,
                    'url_prefix': url_prefix
                })

    def _analyze_models(self):
        """分析数据模型"""
        print("🗃️ 分析数据模型...")

        model_files = list(self.project_root.glob('app/models*.py'))

        for model_file in model_files:
            try:
                with open(model_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 使用AST解析Python文件
                tree = ast.parse(content)

                for node in ast.walk(tree):
                    if isinstance(node, ast.ClassDef):
                        # 检查是否继承自db.Model
                        for base in node.bases:
                            if (isinstance(base, ast.Attribute) and
                                isinstance(base.value, ast.Name) and
                                base.value.id == 'db' and base.attr == 'Model'):

                                self.analysis_result['models'].append({
                                    'name': node.name,
                                    'file': model_file.name,
                                    'fields': self._extract_model_fields(node)
                                })
                                break
            except Exception as e:
                print(f"⚠️ 解析模型文件 {model_file} 时出错: {e}")

    def _extract_model_fields(self, class_node) -> List[str]:
        """提取模型字段"""
        fields = []
        for node in class_node.body:
            if isinstance(node, ast.Assign):
                for target in node.targets:
                    if isinstance(target, ast.Name):
                        # 检查是否是db.Column
                        if (isinstance(node.value, ast.Call) and
                            isinstance(node.value.func, ast.Attribute) and
                            isinstance(node.value.func.value, ast.Name) and
                            node.value.func.value.id == 'db' and
                            node.value.func.attr == 'Column'):
                            fields.append(target.id)
        return fields

    def _analyze_routes(self):
        """分析路由"""
        print("🛣️ 分析路由...")

        route_files = list(self.project_root.rglob('app/routes/*.py')) + \
                     list(self.project_root.rglob('app/*/routes.py'))

        for route_file in route_files:
            try:
                with open(route_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 提取路由装饰器
                route_pattern = r'@[^.]*\.route\([\'"]([^\'"]*)[\'"](?:,\s*methods=\[([^\]]*)\])?\)'
                matches = re.findall(route_pattern, content)

                for match in matches:
                    route_path = match[0]
                    methods = match[1].replace("'", "").replace('"', '').split(', ') if match[1] else ['GET']

                    self.analysis_result['routes'].append({
                        'path': route_path,
                        'methods': methods,
                        'file': route_file.name
                    })
            except Exception as e:
                print(f"⚠️ 解析路由文件 {route_file} 时出错: {e}")

    def _analyze_templates(self):
        """分析模板文件"""
        print("📄 分析模板文件...")

        template_dir = self.project_root / 'app' / 'templates'
        if template_dir.exists():
            for template_file in template_dir.rglob('*.html'):
                self.analysis_result['templates'].append({
                    'name': template_file.name,
                    'path': str(template_file.relative_to(template_dir)),
                    'size': template_file.stat().st_size
                })

    def _analyze_static_files(self):
        """分析静态文件"""
        print("📁 分析静态文件...")

        static_dir = self.project_root / 'app' / 'static'
        if static_dir.exists():
            for static_file in static_dir.rglob('*'):
                if static_file.is_file():
                    self.analysis_result['static_files'].append({
                        'name': static_file.name,
                        'path': str(static_file.relative_to(static_dir)),
                        'size': static_file.stat().st_size,
                        'type': static_file.suffix
                    })

    def _analyze_api_endpoints(self):
        """分析API端点"""
        print("🔌 分析API端点...")

        api_files = list(self.project_root.rglob('app/api/*.py')) + \
                   list(self.project_root.rglob('app/routes/*api*.py'))

        for api_file in api_files:
            try:
                with open(api_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 提取API路由
                api_pattern = r'@[^.]*\.route\([\'"]([^\'"]*)[\'"](?:,\s*methods=\[([^\]]*)\])?\)'
                matches = re.findall(api_pattern, content)

                for match in matches:
                    if '/api' in match[0] or 'api' in api_file.name:
                        self.analysis_result['api_endpoints'].append({
                            'path': match[0],
                            'methods': match[1].replace("'", "").replace('"', '').split(', ') if match[1] else ['GET'],
                            'file': api_file.name
                        })
            except Exception as e:
                print(f"⚠️ 解析API文件 {api_file} 时出错: {e}")

    def _calculate_migration_complexity(self):
        """计算迁移复杂度"""
        print("📊 计算迁移复杂度...")

        # 基于各种指标计算复杂度
        complexity = {
            'models_count': len(self.analysis_result['models']),
            'routes_count': len(self.analysis_result['routes']),
            'templates_count': len(self.analysis_result['templates']),
            'api_endpoints_count': len(self.analysis_result['api_endpoints']),
            'blueprints_count': len(self.analysis_result['blueprints'])
        }

        # 计算总体复杂度评分 (1-10)
        total_score = (
            min(complexity['models_count'] / 10, 3) +
            min(complexity['routes_count'] / 50, 3) +
            min(complexity['templates_count'] / 30, 2) +
            min(complexity['api_endpoints_count'] / 20, 2)
        )

        complexity['overall_score'] = round(total_score, 1)
        complexity['difficulty_level'] = (
            '简单' if total_score < 3 else
            '中等' if total_score < 6 else
            '复杂' if total_score < 8 else
            '非常复杂'
        )

        self.analysis_result['migration_complexity'] = complexity

    def save_report(self, output_file: str = 'migration_analysis_report.json'):
        """保存分析报告"""
        output_path = self.project_root / output_file
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(self.analysis_result, f, ensure_ascii=False, indent=2)
        print(f"📄 分析报告已保存到: {output_path}")

    def print_summary(self):
        """打印分析摘要"""
        print("\n" + "="*60)
        print("📊 项目分析摘要")
        print("="*60)

        info = self.analysis_result['project_info']
        print(f"🏗️ 框架: {info.get('framework', 'Unknown')}")
        print(f"🎨 前端: {info.get('frontend', 'Unknown')}")
        print(f"🗄️ 数据库: {info.get('database', 'Unknown')}")
        print(f"📦 依赖数量: {info.get('total_dependencies', 0)}")
        print(f"📝 Python文件: {info.get('total_python_files', 0)}")
        print(f"📏 代码行数: {info.get('total_code_lines', 0):,}")

        complexity = self.analysis_result['migration_complexity']
        print(f"\n🎯 迁移复杂度: {complexity['difficulty_level']} ({complexity['overall_score']}/10)")
        print(f"📋 数据模型: {complexity['models_count']}")
        print(f"🛣️ 路由数量: {complexity['routes_count']}")
        print(f"📄 模板数量: {complexity['templates_count']}")
        print(f"🔌 API端点: {complexity['api_endpoints_count']}")
        print(f"🗺️ 蓝图数量: {complexity['blueprints_count']}")

        print("\n" + "="*60)

if __name__ == '__main__':
    # 分析当前项目
    analyzer = ProjectAnalyzer('.')
    result = analyzer.analyze()
    analyzer.print_summary()
    analyzer.save_report()

#!/usr/bin/env python3
"""
简化的应用测试脚本
不依赖生成的 API 代码，直接测试配置
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from flask import Flask
from config import config

def test_config():
    """测试配置"""
    print("🔍 测试配置...")
    print("=" * 50)

    # 创建应用
    app = Flask(__name__)

    # 加载配置
    config_class = config.get('development', config['default'])
    config_instance = config_class()
    app.config.from_object(config_instance)

    print(f"✅ 配置加载成功")
    print(f"📊 数据库URI: {app.config['SQLALCHEMY_DATABASE_URI'][:50]}...")
    print(f"🔧 使用Windows认证: {config_instance.USE_WINDOWS_AUTH}")
    print(f"🗄️ 数据库: {config_instance.DB_DATABASE}")
    print(f"🖥️ 服务器: {config_instance.DB_SERVER}")

    return True

def test_direct_connection():
    """直接测试数据库连接"""
    print("\n🔍 测试数据库连接...")
    print("=" * 50)

    import urllib.parse
    from sqlalchemy import create_engine, text

    # 使用与配置相同的连接字符串
    conn_str = "DRIVER={SQL Server};SERVER=(local)\\SQLEXPRESS;DATABASE=StudentsCMSSP;Trusted_Connection=yes"
    quoted_conn_str = urllib.parse.quote_plus(conn_str)
    connection_string = f"mssql+pyodbc:///?odbc_connect={quoted_conn_str}"

    try:
        # 创建引擎
        engine = create_engine(
            connection_string,
            echo=False,
            pool_pre_ping=True,
            pool_recycle=300
        )

        # 测试连接
        with engine.connect() as connection:
            result = connection.execute(text("SELECT 1 as test"))
            row = result.fetchone()

            if row and row[0] == 1:
                print("✅ 数据库连接成功！")

                # 查询数据库信息
                result = connection.execute(text("SELECT @@VERSION as version"))
                version = result.fetchone()
                if version:
                    print(f"📋 SQL Server 版本: {version[0][:50]}...")

                # 查询表数量
                result = connection.execute(text("""
                    SELECT COUNT(*) as table_count
                    FROM INFORMATION_SCHEMA.TABLES
                    WHERE TABLE_TYPE = 'BASE TABLE'
                """))
                table_count = result.fetchone()
                if table_count:
                    print(f"📊 数据库表数量: {table_count[0]}")

                return True
            else:
                print("❌ 连接测试失败")
                return False

    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 简化应用测试")
    print("=" * 60)

    try:
        # 测试配置
        if not test_config():
            return False

        # 测试数据库连接
        if not test_direct_connection():
            return False

        print("\n" + "=" * 60)
        print("🎉 所有测试通过！")
        print("✅ 配置正确")
        print("✅ 数据库连接正常")
        print("🚀 可以开始启动 API 服务器")

        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)

import request from '@/utils/request'

const consumptiondetailAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/consumptiondetail',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/consumptiondetail/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/consumptiondetail',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/consumptiondetail/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/consumptiondetail/${id}`,
      method: 'delete'
    })
  }
}

export default consumptiondetailAPI
import request from '@/utils/request'

const recipetagAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/recipetag',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/recipetag/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/recipetag',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/recipetag/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/recipetag/${id}`,
      method: 'delete'
    })
  }
}

export default recipetagAPI
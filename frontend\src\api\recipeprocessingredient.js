import request from '@/utils/request'

const recipeprocessingredientAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/recipeprocessingredient',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/recipeprocessingredient/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/recipeprocessingredient',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/recipeprocessingredient/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/recipeprocessingredient/${id}`,
      method: 'delete'
    })
  }
}

export default recipeprocessingredientAPI
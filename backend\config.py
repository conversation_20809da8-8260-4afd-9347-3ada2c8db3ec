"""
应用配置
"""

import os
from datetime import timedelta
import urllib.parse

class Config:
    """基础配置"""
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key'

    # SQL Server 数据库配置
    DB_SERVER = os.environ.get('DB_SERVER') or '14.103.246.164'
    DB_DATABASE = os.environ.get('DB_DATABASE') or 'StudentsCMSSP'
    DB_USERNAME = os.environ.get('DB_USERNAME') or 'StudentsCMSSP'
    DB_PASSWORD = os.environ.get('DB_PASSWORD') or 'Xg2LS44Cyz5Zt8.'
    DB_DRIVER = os.environ.get('DB_DRIVER') or 'ODBC Driver 17 for SQL Server'

    # 构建数据库连接字符串
    @property
    def SQLALCHEMY_DATABASE_URI(self):
        # URL编码密码以处理特殊字符
        password_encoded = urllib.parse.quote_plus(self.DB_PASSWORD)
        driver_encoded = urllib.parse.quote_plus(self.DB_DRIVER)

        return f"mssql+pyodbc://{self.DB_USERNAME}:{password_encoded}@{self.DB_SERVER}/{self.DB_DATABASE}?driver={driver_encoded}&TrustServerCertificate=yes"

    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
        'connect_args': {
            'timeout': 20,
            'check_same_thread': False
        }
    }

    # JWT 配置
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY') or 'jwt-secret-key'
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=24)
    JWT_REFRESH_TOKEN_EXPIRES = timedelta(days=30)

    # CORS 配置
    CORS_ORIGINS = ['http://localhost:3000', 'http://localhost:8080', 'http://localhost:5001']

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    SQLALCHEMY_ECHO = False  # 设置为 True 可以看到 SQL 查询日志

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    SQLALCHEMY_ECHO = False

class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    DEBUG = True
    # 测试环境可以使用内存数据库
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'

config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
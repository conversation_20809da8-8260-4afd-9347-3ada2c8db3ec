<template>
  <div class="page-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>新增SupplierCategory</span>
          <el-button @click="handleBack">返回</el-button>
        </div>
      </template>

      <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
        
        
        <el-form-item label="" prop="">
          
          <el-input v-model="form." placeholder="请输入" />
          
        </el-form-item>
        
        
        
        <el-form-item label="" prop="">
          
          <el-input v-model="form." placeholder="请输入" />
          
        </el-form-item>
        
        
        
        <el-form-item label="" prop="">
          
          <el-input v-model="form." placeholder="请输入" />
          
        </el-form-item>
        
        
        
        <el-form-item label="" prop="">
          
          <el-input v-model="form." placeholder="请输入" />
          
        </el-form-item>
        
        

        <el-form-item>
          <el-button type="primary" @click="handleSubmit" :loading="loading">
            创建
          </el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useSupplierCategoryStore } from '@/stores/suppliercategory'

const route = useRoute()
const router = useRouter()
const suppliercategoryStore = useSupplierCategoryStore()

// 响应式数据
const loading = ref(false)
const formRef = ref()
const isEdit = computed(() => !!route.params.id)

const form = reactive({
  
  
  : '',
  
  
  
  : '',
  
  
  
  : '',
  
  
  
  : '',
  
  
})

const rules = reactive({
  
  
  : [{ required: true, message: '请输入', trigger: 'blur' }],
  
  
  
  : [{ required: true, message: '请输入', trigger: 'blur' }],
  
  
  
  : [{ required: true, message: '请输入', trigger: 'blur' }],
  
  
  
  : [{ required: true, message: '请输入', trigger: 'blur' }],
  
  
})

// 方法
const fetchData = async () => {
  if (!isEdit.value) return

  try {
    const data = await suppliercategoryStore.fetchById(route.params.id)
    Object.assign(form, data)
  } catch (error) {
    ElMessage.error('获取数据失败')
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    loading.value = true

    if (isEdit.value) {
      await suppliercategoryStore.update(route.params.id, form)
      ElMessage.success('更新成功')
    } else {
      await suppliercategoryStore.create(form)
      ElMessage.success('创建成功')
    }

    handleBack()
  } catch (error) {
    ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
  } finally {
    loading.value = false
  }
}

const handleReset = () => {
  formRef.value.resetFields()
}

const handleBack = () => {
  router.push({ name: 'SupplierCategoryList' })
}

// 生命周期
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
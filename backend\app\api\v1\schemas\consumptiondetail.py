"""
ConsumptionDetail 序列化模式
"""

from marshmallow import Schema, fields, validate

class ConsumptionDetailSchema(Schema):
    """ConsumptionDetail 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    consumption_plan_id = fields.Integer(required=True)
    
    
    
    ingredient_id = fields.Integer(required=True)
    
    
    
    planned_quantity = fields.Float(required=True)
    
    
    
    actual_quantity = fields.Float()
    
    
    
    unit = fields.String(required=True)
    
    
    
    status = fields.String(required=True)
    
    
    
    is_main_ingredient = fields.Boolean(required=True)
    
    
    
    notes = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
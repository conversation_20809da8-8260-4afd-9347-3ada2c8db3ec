"""
DatabaseBackup 序列化模式
"""

from marshmallow import Schema, fields, validate

class DatabaseBackupSchema(Schema):
    """DatabaseBackup 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    filename = fields.String(required=True)
    
    
    
    backup_type = fields.String(required=True)
    
    
    
    size = fields.Integer()
    
    
    
    description = fields.String()
    
    
    
    status = fields.String(required=True)
    
    
    
    created_at = fields.String(required=True)
    
    
    
    created_by = fields.Integer()
    
    

    class Meta:
        ordered = True
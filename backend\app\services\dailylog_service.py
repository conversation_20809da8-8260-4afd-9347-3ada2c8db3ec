"""
DailyLog 服务层
"""

from typing import Optional, Dict, Any
from sqlalchemy.orm import Session
from app.models.models_daily_management import DailyLog
from app.utils.database import get_db_session

class DailyLogService:
    """DailyLog 服务"""

    @staticmethod
    def get_list(page: int = 1, per_page: int = 10, **filters):
        """获取DailyLog列表"""
        with get_db_session() as session:
            query = session.query(DailyLog)

            # 应用过滤条件
            for key, value in filters.items():
                if hasattr(DailyLog, key) and value is not None:
                    query = query.filter(getattr(DailyLog, key) == value)

            return query.paginate(
                page=page,
                per_page=per_page,
                error_out=False
            )

    @staticmethod
    def get_by_id(id: int) -> Optional[DailyLog]:
        """根据ID获取DailyLog"""
        with get_db_session() as session:
            return session.query(DailyLog).filter(DailyLog.id == id).first()

    @staticmethod
    def create(data: Dict[str, Any]) -> DailyLog:
        """创建DailyLog"""
        with get_db_session() as session:
            item = DailyLog(**data)
            session.add(item)
            session.commit()
            session.refresh(item)
            return item

    @staticmethod
    def update(id: int, data: Dict[str, Any]) -> Optional[DailyLog]:
        """更新DailyLog"""
        with get_db_session() as session:
            item = session.query(DailyLog).filter(DailyLog.id == id).first()
            if not item:
                return None

            for key, value in data.items():
                if hasattr(item, key):
                    setattr(item, key, value)

            session.commit()
            session.refresh(item)
            return item

    @staticmethod
    def delete(id: int) -> bool:
        """删除DailyLog"""
        with get_db_session() as session:
            item = session.query(DailyLog).filter(DailyLog.id == id).first()
            if not item:
                return False

            session.delete(item)
            session.commit()
            return True
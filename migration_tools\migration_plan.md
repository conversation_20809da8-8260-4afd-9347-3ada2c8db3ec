# 学校食堂管理系统迁移计划

## 项目现状分析

### 系统规模
- **框架**: Flask 3.0.3
- **前端**: Bootstrap 5.3.6 + jQuery
- **数据库**: SQL Server + Redis
- **代码规模**: 5,574 个 Python 文件，213万+ 行代码
- **依赖数量**: 92 个 Python 包
- **迁移复杂度**: 非常复杂 (10/10)

### 核心组件统计
- **数据模型**: 107 个
- **路由数量**: 444 个
- **模板文件**: 350 个
- **API端点**: 41 个
- **蓝图模块**: 24 个

### 主要功能模块
1. **用户认证与权限管理**
2. **供应商管理系统**
3. **食材与配方管理**
4. **采购订单管理**
5. **库存管理系统**
6. **食材溯源系统**
7. **食堂日常管理**
8. **财务管理系统**
9. **周菜单管理**
10. **数据统计分析**

## 迁移策略

### 1. 渐进式迁移方案
采用并存渐进式迁移，确保业务连续性：

```
现有系统 (Flask)     新系统 (Vue 3 + Flask API)
     ↓                        ↓
  localhost:8080         localhost:8080/new/
     ↓                        ↓
逐步迁移功能模块 → 最终完全切换到新系统
```

### 2. 技术栈选择

#### 前端技术栈
- **框架**: Vue 3.4+
- **UI库**: Element Plus 2.4+
- **路由**: Vue Router 4.2+
- **状态管理**: Pinia 2.1+
- **HTTP客户端**: Axios 1.6+
- **构建工具**: Vite 5.0+
- **TypeScript**: 5.0+

#### 后端技术栈
- **API框架**: Flask 3.0.3 + Flask-RESTful
- **认证**: Flask-JWT-Extended
- **数据库**: 保持现有 SQL Server
- **缓存**: 保持现有 Redis
- **文档**: Flask-RESTX (Swagger)

#### 部署架构
```
Nginx (反向代理)
├── /new/ → Vue 3 前端 (静态文件)
├── /api/v1/ → Flask API 后端
└── / → 现有 Flask 系统 (逐步迁移)
```

## 详细迁移计划

### 阶段一：基础架构搭建 (2-3周)

#### 1.1 前端项目初始化
```bash
# 创建 Vue 3 项目
npm create vue@latest frontend
cd frontend
npm install element-plus axios pinia vue-router @types/node
```

#### 1.2 后端 API 项目结构
```
backend/
├── app/
│   ├── api/
│   │   ├── v1/
│   │   │   ├── auth/
│   │   │   ├── users/
│   │   │   ├── suppliers/
│   │   │   ├── ingredients/
│   │   │   ├── recipes/
│   │   │   └── ...
│   │   └── __init__.py
│   ├── models/
│   ├── services/
│   ├── utils/
│   └── __init__.py
├── config.py
├── requirements.txt
└── run.py
```

#### 1.3 Nginx 配置
```nginx
server {
    listen 80;
    server_name localhost;

    # 新系统前端
    location /new/ {
        alias /path/to/frontend/dist/;
        try_files $uri $uri/ /new/index.html;
    }

    # 新系统 API
    location /api/v1/ {
        proxy_pass http://localhost:5001/api/v1/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    # 现有系统
    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 阶段二：核心模块迁移 (8-10周)

#### 2.1 用户认证模块 (2周)
**优先级**: 最高
**复杂度**: 中等

**后端 API 开发**:
- JWT 认证实现
- 用户登录/注册 API
- 权限验证中间件
- 角色管理 API

**前端组件开发**:
- 登录页面组件
- 用户信息组件
- 权限控制指令
- 路由守卫

**数据迁移**:
- 用户表数据同步
- 角色权限数据迁移

#### 2.2 供应商管理模块 (2周)
**优先级**: 高
**复杂度**: 中等

**API 端点**:
```
GET    /api/v1/suppliers          # 获取供应商列表
POST   /api/v1/suppliers          # 创建供应商
GET    /api/v1/suppliers/{id}     # 获取供应商详情
PUT    /api/v1/suppliers/{id}     # 更新供应商
DELETE /api/v1/suppliers/{id}     # 删除供应商
```

**前端页面**:
- 供应商列表页
- 供应商详情页
- 供应商编辑表单
- 供应商产品管理

"""
PurchaseRequisitionItem 服务层
"""

from typing import Optional, Dict, Any
from sqlalchemy.orm import Session
from app.models.models_phase3 import PurchaseRequisitionItem
from app.utils.database import get_db_session

class PurchaseRequisitionItemService:
    """PurchaseRequisitionItem 服务"""

    @staticmethod
    def get_list(page: int = 1, per_page: int = 10, **filters):
        """获取PurchaseRequisitionItem列表"""
        with get_db_session() as session:
            query = session.query(PurchaseRequisitionItem)

            # 应用过滤条件
            for key, value in filters.items():
                if hasattr(PurchaseRequisitionItem, key) and value is not None:
                    query = query.filter(getattr(PurchaseRequisitionItem, key) == value)

            return query.paginate(
                page=page,
                per_page=per_page,
                error_out=False
            )

    @staticmethod
    def get_by_id(id: int) -> Optional[PurchaseRequisitionItem]:
        """根据ID获取PurchaseRequisitionItem"""
        with get_db_session() as session:
            return session.query(PurchaseRequisitionItem).filter(PurchaseRequisitionItem.id == id).first()

    @staticmethod
    def create(data: Dict[str, Any]) -> PurchaseRequisitionItem:
        """创建PurchaseRequisitionItem"""
        with get_db_session() as session:
            item = PurchaseRequisitionItem(**data)
            session.add(item)
            session.commit()
            session.refresh(item)
            return item

    @staticmethod
    def update(id: int, data: Dict[str, Any]) -> Optional[PurchaseRequisitionItem]:
        """更新PurchaseRequisitionItem"""
        with get_db_session() as session:
            item = session.query(PurchaseRequisitionItem).filter(PurchaseRequisitionItem.id == id).first()
            if not item:
                return None

            for key, value in data.items():
                if hasattr(item, key):
                    setattr(item, key, value)

            session.commit()
            session.refresh(item)
            return item

    @staticmethod
    def delete(id: int) -> bool:
        """删除PurchaseRequisitionItem"""
        with get_db_session() as session:
            item = session.query(PurchaseRequisitionItem).filter(PurchaseRequisitionItem.id == id).first()
            if not item:
                return False

            session.delete(item)
            session.commit()
            return True
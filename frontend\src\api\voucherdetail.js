import request from '@/utils/request'

const voucherdetailAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/voucherdetail',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/voucherdetail/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/voucherdetail',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/voucherdetail/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/voucherdetail/${id}`,
      method: 'delete'
    })
  }
}

export default voucherdetailAPI
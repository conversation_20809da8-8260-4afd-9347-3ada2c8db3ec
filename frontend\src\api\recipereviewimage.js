import request from '@/utils/request'

const recipereviewimageAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/recipereviewimage',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/recipereviewimage/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/recipereviewimage',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/recipereviewimage/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/recipereviewimage/${id}`,
      method: 'delete'
    })
  }
}

export default recipereviewimageAPI
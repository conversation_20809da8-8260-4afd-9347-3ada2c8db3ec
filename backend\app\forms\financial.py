"""
财务系统表单
"""

from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, SelectField, DecimalField, DateField, IntegerField, SubmitField, HiddenField
from wtforms.validators import DataRequired, Length, Optional, NumberRange, ValidationError
from flask_login import current_user
from app import db
from app.models import Supplier
from app.models_financial import AccountingSubject, AccountPayable
from datetime import date


class AccountingSubjectForm(FlaskForm):
    """会计科目表单"""
    code = StringField('科目编码', validators=[
        DataRequired(message='请输入科目编码'),
        Length(min=1, max=20, message='科目编码长度应在1-20个字符之间')
    ])
    
    name = StringField('科目名称', validators=[
        DataRequired(message='请输入科目名称'),
        Length(min=1, max=100, message='科目名称长度应在1-100个字符之间')
    ])
    
    parent_id = SelectField('上级科目', coerce=int, validators=[Optional()])
    
    subject_type = SelectField('科目类型', choices=[
        ('资产', '资产'),
        ('负债', '负债'),
        ('净资产', '净资产'),
        ('所有者权益', '所有者权益'),
        ('收入', '收入'),
        ('费用', '费用'),
        ('预算收入', '预算收入'),
        ('预算支出', '预算支出')
    ], validators=[DataRequired(message='请选择科目类型')])
    
    balance_direction = SelectField('余额方向', choices=[
        ('借方', '借方'),
        ('贷方', '贷方')
    ], validators=[DataRequired(message='请选择余额方向')])
    
    description = TextAreaField('科目说明', validators=[
        Optional(),
        Length(max=500, message='科目说明不能超过500个字符')
    ])
    
    submit = SubmitField('提交')

    def __init__(self, *args, **kwargs):
        super(AccountingSubjectForm, self).__init__(*args, **kwargs)
        
        # 获取当前用户可访问的会计科目作为上级科目选项（系统科目+学校科目）
        if current_user.is_authenticated:
            user_area = current_user.get_current_area()
            if user_area:
                # 同时获取系统科目和学校科目
                subjects = AccountingSubject.query.filter(
                    db.or_(
                        db.and_(AccountingSubject.area_id == 1, AccountingSubject.is_system == True),
                        db.and_(AccountingSubject.area_id == user_area.id, AccountingSubject.is_system == False)
                    ),
                    AccountingSubject.is_active == True
                ).order_by(AccountingSubject.code).all()

                choices = [(0, '无上级科目')]
                for subject in subjects:
                    # 标识系统科目
                    prefix = "[系统]" if subject.is_system else "[学校]"
                    choices.append((subject.id, f"{prefix} {subject.code} - {subject.name}"))

                self.parent_id.choices = choices
            else:
                self.parent_id.choices = [(0, '无上级科目')]


class FinancialVoucherForm(FlaskForm):
    """财务凭证表单"""
    voucher_date = DateField('凭证日期', validators=[
        DataRequired(message='请选择凭证日期')
    ], default=date.today)
    
    voucher_type = SelectField('凭证类型', choices=[
        ('入库凭证', '入库凭证'),
        ('出库凭证', '出库凭证'),
        ('收款凭证', '收款凭证'),
        ('付款凭证', '付款凭证'),
        ('转账凭证', '转账凭证')
    ], validators=[DataRequired(message='请选择凭证类型')])
    
    summary = StringField('摘要', validators=[
        DataRequired(message='请输入摘要'),
        Length(min=1, max=200, message='摘要长度应在1-200个字符之间')
    ])
    
    notes = TextAreaField('备注', validators=[
        Optional(),
        Length(max=1000, message='备注不能超过1000个字符')
    ])
    
    submit = SubmitField('提交')


class PaymentRecordForm(FlaskForm):
    """付款记录表单"""
    payment_date = DateField('付款日期', validators=[
        DataRequired(message='请选择付款日期')
    ], default=date.today)
    
    amount = DecimalField('付款金额', validators=[
        DataRequired(message='请输入付款金额'),
        NumberRange(min=0.01, message='付款金额必须大于0')
    ], places=2)
    
    payment_method = SelectField('付款方式', choices=[
        ('现金', '现金'),
        ('银行转账', '银行转账'),
        ('支票', '支票'),
        ('其他', '其他')
    ], validators=[DataRequired(message='请选择付款方式')])
    
    payable_id = SelectField('应付账款', coerce=int, validators=[
        DataRequired(message='请选择应付账款')
    ])
    
    bank_account = StringField('付款账户', validators=[
        Optional(),
        Length(max=50, message='付款账户不能超过50个字符')
    ])
    
    reference_number = StringField('参考号', validators=[
        Optional(),
        Length(max=50, message='参考号不能超过50个字符')
    ])
    
    summary = StringField('摘要', validators=[
        DataRequired(message='请输入摘要'),
        Length(min=1, max=200, message='摘要长度应在1-200个字符之间')
    ])
    
    notes = TextAreaField('备注', validators=[
        Optional(),
        Length(max=1000, message='备注不能超过1000个字符')
    ])
    
    submit = SubmitField('提交')

    def __init__(self, *args, **kwargs):
        super(PaymentRecordForm, self).__init__(*args, **kwargs)
        
        # 获取当前用户可访问的未付清应付账款
        if current_user.is_authenticated:
            user_area = current_user.get_current_area()
            if user_area:
                payables = AccountPayable.query.filter(
                    AccountPayable.area_id == user_area.id,
                    AccountPayable.balance_amount > 0,
                    AccountPayable.status.in_(['未付款', '部分付款'])
                ).order_by(AccountPayable.created_at.desc()).all()
                
                choices = [(0, '请选择应付账款')]
                for payable in payables:
                    supplier_name = payable.supplier.name if payable.supplier else '未知供应商'
                    choices.append((
                        payable.id, 
                        f"{payable.payable_number} - {supplier_name} - ¥{payable.balance_amount}"
                    ))
                
                self.payable_id.choices = choices
            else:
                self.payable_id.choices = [(0, '请选择应付账款')]

    def validate_amount(self, amount):
        """验证付款金额不能超过应付账款余额"""
        if self.payable_id.data and self.payable_id.data > 0:
            payable = AccountPayable.query.get(self.payable_id.data)
            if payable and amount.data > payable.balance_amount:
                raise ValidationError(f'付款金额不能超过应付账款余额 ¥{payable.balance_amount}')


class IncomeRecordForm(FlaskForm):
    """收入记录表单"""
    income_date = DateField('收入日期', validators=[
        DataRequired(message='请选择收入日期')
    ], default=date.today)
    
    income_type = SelectField('收入类型', choices=[
        ('学生餐费', '学生餐费'),
        ('教师餐费', '教师餐费'),
        ('外来人员餐费', '外来人员餐费'),
        ('其他收入', '其他收入')
    ], validators=[DataRequired(message='请选择收入类型')])
    
    amount = DecimalField('收入金额', validators=[
        DataRequired(message='请输入收入金额'),
        NumberRange(min=0.01, message='收入金额必须大于0')
    ], places=2)
    
    payment_method = SelectField('收款方式', choices=[
        ('现金', '现金'),
        ('刷卡', '刷卡'),
        ('移动支付', '移动支付'),
        ('转账', '转账')
    ], validators=[DataRequired(message='请选择收款方式')])
    
    customer_type = SelectField('客户类型', choices=[
        ('', '请选择'),
        ('学生', '学生'),
        ('教师', '教师'),
        ('其他', '其他')
    ], validators=[Optional()])
    
    meal_type = SelectField('餐次', choices=[
        ('', '请选择'),
        ('早餐', '早餐'),
        ('午餐', '午餐'),
        ('晚餐', '晚餐')
    ], validators=[Optional()])
    
    serving_count = IntegerField('用餐人数', validators=[
        Optional(),
        NumberRange(min=1, message='用餐人数必须大于0')
    ])
    
    summary = StringField('摘要', validators=[
        DataRequired(message='请输入摘要'),
        Length(min=1, max=200, message='摘要长度应在1-200个字符之间')
    ])
    
    notes = TextAreaField('备注', validators=[
        Optional(),
        Length(max=1000, message='备注不能超过1000个字符')
    ])
    
    submit = SubmitField('提交')


class VoucherDetailForm(FlaskForm):
    """凭证明细表单（用于动态添加明细行）"""
    subject_id = SelectField('会计科目', coerce=int, validators=[
        DataRequired(message='请选择会计科目')
    ])
    
    summary = StringField('摘要', validators=[
        DataRequired(message='请输入摘要'),
        Length(min=1, max=200, message='摘要长度应在1-200个字符之间')
    ])
    
    debit_amount = DecimalField('借方金额', validators=[
        Optional(),
        NumberRange(min=0, message='借方金额不能为负数')
    ], places=2, default=0)
    
    credit_amount = DecimalField('贷方金额', validators=[
        Optional(),
        NumberRange(min=0, message='贷方金额不能为负数')
    ], places=2, default=0)
    
    auxiliary_info = StringField('辅助信息', validators=[
        Optional(),
        Length(max=200, message='辅助信息不能超过200个字符')
    ])

    def __init__(self, *args, **kwargs):
        super(VoucherDetailForm, self).__init__(*args, **kwargs)
        
        # 获取当前用户可访问的会计科目
        if current_user.is_authenticated:
            user_area = current_user.get_current_area()
            if user_area:
                subjects = AccountingSubject.query.filter_by(
                    area_id=user_area.id,
                    is_active=True
                ).order_by(AccountingSubject.code).all()
                
                choices = [(0, '请选择会计科目')]
                for subject in subjects:
                    choices.append((subject.id, f"{subject.code} - {subject.name}"))
                
                self.subject_id.choices = choices
            else:
                self.subject_id.choices = [(0, '请选择会计科目')]

    def validate(self):
        """验证借贷金额"""
        if not super().validate():
            return False
        
        # 借方金额和贷方金额不能同时为0
        if (not self.debit_amount.data or self.debit_amount.data == 0) and \
           (not self.credit_amount.data or self.credit_amount.data == 0):
            self.debit_amount.errors.append('借方金额和贷方金额不能同时为0')
            return False
        
        # 借方金额和贷方金额不能同时有值
        if (self.debit_amount.data and self.debit_amount.data > 0) and \
           (self.credit_amount.data and self.credit_amount.data > 0):
            self.debit_amount.errors.append('借方金额和贷方金额不能同时有值')
            return False
        
        return True

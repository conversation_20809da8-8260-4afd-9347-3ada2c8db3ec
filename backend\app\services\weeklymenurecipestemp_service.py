"""
WeeklyMenuRecipesTemp 服务层
"""

from typing import Optional, Dict, Any
from sqlalchemy.orm import Session
from app.models.models_weekly_menu_temp import WeeklyMenuRecipesTemp
from app.utils.database import get_db_session

class WeeklyMenuRecipesTempService:
    """WeeklyMenuRecipesTemp 服务"""

    @staticmethod
    def get_list(page: int = 1, per_page: int = 10, **filters):
        """获取WeeklyMenuRecipesTemp列表"""
        with get_db_session() as session:
            query = session.query(WeeklyMenuRecipesTemp)

            # 应用过滤条件
            for key, value in filters.items():
                if hasattr(WeeklyMenuRecipesTemp, key) and value is not None:
                    query = query.filter(getattr(WeeklyMenuRecipesTemp, key) == value)

            return query.paginate(
                page=page,
                per_page=per_page,
                error_out=False
            )

    @staticmethod
    def get_by_id(id: int) -> Optional[WeeklyMenuRecipesTemp]:
        """根据ID获取WeeklyMenuRecipesTemp"""
        with get_db_session() as session:
            return session.query(WeeklyMenuRecipesTemp).filter(WeeklyMenuRecipesTemp.id == id).first()

    @staticmethod
    def create(data: Dict[str, Any]) -> WeeklyMenuRecipesTemp:
        """创建WeeklyMenuRecipesTemp"""
        with get_db_session() as session:
            item = WeeklyMenuRecipesTemp(**data)
            session.add(item)
            session.commit()
            session.refresh(item)
            return item

    @staticmethod
    def update(id: int, data: Dict[str, Any]) -> Optional[WeeklyMenuRecipesTemp]:
        """更新WeeklyMenuRecipesTemp"""
        with get_db_session() as session:
            item = session.query(WeeklyMenuRecipesTemp).filter(WeeklyMenuRecipesTemp.id == id).first()
            if not item:
                return None

            for key, value in data.items():
                if hasattr(item, key):
                    setattr(item, key, value)

            session.commit()
            session.refresh(item)
            return item

    @staticmethod
    def delete(id: int) -> bool:
        """删除WeeklyMenuRecipesTemp"""
        with get_db_session() as session:
            item = session.query(WeeklyMenuRecipesTemp).filter(WeeklyMenuRecipesTemp.id == id).first()
            if not item:
                return False

            session.delete(item)
            session.commit()
            return True
"""
InventoryCheck 序列化模式
"""

from marshmallow import Schema, fields, validate

class InventoryCheckSchema(Schema):
    """InventoryCheck 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    check_number = fields.String(required=True)
    
    
    
    warehouse_id = fields.Integer(required=True)
    
    
    
    check_date = fields.Date(required=True)
    
    
    
    check_type = fields.String(required=True)
    
    
    
    operator_id = fields.Integer(required=True)
    
    
    
    approver_id = fields.Integer()
    
    
    
    status = fields.String(required=True)
    
    
    
    notes = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
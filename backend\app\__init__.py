from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_cors import CORS

# 初始化数据库
db = SQLAlchemy()

def create_app(config_name='development'):
    app = Flask(__name__)
    
    # 配置数据库
    if config_name == 'development':
        app.config['SQLALCHEMY_DATABASE_URI'] = 'mssql+pyodbc://StudentsCMSSP:Xg2LS44Cyz5Zt8@14.103.246.164/StudentsCMSSP?driver=ODBC+Driver+17+for+SQL+Server'
    
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SECRET_KEY'] = 'your-secret-key-here'
    
    # 初始化扩展
    db.init_app(app)
    CORS(app)
    
    # 注册蓝图
    try:
        from app.api import api_bp
        app.register_blueprint(api_bp, url_prefix='/api')
    except ImportError:
        pass
    
    # 健康检查路由
    @app.route('/health')
    def health_check():
        return {'status': 'ok', 'message': 'Backend API is running'}
    
    # 导入所有模型
    with app.app_context():
        try:
            from app.models.models import *
            from app.models.models_system import *
        except ImportError as e:
            app.logger.warning(f"模型导入失败: {e}")
    
    return app

"""
Flask 应用工厂
"""

from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_jwt_extended import JWTManager
from flask_cors import CORS
from config import Config

# 初始化扩展
db = SQLAlchemy()
jwt = JWTManager()

def create_app(config_class=Config):
    """创建 Flask 应用"""
    app = Flask(__name__)
    app.config.from_object(config_class)

    # 初始化扩展
    db.init_app(app)
    jwt.init_app(app)
    CORS(app)

    # 注册蓝图
    from app.api.v1 import api_v1_bp
    app.register_blueprint(api_v1_bp, url_prefix='/api/v1')

    # 导入模型
    
    from app.models.models import User
    
    from app.models.models import Role
    
    from app.models.models import UserRole
    
    from app.models.models import SupplierCategory
    
    from app.models.models import Supplier
    
    from app.models.models import IngredientCategory
    
    from app.models.models import Ingredient
    
    from app.models.models import SupplierProduct
    
    from app.models.models import PurchaseOrder
    
    from app.models.models import PurchaseOrderItem
    
    from app.models.models import RecipeCategory
    
    from app.models.models import Recipe
    
    from app.models.models import RecipeIngredient
    
    from app.models.models import ConsumptionPlan
    
    from app.models.models import ConsumptionDetail
    
    from app.models.models import FoodSample
    
    from app.models.models import SupplierCertificate
    
    from app.models.models import SupplierSchoolRelation
    
    from app.models.models import ProductSpecParameter
    
    from app.models.models import DeliveryInspection
    
    from app.models.models import DeliveryItemInspection
    
    from app.models.models import PurchaseRequisition
    
    from app.models.models import PurchaseRequisitionItem
    
    from app.models.models import SupplierDelivery
    
    from app.models.models import VideoGuide
    
    from app.models.models import DeliveryItem
    
    from app.models.models import Warehouse
    
    from app.models.models import StorageLocation
    
    from app.models.models import Inventory
    
    from app.models.models import StockIn
    
    from app.models.models import StockInItem
    
    from app.models.models import StockInDocument
    
    from app.models.models import IngredientInspection
    
    from app.models.models import StockOut
    
    from app.models.models import StockOutItem
    
    from app.models.models import InventoryCheck
    
    from app.models.models import InventoryCheckItem
    
    from app.models.models import Employee
    
    from app.models.models import HealthCertificate
    
    from app.models.models import MedicalExamination
    
    from app.models.models import DailyHealthCheck
    
    from app.models.models import TrainingRecord
    
    from app.models.models import AdministrativeArea
    
    from app.models.models import AreaChangeHistory
    
    from app.models.models import RecipeProcess
    
    from app.models.models import RecipeProcessIngredient
    
    from app.models.models import AuditLog
    
    from app.models.models import InventoryAlert
    
    from app.models.models import WeeklyMenu
    
    from app.models.models import WeeklyMenuRecipe
    
    from app.models.models import Notification
    
    from app.models.models_consultation import OnlineConsultation
    
    from app.models.models_daily_management import DailyLog
    
    from app.models.models_daily_management import InspectionRecord
    
    from app.models.models_daily_management import DiningCompanion
    
    from app.models.models_daily_management import CanteenTrainingRecord
    
    from app.models.models_daily_management import SpecialEvent
    
    from app.models.models_daily_management import Issue
    
    from app.models.models_daily_management import Photo
    
    from app.models.models_daily_management import InspectionTemplate
    
    from app.models.models_financial import VoucherDetail
    
    from app.models.models_homepage_carousel import HomepageCarousel
    
    from app.models.models_ingredient_traceability import MaterialBatch
    
    from app.models.models_ingredient_traceability import TraceDocument
    
    from app.models.models_ingredient_traceability import BatchFlow
    
    from app.models.models_phase1 import IngredientCategory
    
    from app.models.models_phase1 import RecipeCategory
    
    from app.models.models_phase1 import RecipeProcess
    
    from app.models.models_phase1 import RecipeProcessIngredient
    
    from app.models.models_phase3 import SupplierCertificate
    
    from app.models.models_phase3 import PurchaseRequisition
    
    from app.models.models_phase3 import PurchaseRequisitionItem
    
    from app.models.models_phase3 import SupplierDelivery
    
    from app.models.models_phase3 import DeliveryItem
    
    from app.models.models_phase4 import Warehouse
    
    from app.models.models_phase4 import StorageLocation
    
    from app.models.models_phase4 import Inventory
    
    from app.models.models_phase4 import StockIn
    
    from app.models.models_phase4 import StockInItem
    
    from app.models.models_phase4 import StockOut
    
    from app.models.models_phase4 import StockOutItem
    
    from app.models.models_phase4 import InventoryCheck
    
    from app.models.models_phase4 import InventoryCheckItem
    
    from app.models.models_product_batch import StandardUnit
    
    from app.models.models_product_batch import CategoryUnitMapping
    
    from app.models.models_product_batch import ProductBatch
    
    from app.models.models_recipe_advanced import RecipeReview
    
    from app.models.models_recipe_advanced import RecipeReviewImage
    
    from app.models.models_recipe_advanced import RecipeReviewTag
    
    from app.models.models_recipe_advanced import RecipeImprovementSuggestion
    
    from app.models.models_recipe_advanced import RecipeVersion
    
    from app.models.models_recipe_advanced import RecipeIngredientAlternative
    
    from app.models.models_recipe_advanced import RecipeSeasonalInfo
    
    from app.models.models_recipe_advanced import RecipeTag
    
    from app.models.models_recipe_advanced import UserRecipeFavorite
    
    from app.models.models_recipe_advanced import UserSearchHistory
    
    from app.models.models_supplier import SupplierSchoolRelation
    
    from app.models.models_supplier import ProductSpecParameter
    
    from app.models.models_supplier import DeliveryInspection
    
    from app.models.models_supplier import DeliveryItemInspection
    
    from app.models.models_system import SystemSetting
    
    from app.models.models_system import DatabaseBackup
    
    from app.models.models_system import SystemLog
    
    from app.models.models_visibility import ModuleVisibility
    
    from app.models.models_warehouse_new import WarehouseNew
    
    from app.models.models_weekly_menu_temp import WeeklyMenuRecipesTemp
    

    return app
"""
Flask 应用工厂
"""

from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_jwt_extended import JWTManager
from flask_cors import CORS
from config import config
from app.utils.database import init_database, test_connection

# 初始化扩展
db = SQLAlchemy()
jwt = JWTManager()

def create_app(config_name='default'):
    """创建 Flask 应用"""
    app = Flask(__name__)

    # 加载配置
    config_class = config.get(config_name, config['default'])
    config_instance = config_class()
    app.config.from_object(config_instance)

    # 初始化扩展
    db.init_app(app)
    jwt.init_app(app)
    CORS(app, origins=app.config['CORS_ORIGINS'])

    # 初始化数据库连接
    with app.app_context():
        init_database(app)

        # 测试数据库连接
        if test_connection():
            print("✅ 数据库连接成功")
        else:
            print("❌ 数据库连接失败")

    # 注册错误处理器
    @app.errorhandler(404)
    def not_found(error):
        from app.utils.response import error_response
        return error_response(message="资源不存在", code=404)

    @app.errorhandler(500)
    def internal_error(error):
        from app.utils.response import error_response
        return error_response(message="服务器内部错误", code=500)

    # 健康检查端点
    @app.route('/health')
    def health_check():
        from app.utils.response import success_response
        return success_response(data={"status": "healthy"}, message="服务运行正常")

    # 数据库连接测试端点
    @app.route('/api/v1/test-db')
    def test_db():
        from app.utils.response import success_response, error_response
        if test_connection():
            return success_response(message="数据库连接正常")
        else:
            return error_response(message="数据库连接失败", code=500)

    # 先设置模型文件中的 db 变量，然后再导入任何使用模型的代码
    import app.models.models as models_module
    models_module.db = db
    models_module.login_manager = jwt

    # 设置 login_manager 的 user_loader
    jwt.user_loader(models_module.load_user)
    print("✅ 模型 db 变量设置成功")

    # 现在可以安全地导入和注册 API
    with app.app_context():
        try:
            # 导入模型以确保它们被注册到 SQLAlchemy
            from app.models.models import User
            print("✅ 模型导入成功")

            # 注册简化的 API 蓝图
            from app.api.v1.simple_api import simple_api_bp
            app.register_blueprint(simple_api_bp, url_prefix='/api/v1')
            print("✅ 简化 API 注册成功")

            print("✅ 基本初始化成功")

        except Exception as e:
            print(f"⚠️ 初始化失败: {e}")
            print("🔧 基本功能仍可使用")

    # 模型将在需要时动态导入
    

    return app
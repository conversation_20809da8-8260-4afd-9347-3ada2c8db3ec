"""
PurchaseRequisition 序列化模式
"""

from marshmallow import Schema, fields, validate

class PurchaseRequisitionSchema(Schema):
    """PurchaseRequisition 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    requisition_number = fields.String(required=True)
    
    
    
    area_id = fields.Integer(required=True)
    
    
    
    requisition_date = fields.Date(required=True)
    
    
    
    required_date = fields.Date(required=True)
    
    
    
    status = fields.String(required=True)
    
    
    
    created_by = fields.Integer(required=True)
    
    
    
    approved_by = fields.Integer()
    
    
    
    notes = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
"""
CategoryUnitMapping 序列化模式
"""

from marshmallow import Schema, fields, validate

class CategoryUnitMappingSchema(Schema):
    """CategoryUnitMapping 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    category_id = fields.Integer(required=True)
    
    
    
    unit_id = fields.Integer(required=True)
    
    
    
    is_primary = fields.Boolean(required=True)
    
    
    
    created_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
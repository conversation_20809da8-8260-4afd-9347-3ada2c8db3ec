"""
AdministrativeArea 序列化模式
"""

from marshmallow import Schema, fields, validate

class AdministrativeAreaSchema(Schema):
    """AdministrativeArea 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    name = fields.String(required=True)
    
    
    
    code = fields.String(required=True)
    
    
    
    level = fields.Integer(required=True)
    
    
    
    parent_id = fields.Integer()
    
    
    
    description = fields.String()
    
    
    
    status = fields.Integer(required=True)
    
    
    
    is_township_school = fields.Boolean(required=True)
    
    
    
    created_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
"""
RecipeVersion 序列化模式
"""

from marshmallow import Schema, fields, validate

class RecipeVersionSchema(Schema):
    """RecipeVersion 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    recipe_id = fields.Integer(required=True)
    
    
    
    version = fields.Integer(required=True)
    
    
    
    data = fields.String(required=True)
    
    
    
    changed_by = fields.Integer(required=True)
    
    
    
    change_reason = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
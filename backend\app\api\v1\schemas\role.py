"""
Role 序列化模式
"""

from marshmallow import Schema, fields, validate

class RoleSchema(Schema):
    """Role 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    name = fields.String(required=True)
    
    
    
    description = fields.String()
    
    
    
    permissions = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
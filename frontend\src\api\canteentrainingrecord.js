import request from '@/utils/request'

const canteentrainingrecordAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/canteentrainingrecord',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/canteentrainingrecord/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/canteentrainingrecord',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/canteentrainingrecord/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/canteentrainingrecord/${id}`,
      method: 'delete'
    })
  }
}

export default canteentrainingrecordAPI
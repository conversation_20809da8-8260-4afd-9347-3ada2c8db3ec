"""
BatchFlow 服务层
"""

from typing import Optional, Dict, Any
from sqlalchemy.orm import Session
from app.models.models_ingredient_traceability import Batch<PERSON>low
from app.utils.database import get_db_session

class BatchFlowService:
    """BatchFlow 服务"""

    @staticmethod
    def get_list(page: int = 1, per_page: int = 10, **filters):
        """获取BatchFlow列表"""
        with get_db_session() as session:
            query = session.query(BatchFlow)

            # 应用过滤条件
            for key, value in filters.items():
                if hasattr(BatchFlow, key) and value is not None:
                    query = query.filter(getattr(BatchFlow, key) == value)

            return query.paginate(
                page=page,
                per_page=per_page,
                error_out=False
            )

    @staticmethod
    def get_by_id(id: int) -> Optional[BatchFlow]:
        """根据ID获取BatchFlow"""
        with get_db_session() as session:
            return session.query(BatchFlow).filter(BatchFlow.id == id).first()

    @staticmethod
    def create(data: Dict[str, Any]) -> BatchFlow:
        """创建BatchFlow"""
        with get_db_session() as session:
            item = BatchFlow(**data)
            session.add(item)
            session.commit()
            session.refresh(item)
            return item

    @staticmethod
    def update(id: int, data: Dict[str, Any]) -> Optional[BatchFlow]:
        """更新BatchFlow"""
        with get_db_session() as session:
            item = session.query(BatchFlow).filter(BatchFlow.id == id).first()
            if not item:
                return None

            for key, value in data.items():
                if hasattr(item, key):
                    setattr(item, key, value)

            session.commit()
            session.refresh(item)
            return item

    @staticmethod
    def delete(id: int) -> bool:
        """删除BatchFlow"""
        with get_db_session() as session:
            item = session.query(BatchFlow).filter(BatchFlow.id == id).first()
            if not item:
                return False

            session.delete(item)
            session.commit()
            return True
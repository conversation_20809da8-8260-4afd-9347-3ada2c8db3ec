"""
Recipe 序列化模式
"""

from marshmallow import Schema, fields, validate

class RecipeSchema(Schema):
    """Recipe 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    name = fields.String(required=True)
    
    
    
    category = fields.String(required=True)
    
    
    
    category_id = fields.Integer()
    
    
    
    meal_type = fields.String()
    
    
    
    main_image = fields.String()
    
    
    
    description = fields.String()
    
    
    
    calories = fields.Integer()
    
    
    
    nutrition_info = fields.String()
    
    
    
    cooking_method = fields.String()
    
    
    
    cooking_steps = fields.String()
    
    
    
    cooking_time = fields.Integer()
    
    
    
    serving_size = fields.Integer()
    
    
    
    status = fields.Integer(required=True)
    
    
    
    created_by = fields.Integer(required=True)
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    
    
    parent_id = fields.Integer()
    
    
    
    is_template = fields.Boolean(required=True)
    
    
    
    template_type = fields.String()
    
    
    
    variation_reason = fields.String()
    
    
    
    version = fields.Integer(required=True)
    
    
    
    is_user_defined = fields.Boolean(required=True)
    
    
    
    priority = fields.Integer(required=True)
    
    
    
    area_id = fields.Integer()
    
    
    
    is_global = fields.Boolean(required=True)
    
    
    
    is_deleted = fields.Boolean(required=True)
    
    
    
    deleted_at = fields.String()
    
    
    
    deleted_by = fields.Integer()
    
    

    class Meta:
        ordered = True
"""
MaterialBatch 服务层
"""

from typing import Optional, Dict, Any
from sqlalchemy.orm import Session
from app.models.models_ingredient_traceability import MaterialBatch
from app.utils.database import get_db_session

class MaterialBatchService:
    """MaterialBatch 服务"""

    @staticmethod
    def get_list(page: int = 1, per_page: int = 10, **filters):
        """获取MaterialBatch列表"""
        with get_db_session() as session:
            query = session.query(MaterialBatch)

            # 应用过滤条件
            for key, value in filters.items():
                if hasattr(MaterialBatch, key) and value is not None:
                    query = query.filter(getattr(MaterialBatch, key) == value)

            return query.paginate(
                page=page,
                per_page=per_page,
                error_out=False
            )

    @staticmethod
    def get_by_id(id: int) -> Optional[MaterialBatch]:
        """根据ID获取MaterialBatch"""
        with get_db_session() as session:
            return session.query(MaterialBatch).filter(MaterialBatch.id == id).first()

    @staticmethod
    def create(data: Dict[str, Any]) -> MaterialBatch:
        """创建MaterialBatch"""
        with get_db_session() as session:
            item = MaterialBatch(**data)
            session.add(item)
            session.commit()
            session.refresh(item)
            return item

    @staticmethod
    def update(id: int, data: Dict[str, Any]) -> Optional[MaterialBatch]:
        """更新MaterialBatch"""
        with get_db_session() as session:
            item = session.query(MaterialBatch).filter(MaterialBatch.id == id).first()
            if not item:
                return None

            for key, value in data.items():
                if hasattr(item, key):
                    setattr(item, key, value)

            session.commit()
            session.refresh(item)
            return item

    @staticmethod
    def delete(id: int) -> bool:
        """删除MaterialBatch"""
        with get_db_session() as session:
            item = session.query(MaterialBatch).filter(MaterialBatch.id == id).first()
            if not item:
                return False

            session.delete(item)
            session.commit()
            return True
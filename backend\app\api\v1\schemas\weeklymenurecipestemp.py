"""
WeeklyMenuRecipesTemp 序列化模式
"""

from marshmallow import Schema, fields, validate

class WeeklyMenuRecipesTempSchema(Schema):
    """WeeklyMenuRecipesTemp 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    weekly_menu_id = fields.Integer()
    
    
    
    day_of_week = fields.Integer(required=True)
    
    
    
    meal_type = fields.String(required=True)
    
    
    
    recipe_id = fields.Integer()
    
    
    
    recipe_name = fields.String(required=True)
    
    
    
    is_custom = fields.Boolean(required=True)
    
    
    
    temp_data = fields.String()
    
    
    
    created_at = fields.DateTime(required=True)
    
    
    
    updated_at = fields.DateTime(required=True)
    
    

    class Meta:
        ordered = True
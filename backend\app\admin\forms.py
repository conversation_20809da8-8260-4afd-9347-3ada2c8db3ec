from flask_wtf import FlaskForm
from wtforms import StringField, PasswordField, SelectField, SelectMultipleField, BooleanField, SubmitField, TextAreaField, HiddenField
from wtforms.validators import DataRequired, Email, EqualTo, Length, ValidationError, Optional
from app.models import User, Role, AdministrativeArea
import json

class UserForm(FlaskForm):
    """用户表单"""
    username = StringField('用户名', validators=[DataRequired(message='请输入用户名'), Length(min=3, max=20, message='用户名长度必须在3-20个字符之间')])
    email = StringField('电子邮箱', validators=[DataRequired(message='请输入电子邮箱'), Email(message='请输入有效的电子邮箱地址')])
    real_name = StringField('真实姓名', validators=[DataRequired(message='请输入真实姓名'), Length(min=2, max=20, message='姓名长度必须在2-20个字符之间')])
    phone = StringField('手机号码', validators=[DataRequired(message='请输入手机号码'), Length(min=11, max=11, message='请输入11位手机号码')])
    password = PasswordField('密码', validators=[Optional(), Length(min=6, message='密码长度不能少于6个字符')])
    password2 = PasswordField('确认密码', validators=[EqualTo('password', message='两次输入的密码不一致')])
    roles = SelectMultipleField('角色', coerce=int, validators=[DataRequired(message='请选择至少一个角色')])
    area_id = SelectField('所属区域', coerce=int, validators=[Optional()])
    area_level = SelectField('区域级别', choices=[(1, '县市区'), (2, '乡镇'), (3, '学校'), (4, '食堂')], coerce=int, validators=[Optional()])
    status = SelectField('状态', choices=[(1, '启用'), (0, '禁用')], coerce=int, validators=[DataRequired(message='请选择状态')])
    submit = SubmitField('提交')

    # 用于存储用户ID，用于编辑时验证唯一性
    user_id = None

    def validate_username(self, username):
        """验证用户名唯一性"""
        user = User.query.filter_by(username=username.data).first()
        if user and (not hasattr(self, 'user_id') or user.id != self.user_id):
            raise ValidationError('该用户名已被使用，请更换一个')

    def validate_email(self, email):
        """验证邮箱唯一性"""
        user = User.query.filter_by(email=email.data).first()
        if user and (not hasattr(self, 'user_id') or user.id != self.user_id):
            raise ValidationError('该邮箱已被注册，请更换一个')

    def validate_password2(self, password2):
        """验证两次密码输入是否一致"""
        if self.password.data and self.password.data != password2.data:
            raise ValidationError('两次输入的密码不一致')

class RoleForm(FlaskForm):
    """角色表单"""
    name = StringField('角色名称', validators=[DataRequired(message='请输入角色名称'), Length(min=2, max=50, message='角色名称长度必须在2-50个字符之间')])
    description = StringField('角色描述', validators=[Optional(), Length(max=200, message='角色描述长度不能超过200个字符')])
    permissions = TextAreaField('权限配置 (JSON格式)', validators=[Optional()])
    submit = SubmitField('提交')

    # 用于存储角色ID，用于编辑时验证唯一性
    role_id = None

    def validate_permissions(self, permissions):
        """验证权限配置是否是有效的JSON格式"""
        if permissions.data:
            try:
                json.loads(permissions.data)
            except json.JSONDecodeError:
                raise ValidationError('权限配置必须是有效的JSON格式')

    def validate_name(self, name):
        """验证角色名称唯一性"""
        role = Role.query.filter_by(name=name.data).first()
        if role and (not hasattr(self, 'role_id') or role.id != self.role_id):
            raise ValidationError('该角色名称已存在，请更换一个')


class PermissionsForm(FlaskForm):
    """权限表单"""
    csrf_token = HiddenField()  # 显式定义csrf_token字段，确保CSRF保护正常工作
    submit = SubmitField('保存')

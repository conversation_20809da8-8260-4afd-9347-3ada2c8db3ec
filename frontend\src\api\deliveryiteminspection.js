import request from '@/utils/request'

const deliveryiteminspectionAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/deliveryiteminspection',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/deliveryiteminspection/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/deliveryiteminspection',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/deliveryiteminspection/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/deliveryiteminspection/${id}`,
      method: 'delete'
    })
  }
}

export default deliveryiteminspectionAPI
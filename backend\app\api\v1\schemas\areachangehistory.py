"""
AreaChangeHistory 序列化模式
"""

from marshmallow import Schema, fields, validate

class AreaChangeHistorySchema(Schema):
    """AreaChangeHistory 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    area_id = fields.Integer(required=True)
    
    
    
    change_type = fields.String(required=True)
    
    
    
    old_parent_id = fields.Integer()
    
    
    
    new_parent_id = fields.Integer()
    
    
    
    old_data = fields.String()
    
    
    
    new_data = fields.String()
    
    
    
    changed_by = fields.Integer(required=True)
    
    
    
    created_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
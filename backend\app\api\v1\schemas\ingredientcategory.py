"""
IngredientCategory 序列化模式
"""

from marshmallow import Schema, fields, validate

class IngredientCategorySchema(Schema):
    """IngredientCategory 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    name = fields.String(required=True)
    
    
    
    parent_id = fields.Integer()
    
    
    
    description = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
import request from '@/utils/request'

const ingredientinspectionAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/ingredientinspection',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/ingredientinspection/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/ingredientinspection',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/ingredientinspection/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/ingredientinspection/${id}`,
      method: 'delete'
    })
  }
}

export default ingredientinspectionAPI
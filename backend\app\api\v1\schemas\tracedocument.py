"""
TraceDocument 序列化模式
"""

from marshmallow import Schema, fields, validate

class TraceDocumentSchema(Schema):
    """TraceDocument 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    batch_id = fields.Integer(required=True)
    
    
    
    document_type = fields.String(required=True)
    
    
    
    document_no = fields.String()
    
    
    
    document_path = fields.String(required=True)
    
    
    
    upload_time = fields.String(required=True)
    
    
    
    uploader_id = fields.Integer(required=True)
    
    
    
    remark = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    name: 'Home',
    redirect: '/dashboard'
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/Dashboard.vue')
  },
  
  // User 路由
  {
    path: '/user',
    name: 'UserList',
    component: () => import('@/views/UserList.vue')
  },
  {
    path: '/user/create',
    name: 'UserForm',
    component: () => import('@/views/UserForm.vue')
  },
  {
    path: '/user/:id/edit',
    name: 'UserEdit',
    component: () => import('@/views/UserForm.vue')
  },
  {
    path: '/user/:id',
    name: 'UserDetail',
    component: () => import('@/views/UserDetail.vue')
  },
  
  // Role 路由
  {
    path: '/role',
    name: 'RoleList',
    component: () => import('@/views/RoleList.vue')
  },
  {
    path: '/role/create',
    name: 'RoleForm',
    component: () => import('@/views/RoleForm.vue')
  },
  {
    path: '/role/:id/edit',
    name: 'RoleEdit',
    component: () => import('@/views/RoleForm.vue')
  },
  {
    path: '/role/:id',
    name: 'RoleDetail',
    component: () => import('@/views/RoleDetail.vue')
  },
  
  // UserRole 路由
  {
    path: '/userrole',
    name: 'UserRoleList',
    component: () => import('@/views/UserRoleList.vue')
  },
  {
    path: '/userrole/create',
    name: 'UserRoleForm',
    component: () => import('@/views/UserRoleForm.vue')
  },
  {
    path: '/userrole/:id/edit',
    name: 'UserRoleEdit',
    component: () => import('@/views/UserRoleForm.vue')
  },
  {
    path: '/userrole/:id',
    name: 'UserRoleDetail',
    component: () => import('@/views/UserRoleDetail.vue')
  },
  
  // SupplierCategory 路由
  {
    path: '/suppliercategory',
    name: 'SupplierCategoryList',
    component: () => import('@/views/SupplierCategoryList.vue')
  },
  {
    path: '/suppliercategory/create',
    name: 'SupplierCategoryForm',
    component: () => import('@/views/SupplierCategoryForm.vue')
  },
  {
    path: '/suppliercategory/:id/edit',
    name: 'SupplierCategoryEdit',
    component: () => import('@/views/SupplierCategoryForm.vue')
  },
  {
    path: '/suppliercategory/:id',
    name: 'SupplierCategoryDetail',
    component: () => import('@/views/SupplierCategoryDetail.vue')
  },
  
  // Supplier 路由
  {
    path: '/supplier',
    name: 'SupplierList',
    component: () => import('@/views/SupplierList.vue')
  },
  {
    path: '/supplier/create',
    name: 'SupplierForm',
    component: () => import('@/views/SupplierForm.vue')
  },
  {
    path: '/supplier/:id/edit',
    name: 'SupplierEdit',
    component: () => import('@/views/SupplierForm.vue')
  },
  {
    path: '/supplier/:id',
    name: 'SupplierDetail',
    component: () => import('@/views/SupplierDetail.vue')
  },
  
  // IngredientCategory 路由
  {
    path: '/ingredientcategory',
    name: 'IngredientCategoryList',
    component: () => import('@/views/IngredientCategoryList.vue')
  },
  {
    path: '/ingredientcategory/create',
    name: 'IngredientCategoryForm',
    component: () => import('@/views/IngredientCategoryForm.vue')
  },
  {
    path: '/ingredientcategory/:id/edit',
    name: 'IngredientCategoryEdit',
    component: () => import('@/views/IngredientCategoryForm.vue')
  },
  {
    path: '/ingredientcategory/:id',
    name: 'IngredientCategoryDetail',
    component: () => import('@/views/IngredientCategoryDetail.vue')
  },
  
  // Ingredient 路由
  {
    path: '/ingredient',
    name: 'IngredientList',
    component: () => import('@/views/IngredientList.vue')
  },
  {
    path: '/ingredient/create',
    name: 'IngredientForm',
    component: () => import('@/views/IngredientForm.vue')
  },
  {
    path: '/ingredient/:id/edit',
    name: 'IngredientEdit',
    component: () => import('@/views/IngredientForm.vue')
  },
  {
    path: '/ingredient/:id',
    name: 'IngredientDetail',
    component: () => import('@/views/IngredientDetail.vue')
  },
  
  // SupplierProduct 路由
  {
    path: '/supplierproduct',
    name: 'SupplierProductList',
    component: () => import('@/views/SupplierProductList.vue')
  },
  {
    path: '/supplierproduct/create',
    name: 'SupplierProductForm',
    component: () => import('@/views/SupplierProductForm.vue')
  },
  {
    path: '/supplierproduct/:id/edit',
    name: 'SupplierProductEdit',
    component: () => import('@/views/SupplierProductForm.vue')
  },
  {
    path: '/supplierproduct/:id',
    name: 'SupplierProductDetail',
    component: () => import('@/views/SupplierProductDetail.vue')
  },
  
  // PurchaseOrder 路由
  {
    path: '/purchaseorder',
    name: 'PurchaseOrderList',
    component: () => import('@/views/PurchaseOrderList.vue')
  },
  {
    path: '/purchaseorder/create',
    name: 'PurchaseOrderForm',
    component: () => import('@/views/PurchaseOrderForm.vue')
  },
  {
    path: '/purchaseorder/:id/edit',
    name: 'PurchaseOrderEdit',
    component: () => import('@/views/PurchaseOrderForm.vue')
  },
  {
    path: '/purchaseorder/:id',
    name: 'PurchaseOrderDetail',
    component: () => import('@/views/PurchaseOrderDetail.vue')
  },
  
  // PurchaseOrderItem 路由
  {
    path: '/purchaseorderitem',
    name: 'PurchaseOrderItemList',
    component: () => import('@/views/PurchaseOrderItemList.vue')
  },
  {
    path: '/purchaseorderitem/create',
    name: 'PurchaseOrderItemForm',
    component: () => import('@/views/PurchaseOrderItemForm.vue')
  },
  {
    path: '/purchaseorderitem/:id/edit',
    name: 'PurchaseOrderItemEdit',
    component: () => import('@/views/PurchaseOrderItemForm.vue')
  },
  {
    path: '/purchaseorderitem/:id',
    name: 'PurchaseOrderItemDetail',
    component: () => import('@/views/PurchaseOrderItemDetail.vue')
  },
  
  // RecipeCategory 路由
  {
    path: '/recipecategory',
    name: 'RecipeCategoryList',
    component: () => import('@/views/RecipeCategoryList.vue')
  },
  {
    path: '/recipecategory/create',
    name: 'RecipeCategoryForm',
    component: () => import('@/views/RecipeCategoryForm.vue')
  },
  {
    path: '/recipecategory/:id/edit',
    name: 'RecipeCategoryEdit',
    component: () => import('@/views/RecipeCategoryForm.vue')
  },
  {
    path: '/recipecategory/:id',
    name: 'RecipeCategoryDetail',
    component: () => import('@/views/RecipeCategoryDetail.vue')
  },
  
  // Recipe 路由
  {
    path: '/recipe',
    name: 'RecipeList',
    component: () => import('@/views/RecipeList.vue')
  },
  {
    path: '/recipe/create',
    name: 'RecipeForm',
    component: () => import('@/views/RecipeForm.vue')
  },
  {
    path: '/recipe/:id/edit',
    name: 'RecipeEdit',
    component: () => import('@/views/RecipeForm.vue')
  },
  {
    path: '/recipe/:id',
    name: 'RecipeDetail',
    component: () => import('@/views/RecipeDetail.vue')
  },
  
  // RecipeIngredient 路由
  {
    path: '/recipeingredient',
    name: 'RecipeIngredientList',
    component: () => import('@/views/RecipeIngredientList.vue')
  },
  {
    path: '/recipeingredient/create',
    name: 'RecipeIngredientForm',
    component: () => import('@/views/RecipeIngredientForm.vue')
  },
  {
    path: '/recipeingredient/:id/edit',
    name: 'RecipeIngredientEdit',
    component: () => import('@/views/RecipeIngredientForm.vue')
  },
  {
    path: '/recipeingredient/:id',
    name: 'RecipeIngredientDetail',
    component: () => import('@/views/RecipeIngredientDetail.vue')
  },
  
  // ConsumptionPlan 路由
  {
    path: '/consumptionplan',
    name: 'ConsumptionPlanList',
    component: () => import('@/views/ConsumptionPlanList.vue')
  },
  {
    path: '/consumptionplan/create',
    name: 'ConsumptionPlanForm',
    component: () => import('@/views/ConsumptionPlanForm.vue')
  },
  {
    path: '/consumptionplan/:id/edit',
    name: 'ConsumptionPlanEdit',
    component: () => import('@/views/ConsumptionPlanForm.vue')
  },
  {
    path: '/consumptionplan/:id',
    name: 'ConsumptionPlanDetail',
    component: () => import('@/views/ConsumptionPlanDetail.vue')
  },
  
  // ConsumptionDetail 路由
  {
    path: '/consumptiondetail',
    name: 'ConsumptionDetailList',
    component: () => import('@/views/ConsumptionDetailList.vue')
  },
  {
    path: '/consumptiondetail/create',
    name: 'ConsumptionDetailForm',
    component: () => import('@/views/ConsumptionDetailForm.vue')
  },
  {
    path: '/consumptiondetail/:id/edit',
    name: 'ConsumptionDetailEdit',
    component: () => import('@/views/ConsumptionDetailForm.vue')
  },
  {
    path: '/consumptiondetail/:id',
    name: 'ConsumptionDetailDetail',
    component: () => import('@/views/ConsumptionDetailDetail.vue')
  },
  
  // FoodSample 路由
  {
    path: '/foodsample',
    name: 'FoodSampleList',
    component: () => import('@/views/FoodSampleList.vue')
  },
  {
    path: '/foodsample/create',
    name: 'FoodSampleForm',
    component: () => import('@/views/FoodSampleForm.vue')
  },
  {
    path: '/foodsample/:id/edit',
    name: 'FoodSampleEdit',
    component: () => import('@/views/FoodSampleForm.vue')
  },
  {
    path: '/foodsample/:id',
    name: 'FoodSampleDetail',
    component: () => import('@/views/FoodSampleDetail.vue')
  },
  
  // SupplierCertificate 路由
  {
    path: '/suppliercertificate',
    name: 'SupplierCertificateList',
    component: () => import('@/views/SupplierCertificateList.vue')
  },
  {
    path: '/suppliercertificate/create',
    name: 'SupplierCertificateForm',
    component: () => import('@/views/SupplierCertificateForm.vue')
  },
  {
    path: '/suppliercertificate/:id/edit',
    name: 'SupplierCertificateEdit',
    component: () => import('@/views/SupplierCertificateForm.vue')
  },
  {
    path: '/suppliercertificate/:id',
    name: 'SupplierCertificateDetail',
    component: () => import('@/views/SupplierCertificateDetail.vue')
  },
  
  // SupplierSchoolRelation 路由
  {
    path: '/supplierschoolrelation',
    name: 'SupplierSchoolRelationList',
    component: () => import('@/views/SupplierSchoolRelationList.vue')
  },
  {
    path: '/supplierschoolrelation/create',
    name: 'SupplierSchoolRelationForm',
    component: () => import('@/views/SupplierSchoolRelationForm.vue')
  },
  {
    path: '/supplierschoolrelation/:id/edit',
    name: 'SupplierSchoolRelationEdit',
    component: () => import('@/views/SupplierSchoolRelationForm.vue')
  },
  {
    path: '/supplierschoolrelation/:id',
    name: 'SupplierSchoolRelationDetail',
    component: () => import('@/views/SupplierSchoolRelationDetail.vue')
  },
  
  // ProductSpecParameter 路由
  {
    path: '/productspecparameter',
    name: 'ProductSpecParameterList',
    component: () => import('@/views/ProductSpecParameterList.vue')
  },
  {
    path: '/productspecparameter/create',
    name: 'ProductSpecParameterForm',
    component: () => import('@/views/ProductSpecParameterForm.vue')
  },
  {
    path: '/productspecparameter/:id/edit',
    name: 'ProductSpecParameterEdit',
    component: () => import('@/views/ProductSpecParameterForm.vue')
  },
  {
    path: '/productspecparameter/:id',
    name: 'ProductSpecParameterDetail',
    component: () => import('@/views/ProductSpecParameterDetail.vue')
  },
  
  // DeliveryInspection 路由
  {
    path: '/deliveryinspection',
    name: 'DeliveryInspectionList',
    component: () => import('@/views/DeliveryInspectionList.vue')
  },
  {
    path: '/deliveryinspection/create',
    name: 'DeliveryInspectionForm',
    component: () => import('@/views/DeliveryInspectionForm.vue')
  },
  {
    path: '/deliveryinspection/:id/edit',
    name: 'DeliveryInspectionEdit',
    component: () => import('@/views/DeliveryInspectionForm.vue')
  },
  {
    path: '/deliveryinspection/:id',
    name: 'DeliveryInspectionDetail',
    component: () => import('@/views/DeliveryInspectionDetail.vue')
  },
  
  // DeliveryItemInspection 路由
  {
    path: '/deliveryiteminspection',
    name: 'DeliveryItemInspectionList',
    component: () => import('@/views/DeliveryItemInspectionList.vue')
  },
  {
    path: '/deliveryiteminspection/create',
    name: 'DeliveryItemInspectionForm',
    component: () => import('@/views/DeliveryItemInspectionForm.vue')
  },
  {
    path: '/deliveryiteminspection/:id/edit',
    name: 'DeliveryItemInspectionEdit',
    component: () => import('@/views/DeliveryItemInspectionForm.vue')
  },
  {
    path: '/deliveryiteminspection/:id',
    name: 'DeliveryItemInspectionDetail',
    component: () => import('@/views/DeliveryItemInspectionDetail.vue')
  },
  
  // PurchaseRequisition 路由
  {
    path: '/purchaserequisition',
    name: 'PurchaseRequisitionList',
    component: () => import('@/views/PurchaseRequisitionList.vue')
  },
  {
    path: '/purchaserequisition/create',
    name: 'PurchaseRequisitionForm',
    component: () => import('@/views/PurchaseRequisitionForm.vue')
  },
  {
    path: '/purchaserequisition/:id/edit',
    name: 'PurchaseRequisitionEdit',
    component: () => import('@/views/PurchaseRequisitionForm.vue')
  },
  {
    path: '/purchaserequisition/:id',
    name: 'PurchaseRequisitionDetail',
    component: () => import('@/views/PurchaseRequisitionDetail.vue')
  },
  
  // PurchaseRequisitionItem 路由
  {
    path: '/purchaserequisitionitem',
    name: 'PurchaseRequisitionItemList',
    component: () => import('@/views/PurchaseRequisitionItemList.vue')
  },
  {
    path: '/purchaserequisitionitem/create',
    name: 'PurchaseRequisitionItemForm',
    component: () => import('@/views/PurchaseRequisitionItemForm.vue')
  },
  {
    path: '/purchaserequisitionitem/:id/edit',
    name: 'PurchaseRequisitionItemEdit',
    component: () => import('@/views/PurchaseRequisitionItemForm.vue')
  },
  {
    path: '/purchaserequisitionitem/:id',
    name: 'PurchaseRequisitionItemDetail',
    component: () => import('@/views/PurchaseRequisitionItemDetail.vue')
  },
  
  // SupplierDelivery 路由
  {
    path: '/supplierdelivery',
    name: 'SupplierDeliveryList',
    component: () => import('@/views/SupplierDeliveryList.vue')
  },
  {
    path: '/supplierdelivery/create',
    name: 'SupplierDeliveryForm',
    component: () => import('@/views/SupplierDeliveryForm.vue')
  },
  {
    path: '/supplierdelivery/:id/edit',
    name: 'SupplierDeliveryEdit',
    component: () => import('@/views/SupplierDeliveryForm.vue')
  },
  {
    path: '/supplierdelivery/:id',
    name: 'SupplierDeliveryDetail',
    component: () => import('@/views/SupplierDeliveryDetail.vue')
  },
  
  // VideoGuide 路由
  {
    path: '/videoguide',
    name: 'VideoGuideList',
    component: () => import('@/views/VideoGuideList.vue')
  },
  {
    path: '/videoguide/create',
    name: 'VideoGuideForm',
    component: () => import('@/views/VideoGuideForm.vue')
  },
  {
    path: '/videoguide/:id/edit',
    name: 'VideoGuideEdit',
    component: () => import('@/views/VideoGuideForm.vue')
  },
  {
    path: '/videoguide/:id',
    name: 'VideoGuideDetail',
    component: () => import('@/views/VideoGuideDetail.vue')
  },
  
  // DeliveryItem 路由
  {
    path: '/deliveryitem',
    name: 'DeliveryItemList',
    component: () => import('@/views/DeliveryItemList.vue')
  },
  {
    path: '/deliveryitem/create',
    name: 'DeliveryItemForm',
    component: () => import('@/views/DeliveryItemForm.vue')
  },
  {
    path: '/deliveryitem/:id/edit',
    name: 'DeliveryItemEdit',
    component: () => import('@/views/DeliveryItemForm.vue')
  },
  {
    path: '/deliveryitem/:id',
    name: 'DeliveryItemDetail',
    component: () => import('@/views/DeliveryItemDetail.vue')
  },
  
  // Warehouse 路由
  {
    path: '/warehouse',
    name: 'WarehouseList',
    component: () => import('@/views/WarehouseList.vue')
  },
  {
    path: '/warehouse/create',
    name: 'WarehouseForm',
    component: () => import('@/views/WarehouseForm.vue')
  },
  {
    path: '/warehouse/:id/edit',
    name: 'WarehouseEdit',
    component: () => import('@/views/WarehouseForm.vue')
  },
  {
    path: '/warehouse/:id',
    name: 'WarehouseDetail',
    component: () => import('@/views/WarehouseDetail.vue')
  },
  
  // StorageLocation 路由
  {
    path: '/storagelocation',
    name: 'StorageLocationList',
    component: () => import('@/views/StorageLocationList.vue')
  },
  {
    path: '/storagelocation/create',
    name: 'StorageLocationForm',
    component: () => import('@/views/StorageLocationForm.vue')
  },
  {
    path: '/storagelocation/:id/edit',
    name: 'StorageLocationEdit',
    component: () => import('@/views/StorageLocationForm.vue')
  },
  {
    path: '/storagelocation/:id',
    name: 'StorageLocationDetail',
    component: () => import('@/views/StorageLocationDetail.vue')
  },
  
  // Inventory 路由
  {
    path: '/inventory',
    name: 'InventoryList',
    component: () => import('@/views/InventoryList.vue')
  },
  {
    path: '/inventory/create',
    name: 'InventoryForm',
    component: () => import('@/views/InventoryForm.vue')
  },
  {
    path: '/inventory/:id/edit',
    name: 'InventoryEdit',
    component: () => import('@/views/InventoryForm.vue')
  },
  {
    path: '/inventory/:id',
    name: 'InventoryDetail',
    component: () => import('@/views/InventoryDetail.vue')
  },
  
  // StockIn 路由
  {
    path: '/stockin',
    name: 'StockInList',
    component: () => import('@/views/StockInList.vue')
  },
  {
    path: '/stockin/create',
    name: 'StockInForm',
    component: () => import('@/views/StockInForm.vue')
  },
  {
    path: '/stockin/:id/edit',
    name: 'StockInEdit',
    component: () => import('@/views/StockInForm.vue')
  },
  {
    path: '/stockin/:id',
    name: 'StockInDetail',
    component: () => import('@/views/StockInDetail.vue')
  },
  
  // StockInItem 路由
  {
    path: '/stockinitem',
    name: 'StockInItemList',
    component: () => import('@/views/StockInItemList.vue')
  },
  {
    path: '/stockinitem/create',
    name: 'StockInItemForm',
    component: () => import('@/views/StockInItemForm.vue')
  },
  {
    path: '/stockinitem/:id/edit',
    name: 'StockInItemEdit',
    component: () => import('@/views/StockInItemForm.vue')
  },
  {
    path: '/stockinitem/:id',
    name: 'StockInItemDetail',
    component: () => import('@/views/StockInItemDetail.vue')
  },
  
  // StockInDocument 路由
  {
    path: '/stockindocument',
    name: 'StockInDocumentList',
    component: () => import('@/views/StockInDocumentList.vue')
  },
  {
    path: '/stockindocument/create',
    name: 'StockInDocumentForm',
    component: () => import('@/views/StockInDocumentForm.vue')
  },
  {
    path: '/stockindocument/:id/edit',
    name: 'StockInDocumentEdit',
    component: () => import('@/views/StockInDocumentForm.vue')
  },
  {
    path: '/stockindocument/:id',
    name: 'StockInDocumentDetail',
    component: () => import('@/views/StockInDocumentDetail.vue')
  },
  
  // IngredientInspection 路由
  {
    path: '/ingredientinspection',
    name: 'IngredientInspectionList',
    component: () => import('@/views/IngredientInspectionList.vue')
  },
  {
    path: '/ingredientinspection/create',
    name: 'IngredientInspectionForm',
    component: () => import('@/views/IngredientInspectionForm.vue')
  },
  {
    path: '/ingredientinspection/:id/edit',
    name: 'IngredientInspectionEdit',
    component: () => import('@/views/IngredientInspectionForm.vue')
  },
  {
    path: '/ingredientinspection/:id',
    name: 'IngredientInspectionDetail',
    component: () => import('@/views/IngredientInspectionDetail.vue')
  },
  
  // StockOut 路由
  {
    path: '/stockout',
    name: 'StockOutList',
    component: () => import('@/views/StockOutList.vue')
  },
  {
    path: '/stockout/create',
    name: 'StockOutForm',
    component: () => import('@/views/StockOutForm.vue')
  },
  {
    path: '/stockout/:id/edit',
    name: 'StockOutEdit',
    component: () => import('@/views/StockOutForm.vue')
  },
  {
    path: '/stockout/:id',
    name: 'StockOutDetail',
    component: () => import('@/views/StockOutDetail.vue')
  },
  
  // StockOutItem 路由
  {
    path: '/stockoutitem',
    name: 'StockOutItemList',
    component: () => import('@/views/StockOutItemList.vue')
  },
  {
    path: '/stockoutitem/create',
    name: 'StockOutItemForm',
    component: () => import('@/views/StockOutItemForm.vue')
  },
  {
    path: '/stockoutitem/:id/edit',
    name: 'StockOutItemEdit',
    component: () => import('@/views/StockOutItemForm.vue')
  },
  {
    path: '/stockoutitem/:id',
    name: 'StockOutItemDetail',
    component: () => import('@/views/StockOutItemDetail.vue')
  },
  
  // InventoryCheck 路由
  {
    path: '/inventorycheck',
    name: 'InventoryCheckList',
    component: () => import('@/views/InventoryCheckList.vue')
  },
  {
    path: '/inventorycheck/create',
    name: 'InventoryCheckForm',
    component: () => import('@/views/InventoryCheckForm.vue')
  },
  {
    path: '/inventorycheck/:id/edit',
    name: 'InventoryCheckEdit',
    component: () => import('@/views/InventoryCheckForm.vue')
  },
  {
    path: '/inventorycheck/:id',
    name: 'InventoryCheckDetail',
    component: () => import('@/views/InventoryCheckDetail.vue')
  },
  
  // InventoryCheckItem 路由
  {
    path: '/inventorycheckitem',
    name: 'InventoryCheckItemList',
    component: () => import('@/views/InventoryCheckItemList.vue')
  },
  {
    path: '/inventorycheckitem/create',
    name: 'InventoryCheckItemForm',
    component: () => import('@/views/InventoryCheckItemForm.vue')
  },
  {
    path: '/inventorycheckitem/:id/edit',
    name: 'InventoryCheckItemEdit',
    component: () => import('@/views/InventoryCheckItemForm.vue')
  },
  {
    path: '/inventorycheckitem/:id',
    name: 'InventoryCheckItemDetail',
    component: () => import('@/views/InventoryCheckItemDetail.vue')
  },
  
  // Employee 路由
  {
    path: '/employee',
    name: 'EmployeeList',
    component: () => import('@/views/EmployeeList.vue')
  },
  {
    path: '/employee/create',
    name: 'EmployeeForm',
    component: () => import('@/views/EmployeeForm.vue')
  },
  {
    path: '/employee/:id/edit',
    name: 'EmployeeEdit',
    component: () => import('@/views/EmployeeForm.vue')
  },
  {
    path: '/employee/:id',
    name: 'EmployeeDetail',
    component: () => import('@/views/EmployeeDetail.vue')
  },
  
  // HealthCertificate 路由
  {
    path: '/healthcertificate',
    name: 'HealthCertificateList',
    component: () => import('@/views/HealthCertificateList.vue')
  },
  {
    path: '/healthcertificate/create',
    name: 'HealthCertificateForm',
    component: () => import('@/views/HealthCertificateForm.vue')
  },
  {
    path: '/healthcertificate/:id/edit',
    name: 'HealthCertificateEdit',
    component: () => import('@/views/HealthCertificateForm.vue')
  },
  {
    path: '/healthcertificate/:id',
    name: 'HealthCertificateDetail',
    component: () => import('@/views/HealthCertificateDetail.vue')
  },
  
  // MedicalExamination 路由
  {
    path: '/medicalexamination',
    name: 'MedicalExaminationList',
    component: () => import('@/views/MedicalExaminationList.vue')
  },
  {
    path: '/medicalexamination/create',
    name: 'MedicalExaminationForm',
    component: () => import('@/views/MedicalExaminationForm.vue')
  },
  {
    path: '/medicalexamination/:id/edit',
    name: 'MedicalExaminationEdit',
    component: () => import('@/views/MedicalExaminationForm.vue')
  },
  {
    path: '/medicalexamination/:id',
    name: 'MedicalExaminationDetail',
    component: () => import('@/views/MedicalExaminationDetail.vue')
  },
  
  // DailyHealthCheck 路由
  {
    path: '/dailyhealthcheck',
    name: 'DailyHealthCheckList',
    component: () => import('@/views/DailyHealthCheckList.vue')
  },
  {
    path: '/dailyhealthcheck/create',
    name: 'DailyHealthCheckForm',
    component: () => import('@/views/DailyHealthCheckForm.vue')
  },
  {
    path: '/dailyhealthcheck/:id/edit',
    name: 'DailyHealthCheckEdit',
    component: () => import('@/views/DailyHealthCheckForm.vue')
  },
  {
    path: '/dailyhealthcheck/:id',
    name: 'DailyHealthCheckDetail',
    component: () => import('@/views/DailyHealthCheckDetail.vue')
  },
  
  // TrainingRecord 路由
  {
    path: '/trainingrecord',
    name: 'TrainingRecordList',
    component: () => import('@/views/TrainingRecordList.vue')
  },
  {
    path: '/trainingrecord/create',
    name: 'TrainingRecordForm',
    component: () => import('@/views/TrainingRecordForm.vue')
  },
  {
    path: '/trainingrecord/:id/edit',
    name: 'TrainingRecordEdit',
    component: () => import('@/views/TrainingRecordForm.vue')
  },
  {
    path: '/trainingrecord/:id',
    name: 'TrainingRecordDetail',
    component: () => import('@/views/TrainingRecordDetail.vue')
  },
  
  // AdministrativeArea 路由
  {
    path: '/administrativearea',
    name: 'AdministrativeAreaList',
    component: () => import('@/views/AdministrativeAreaList.vue')
  },
  {
    path: '/administrativearea/create',
    name: 'AdministrativeAreaForm',
    component: () => import('@/views/AdministrativeAreaForm.vue')
  },
  {
    path: '/administrativearea/:id/edit',
    name: 'AdministrativeAreaEdit',
    component: () => import('@/views/AdministrativeAreaForm.vue')
  },
  {
    path: '/administrativearea/:id',
    name: 'AdministrativeAreaDetail',
    component: () => import('@/views/AdministrativeAreaDetail.vue')
  },
  
  // AreaChangeHistory 路由
  {
    path: '/areachangehistory',
    name: 'AreaChangeHistoryList',
    component: () => import('@/views/AreaChangeHistoryList.vue')
  },
  {
    path: '/areachangehistory/create',
    name: 'AreaChangeHistoryForm',
    component: () => import('@/views/AreaChangeHistoryForm.vue')
  },
  {
    path: '/areachangehistory/:id/edit',
    name: 'AreaChangeHistoryEdit',
    component: () => import('@/views/AreaChangeHistoryForm.vue')
  },
  {
    path: '/areachangehistory/:id',
    name: 'AreaChangeHistoryDetail',
    component: () => import('@/views/AreaChangeHistoryDetail.vue')
  },
  
  // RecipeProcess 路由
  {
    path: '/recipeprocess',
    name: 'RecipeProcessList',
    component: () => import('@/views/RecipeProcessList.vue')
  },
  {
    path: '/recipeprocess/create',
    name: 'RecipeProcessForm',
    component: () => import('@/views/RecipeProcessForm.vue')
  },
  {
    path: '/recipeprocess/:id/edit',
    name: 'RecipeProcessEdit',
    component: () => import('@/views/RecipeProcessForm.vue')
  },
  {
    path: '/recipeprocess/:id',
    name: 'RecipeProcessDetail',
    component: () => import('@/views/RecipeProcessDetail.vue')
  },
  
  // RecipeProcessIngredient 路由
  {
    path: '/recipeprocessingredient',
    name: 'RecipeProcessIngredientList',
    component: () => import('@/views/RecipeProcessIngredientList.vue')
  },
  {
    path: '/recipeprocessingredient/create',
    name: 'RecipeProcessIngredientForm',
    component: () => import('@/views/RecipeProcessIngredientForm.vue')
  },
  {
    path: '/recipeprocessingredient/:id/edit',
    name: 'RecipeProcessIngredientEdit',
    component: () => import('@/views/RecipeProcessIngredientForm.vue')
  },
  {
    path: '/recipeprocessingredient/:id',
    name: 'RecipeProcessIngredientDetail',
    component: () => import('@/views/RecipeProcessIngredientDetail.vue')
  },
  
  // AuditLog 路由
  {
    path: '/auditlog',
    name: 'AuditLogList',
    component: () => import('@/views/AuditLogList.vue')
  },
  {
    path: '/auditlog/create',
    name: 'AuditLogForm',
    component: () => import('@/views/AuditLogForm.vue')
  },
  {
    path: '/auditlog/:id/edit',
    name: 'AuditLogEdit',
    component: () => import('@/views/AuditLogForm.vue')
  },
  {
    path: '/auditlog/:id',
    name: 'AuditLogDetail',
    component: () => import('@/views/AuditLogDetail.vue')
  },
  
  // InventoryAlert 路由
  {
    path: '/inventoryalert',
    name: 'InventoryAlertList',
    component: () => import('@/views/InventoryAlertList.vue')
  },
  {
    path: '/inventoryalert/create',
    name: 'InventoryAlertForm',
    component: () => import('@/views/InventoryAlertForm.vue')
  },
  {
    path: '/inventoryalert/:id/edit',
    name: 'InventoryAlertEdit',
    component: () => import('@/views/InventoryAlertForm.vue')
  },
  {
    path: '/inventoryalert/:id',
    name: 'InventoryAlertDetail',
    component: () => import('@/views/InventoryAlertDetail.vue')
  },
  
  // WeeklyMenu 路由
  {
    path: '/weeklymenu',
    name: 'WeeklyMenuList',
    component: () => import('@/views/WeeklyMenuList.vue')
  },
  {
    path: '/weeklymenu/create',
    name: 'WeeklyMenuForm',
    component: () => import('@/views/WeeklyMenuForm.vue')
  },
  {
    path: '/weeklymenu/:id/edit',
    name: 'WeeklyMenuEdit',
    component: () => import('@/views/WeeklyMenuForm.vue')
  },
  {
    path: '/weeklymenu/:id',
    name: 'WeeklyMenuDetail',
    component: () => import('@/views/WeeklyMenuDetail.vue')
  },
  
  // WeeklyMenuRecipe 路由
  {
    path: '/weeklymenurecipe',
    name: 'WeeklyMenuRecipeList',
    component: () => import('@/views/WeeklyMenuRecipeList.vue')
  },
  {
    path: '/weeklymenurecipe/create',
    name: 'WeeklyMenuRecipeForm',
    component: () => import('@/views/WeeklyMenuRecipeForm.vue')
  },
  {
    path: '/weeklymenurecipe/:id/edit',
    name: 'WeeklyMenuRecipeEdit',
    component: () => import('@/views/WeeklyMenuRecipeForm.vue')
  },
  {
    path: '/weeklymenurecipe/:id',
    name: 'WeeklyMenuRecipeDetail',
    component: () => import('@/views/WeeklyMenuRecipeDetail.vue')
  },
  
  // Notification 路由
  {
    path: '/notification',
    name: 'NotificationList',
    component: () => import('@/views/NotificationList.vue')
  },
  {
    path: '/notification/create',
    name: 'NotificationForm',
    component: () => import('@/views/NotificationForm.vue')
  },
  {
    path: '/notification/:id/edit',
    name: 'NotificationEdit',
    component: () => import('@/views/NotificationForm.vue')
  },
  {
    path: '/notification/:id',
    name: 'NotificationDetail',
    component: () => import('@/views/NotificationDetail.vue')
  },
  
  // OnlineConsultation 路由
  {
    path: '/onlineconsultation',
    name: 'OnlineConsultationList',
    component: () => import('@/views/OnlineConsultationList.vue')
  },
  {
    path: '/onlineconsultation/create',
    name: 'OnlineConsultationForm',
    component: () => import('@/views/OnlineConsultationForm.vue')
  },
  {
    path: '/onlineconsultation/:id/edit',
    name: 'OnlineConsultationEdit',
    component: () => import('@/views/OnlineConsultationForm.vue')
  },
  {
    path: '/onlineconsultation/:id',
    name: 'OnlineConsultationDetail',
    component: () => import('@/views/OnlineConsultationDetail.vue')
  },
  
  // DailyLog 路由
  {
    path: '/dailylog',
    name: 'DailyLogList',
    component: () => import('@/views/DailyLogList.vue')
  },
  {
    path: '/dailylog/create',
    name: 'DailyLogForm',
    component: () => import('@/views/DailyLogForm.vue')
  },
  {
    path: '/dailylog/:id/edit',
    name: 'DailyLogEdit',
    component: () => import('@/views/DailyLogForm.vue')
  },
  {
    path: '/dailylog/:id',
    name: 'DailyLogDetail',
    component: () => import('@/views/DailyLogDetail.vue')
  },
  
  // InspectionRecord 路由
  {
    path: '/inspectionrecord',
    name: 'InspectionRecordList',
    component: () => import('@/views/InspectionRecordList.vue')
  },
  {
    path: '/inspectionrecord/create',
    name: 'InspectionRecordForm',
    component: () => import('@/views/InspectionRecordForm.vue')
  },
  {
    path: '/inspectionrecord/:id/edit',
    name: 'InspectionRecordEdit',
    component: () => import('@/views/InspectionRecordForm.vue')
  },
  {
    path: '/inspectionrecord/:id',
    name: 'InspectionRecordDetail',
    component: () => import('@/views/InspectionRecordDetail.vue')
  },
  
  // DiningCompanion 路由
  {
    path: '/diningcompanion',
    name: 'DiningCompanionList',
    component: () => import('@/views/DiningCompanionList.vue')
  },
  {
    path: '/diningcompanion/create',
    name: 'DiningCompanionForm',
    component: () => import('@/views/DiningCompanionForm.vue')
  },
  {
    path: '/diningcompanion/:id/edit',
    name: 'DiningCompanionEdit',
    component: () => import('@/views/DiningCompanionForm.vue')
  },
  {
    path: '/diningcompanion/:id',
    name: 'DiningCompanionDetail',
    component: () => import('@/views/DiningCompanionDetail.vue')
  },
  
  // CanteenTrainingRecord 路由
  {
    path: '/canteentrainingrecord',
    name: 'CanteenTrainingRecordList',
    component: () => import('@/views/CanteenTrainingRecordList.vue')
  },
  {
    path: '/canteentrainingrecord/create',
    name: 'CanteenTrainingRecordForm',
    component: () => import('@/views/CanteenTrainingRecordForm.vue')
  },
  {
    path: '/canteentrainingrecord/:id/edit',
    name: 'CanteenTrainingRecordEdit',
    component: () => import('@/views/CanteenTrainingRecordForm.vue')
  },
  {
    path: '/canteentrainingrecord/:id',
    name: 'CanteenTrainingRecordDetail',
    component: () => import('@/views/CanteenTrainingRecordDetail.vue')
  },
  
  // SpecialEvent 路由
  {
    path: '/specialevent',
    name: 'SpecialEventList',
    component: () => import('@/views/SpecialEventList.vue')
  },
  {
    path: '/specialevent/create',
    name: 'SpecialEventForm',
    component: () => import('@/views/SpecialEventForm.vue')
  },
  {
    path: '/specialevent/:id/edit',
    name: 'SpecialEventEdit',
    component: () => import('@/views/SpecialEventForm.vue')
  },
  {
    path: '/specialevent/:id',
    name: 'SpecialEventDetail',
    component: () => import('@/views/SpecialEventDetail.vue')
  },
  
  // Issue 路由
  {
    path: '/issue',
    name: 'IssueList',
    component: () => import('@/views/IssueList.vue')
  },
  {
    path: '/issue/create',
    name: 'IssueForm',
    component: () => import('@/views/IssueForm.vue')
  },
  {
    path: '/issue/:id/edit',
    name: 'IssueEdit',
    component: () => import('@/views/IssueForm.vue')
  },
  {
    path: '/issue/:id',
    name: 'IssueDetail',
    component: () => import('@/views/IssueDetail.vue')
  },
  
  // Photo 路由
  {
    path: '/photo',
    name: 'PhotoList',
    component: () => import('@/views/PhotoList.vue')
  },
  {
    path: '/photo/create',
    name: 'PhotoForm',
    component: () => import('@/views/PhotoForm.vue')
  },
  {
    path: '/photo/:id/edit',
    name: 'PhotoEdit',
    component: () => import('@/views/PhotoForm.vue')
  },
  {
    path: '/photo/:id',
    name: 'PhotoDetail',
    component: () => import('@/views/PhotoDetail.vue')
  },
  
  // InspectionTemplate 路由
  {
    path: '/inspectiontemplate',
    name: 'InspectionTemplateList',
    component: () => import('@/views/InspectionTemplateList.vue')
  },
  {
    path: '/inspectiontemplate/create',
    name: 'InspectionTemplateForm',
    component: () => import('@/views/InspectionTemplateForm.vue')
  },
  {
    path: '/inspectiontemplate/:id/edit',
    name: 'InspectionTemplateEdit',
    component: () => import('@/views/InspectionTemplateForm.vue')
  },
  {
    path: '/inspectiontemplate/:id',
    name: 'InspectionTemplateDetail',
    component: () => import('@/views/InspectionTemplateDetail.vue')
  },
  
  // VoucherDetail 路由
  {
    path: '/voucherdetail',
    name: 'VoucherDetailList',
    component: () => import('@/views/VoucherDetailList.vue')
  },
  {
    path: '/voucherdetail/create',
    name: 'VoucherDetailForm',
    component: () => import('@/views/VoucherDetailForm.vue')
  },
  {
    path: '/voucherdetail/:id/edit',
    name: 'VoucherDetailEdit',
    component: () => import('@/views/VoucherDetailForm.vue')
  },
  {
    path: '/voucherdetail/:id',
    name: 'VoucherDetailDetail',
    component: () => import('@/views/VoucherDetailDetail.vue')
  },
  
  // HomepageCarousel 路由
  {
    path: '/homepagecarousel',
    name: 'HomepageCarouselList',
    component: () => import('@/views/HomepageCarouselList.vue')
  },
  {
    path: '/homepagecarousel/create',
    name: 'HomepageCarouselForm',
    component: () => import('@/views/HomepageCarouselForm.vue')
  },
  {
    path: '/homepagecarousel/:id/edit',
    name: 'HomepageCarouselEdit',
    component: () => import('@/views/HomepageCarouselForm.vue')
  },
  {
    path: '/homepagecarousel/:id',
    name: 'HomepageCarouselDetail',
    component: () => import('@/views/HomepageCarouselDetail.vue')
  },
  
  // MaterialBatch 路由
  {
    path: '/materialbatch',
    name: 'MaterialBatchList',
    component: () => import('@/views/MaterialBatchList.vue')
  },
  {
    path: '/materialbatch/create',
    name: 'MaterialBatchForm',
    component: () => import('@/views/MaterialBatchForm.vue')
  },
  {
    path: '/materialbatch/:id/edit',
    name: 'MaterialBatchEdit',
    component: () => import('@/views/MaterialBatchForm.vue')
  },
  {
    path: '/materialbatch/:id',
    name: 'MaterialBatchDetail',
    component: () => import('@/views/MaterialBatchDetail.vue')
  },
  
  // TraceDocument 路由
  {
    path: '/tracedocument',
    name: 'TraceDocumentList',
    component: () => import('@/views/TraceDocumentList.vue')
  },
  {
    path: '/tracedocument/create',
    name: 'TraceDocumentForm',
    component: () => import('@/views/TraceDocumentForm.vue')
  },
  {
    path: '/tracedocument/:id/edit',
    name: 'TraceDocumentEdit',
    component: () => import('@/views/TraceDocumentForm.vue')
  },
  {
    path: '/tracedocument/:id',
    name: 'TraceDocumentDetail',
    component: () => import('@/views/TraceDocumentDetail.vue')
  },
  
  // BatchFlow 路由
  {
    path: '/batchflow',
    name: 'BatchFlowList',
    component: () => import('@/views/BatchFlowList.vue')
  },
  {
    path: '/batchflow/create',
    name: 'BatchFlowForm',
    component: () => import('@/views/BatchFlowForm.vue')
  },
  {
    path: '/batchflow/:id/edit',
    name: 'BatchFlowEdit',
    component: () => import('@/views/BatchFlowForm.vue')
  },
  {
    path: '/batchflow/:id',
    name: 'BatchFlowDetail',
    component: () => import('@/views/BatchFlowDetail.vue')
  },
  
  // IngredientCategory 路由
  {
    path: '/ingredientcategory',
    name: 'IngredientCategoryList',
    component: () => import('@/views/IngredientCategoryList.vue')
  },
  {
    path: '/ingredientcategory/create',
    name: 'IngredientCategoryForm',
    component: () => import('@/views/IngredientCategoryForm.vue')
  },
  {
    path: '/ingredientcategory/:id/edit',
    name: 'IngredientCategoryEdit',
    component: () => import('@/views/IngredientCategoryForm.vue')
  },
  {
    path: '/ingredientcategory/:id',
    name: 'IngredientCategoryDetail',
    component: () => import('@/views/IngredientCategoryDetail.vue')
  },
  
  // RecipeCategory 路由
  {
    path: '/recipecategory',
    name: 'RecipeCategoryList',
    component: () => import('@/views/RecipeCategoryList.vue')
  },
  {
    path: '/recipecategory/create',
    name: 'RecipeCategoryForm',
    component: () => import('@/views/RecipeCategoryForm.vue')
  },
  {
    path: '/recipecategory/:id/edit',
    name: 'RecipeCategoryEdit',
    component: () => import('@/views/RecipeCategoryForm.vue')
  },
  {
    path: '/recipecategory/:id',
    name: 'RecipeCategoryDetail',
    component: () => import('@/views/RecipeCategoryDetail.vue')
  },
  
  // RecipeProcess 路由
  {
    path: '/recipeprocess',
    name: 'RecipeProcessList',
    component: () => import('@/views/RecipeProcessList.vue')
  },
  {
    path: '/recipeprocess/create',
    name: 'RecipeProcessForm',
    component: () => import('@/views/RecipeProcessForm.vue')
  },
  {
    path: '/recipeprocess/:id/edit',
    name: 'RecipeProcessEdit',
    component: () => import('@/views/RecipeProcessForm.vue')
  },
  {
    path: '/recipeprocess/:id',
    name: 'RecipeProcessDetail',
    component: () => import('@/views/RecipeProcessDetail.vue')
  },
  
  // RecipeProcessIngredient 路由
  {
    path: '/recipeprocessingredient',
    name: 'RecipeProcessIngredientList',
    component: () => import('@/views/RecipeProcessIngredientList.vue')
  },
  {
    path: '/recipeprocessingredient/create',
    name: 'RecipeProcessIngredientForm',
    component: () => import('@/views/RecipeProcessIngredientForm.vue')
  },
  {
    path: '/recipeprocessingredient/:id/edit',
    name: 'RecipeProcessIngredientEdit',
    component: () => import('@/views/RecipeProcessIngredientForm.vue')
  },
  {
    path: '/recipeprocessingredient/:id',
    name: 'RecipeProcessIngredientDetail',
    component: () => import('@/views/RecipeProcessIngredientDetail.vue')
  },
  
  // SupplierCertificate 路由
  {
    path: '/suppliercertificate',
    name: 'SupplierCertificateList',
    component: () => import('@/views/SupplierCertificateList.vue')
  },
  {
    path: '/suppliercertificate/create',
    name: 'SupplierCertificateForm',
    component: () => import('@/views/SupplierCertificateForm.vue')
  },
  {
    path: '/suppliercertificate/:id/edit',
    name: 'SupplierCertificateEdit',
    component: () => import('@/views/SupplierCertificateForm.vue')
  },
  {
    path: '/suppliercertificate/:id',
    name: 'SupplierCertificateDetail',
    component: () => import('@/views/SupplierCertificateDetail.vue')
  },
  
  // PurchaseRequisition 路由
  {
    path: '/purchaserequisition',
    name: 'PurchaseRequisitionList',
    component: () => import('@/views/PurchaseRequisitionList.vue')
  },
  {
    path: '/purchaserequisition/create',
    name: 'PurchaseRequisitionForm',
    component: () => import('@/views/PurchaseRequisitionForm.vue')
  },
  {
    path: '/purchaserequisition/:id/edit',
    name: 'PurchaseRequisitionEdit',
    component: () => import('@/views/PurchaseRequisitionForm.vue')
  },
  {
    path: '/purchaserequisition/:id',
    name: 'PurchaseRequisitionDetail',
    component: () => import('@/views/PurchaseRequisitionDetail.vue')
  },
  
  // PurchaseRequisitionItem 路由
  {
    path: '/purchaserequisitionitem',
    name: 'PurchaseRequisitionItemList',
    component: () => import('@/views/PurchaseRequisitionItemList.vue')
  },
  {
    path: '/purchaserequisitionitem/create',
    name: 'PurchaseRequisitionItemForm',
    component: () => import('@/views/PurchaseRequisitionItemForm.vue')
  },
  {
    path: '/purchaserequisitionitem/:id/edit',
    name: 'PurchaseRequisitionItemEdit',
    component: () => import('@/views/PurchaseRequisitionItemForm.vue')
  },
  {
    path: '/purchaserequisitionitem/:id',
    name: 'PurchaseRequisitionItemDetail',
    component: () => import('@/views/PurchaseRequisitionItemDetail.vue')
  },
  
  // SupplierDelivery 路由
  {
    path: '/supplierdelivery',
    name: 'SupplierDeliveryList',
    component: () => import('@/views/SupplierDeliveryList.vue')
  },
  {
    path: '/supplierdelivery/create',
    name: 'SupplierDeliveryForm',
    component: () => import('@/views/SupplierDeliveryForm.vue')
  },
  {
    path: '/supplierdelivery/:id/edit',
    name: 'SupplierDeliveryEdit',
    component: () => import('@/views/SupplierDeliveryForm.vue')
  },
  {
    path: '/supplierdelivery/:id',
    name: 'SupplierDeliveryDetail',
    component: () => import('@/views/SupplierDeliveryDetail.vue')
  },
  
  // DeliveryItem 路由
  {
    path: '/deliveryitem',
    name: 'DeliveryItemList',
    component: () => import('@/views/DeliveryItemList.vue')
  },
  {
    path: '/deliveryitem/create',
    name: 'DeliveryItemForm',
    component: () => import('@/views/DeliveryItemForm.vue')
  },
  {
    path: '/deliveryitem/:id/edit',
    name: 'DeliveryItemEdit',
    component: () => import('@/views/DeliveryItemForm.vue')
  },
  {
    path: '/deliveryitem/:id',
    name: 'DeliveryItemDetail',
    component: () => import('@/views/DeliveryItemDetail.vue')
  },
  
  // Warehouse 路由
  {
    path: '/warehouse',
    name: 'WarehouseList',
    component: () => import('@/views/WarehouseList.vue')
  },
  {
    path: '/warehouse/create',
    name: 'WarehouseForm',
    component: () => import('@/views/WarehouseForm.vue')
  },
  {
    path: '/warehouse/:id/edit',
    name: 'WarehouseEdit',
    component: () => import('@/views/WarehouseForm.vue')
  },
  {
    path: '/warehouse/:id',
    name: 'WarehouseDetail',
    component: () => import('@/views/WarehouseDetail.vue')
  },
  
  // StorageLocation 路由
  {
    path: '/storagelocation',
    name: 'StorageLocationList',
    component: () => import('@/views/StorageLocationList.vue')
  },
  {
    path: '/storagelocation/create',
    name: 'StorageLocationForm',
    component: () => import('@/views/StorageLocationForm.vue')
  },
  {
    path: '/storagelocation/:id/edit',
    name: 'StorageLocationEdit',
    component: () => import('@/views/StorageLocationForm.vue')
  },
  {
    path: '/storagelocation/:id',
    name: 'StorageLocationDetail',
    component: () => import('@/views/StorageLocationDetail.vue')
  },
  
  // Inventory 路由
  {
    path: '/inventory',
    name: 'InventoryList',
    component: () => import('@/views/InventoryList.vue')
  },
  {
    path: '/inventory/create',
    name: 'InventoryForm',
    component: () => import('@/views/InventoryForm.vue')
  },
  {
    path: '/inventory/:id/edit',
    name: 'InventoryEdit',
    component: () => import('@/views/InventoryForm.vue')
  },
  {
    path: '/inventory/:id',
    name: 'InventoryDetail',
    component: () => import('@/views/InventoryDetail.vue')
  },
  
  // StockIn 路由
  {
    path: '/stockin',
    name: 'StockInList',
    component: () => import('@/views/StockInList.vue')
  },
  {
    path: '/stockin/create',
    name: 'StockInForm',
    component: () => import('@/views/StockInForm.vue')
  },
  {
    path: '/stockin/:id/edit',
    name: 'StockInEdit',
    component: () => import('@/views/StockInForm.vue')
  },
  {
    path: '/stockin/:id',
    name: 'StockInDetail',
    component: () => import('@/views/StockInDetail.vue')
  },
  
  // StockInItem 路由
  {
    path: '/stockinitem',
    name: 'StockInItemList',
    component: () => import('@/views/StockInItemList.vue')
  },
  {
    path: '/stockinitem/create',
    name: 'StockInItemForm',
    component: () => import('@/views/StockInItemForm.vue')
  },
  {
    path: '/stockinitem/:id/edit',
    name: 'StockInItemEdit',
    component: () => import('@/views/StockInItemForm.vue')
  },
  {
    path: '/stockinitem/:id',
    name: 'StockInItemDetail',
    component: () => import('@/views/StockInItemDetail.vue')
  },
  
  // StockOut 路由
  {
    path: '/stockout',
    name: 'StockOutList',
    component: () => import('@/views/StockOutList.vue')
  },
  {
    path: '/stockout/create',
    name: 'StockOutForm',
    component: () => import('@/views/StockOutForm.vue')
  },
  {
    path: '/stockout/:id/edit',
    name: 'StockOutEdit',
    component: () => import('@/views/StockOutForm.vue')
  },
  {
    path: '/stockout/:id',
    name: 'StockOutDetail',
    component: () => import('@/views/StockOutDetail.vue')
  },
  
  // StockOutItem 路由
  {
    path: '/stockoutitem',
    name: 'StockOutItemList',
    component: () => import('@/views/StockOutItemList.vue')
  },
  {
    path: '/stockoutitem/create',
    name: 'StockOutItemForm',
    component: () => import('@/views/StockOutItemForm.vue')
  },
  {
    path: '/stockoutitem/:id/edit',
    name: 'StockOutItemEdit',
    component: () => import('@/views/StockOutItemForm.vue')
  },
  {
    path: '/stockoutitem/:id',
    name: 'StockOutItemDetail',
    component: () => import('@/views/StockOutItemDetail.vue')
  },
  
  // InventoryCheck 路由
  {
    path: '/inventorycheck',
    name: 'InventoryCheckList',
    component: () => import('@/views/InventoryCheckList.vue')
  },
  {
    path: '/inventorycheck/create',
    name: 'InventoryCheckForm',
    component: () => import('@/views/InventoryCheckForm.vue')
  },
  {
    path: '/inventorycheck/:id/edit',
    name: 'InventoryCheckEdit',
    component: () => import('@/views/InventoryCheckForm.vue')
  },
  {
    path: '/inventorycheck/:id',
    name: 'InventoryCheckDetail',
    component: () => import('@/views/InventoryCheckDetail.vue')
  },
  
  // InventoryCheckItem 路由
  {
    path: '/inventorycheckitem',
    name: 'InventoryCheckItemList',
    component: () => import('@/views/InventoryCheckItemList.vue')
  },
  {
    path: '/inventorycheckitem/create',
    name: 'InventoryCheckItemForm',
    component: () => import('@/views/InventoryCheckItemForm.vue')
  },
  {
    path: '/inventorycheckitem/:id/edit',
    name: 'InventoryCheckItemEdit',
    component: () => import('@/views/InventoryCheckItemForm.vue')
  },
  {
    path: '/inventorycheckitem/:id',
    name: 'InventoryCheckItemDetail',
    component: () => import('@/views/InventoryCheckItemDetail.vue')
  },
  
  // StandardUnit 路由
  {
    path: '/standardunit',
    name: 'StandardUnitList',
    component: () => import('@/views/StandardUnitList.vue')
  },
  {
    path: '/standardunit/create',
    name: 'StandardUnitForm',
    component: () => import('@/views/StandardUnitForm.vue')
  },
  {
    path: '/standardunit/:id/edit',
    name: 'StandardUnitEdit',
    component: () => import('@/views/StandardUnitForm.vue')
  },
  {
    path: '/standardunit/:id',
    name: 'StandardUnitDetail',
    component: () => import('@/views/StandardUnitDetail.vue')
  },
  
  // CategoryUnitMapping 路由
  {
    path: '/categoryunitmapping',
    name: 'CategoryUnitMappingList',
    component: () => import('@/views/CategoryUnitMappingList.vue')
  },
  {
    path: '/categoryunitmapping/create',
    name: 'CategoryUnitMappingForm',
    component: () => import('@/views/CategoryUnitMappingForm.vue')
  },
  {
    path: '/categoryunitmapping/:id/edit',
    name: 'CategoryUnitMappingEdit',
    component: () => import('@/views/CategoryUnitMappingForm.vue')
  },
  {
    path: '/categoryunitmapping/:id',
    name: 'CategoryUnitMappingDetail',
    component: () => import('@/views/CategoryUnitMappingDetail.vue')
  },
  
  // ProductBatch 路由
  {
    path: '/productbatch',
    name: 'ProductBatchList',
    component: () => import('@/views/ProductBatchList.vue')
  },
  {
    path: '/productbatch/create',
    name: 'ProductBatchForm',
    component: () => import('@/views/ProductBatchForm.vue')
  },
  {
    path: '/productbatch/:id/edit',
    name: 'ProductBatchEdit',
    component: () => import('@/views/ProductBatchForm.vue')
  },
  {
    path: '/productbatch/:id',
    name: 'ProductBatchDetail',
    component: () => import('@/views/ProductBatchDetail.vue')
  },
  
  // RecipeReview 路由
  {
    path: '/recipereview',
    name: 'RecipeReviewList',
    component: () => import('@/views/RecipeReviewList.vue')
  },
  {
    path: '/recipereview/create',
    name: 'RecipeReviewForm',
    component: () => import('@/views/RecipeReviewForm.vue')
  },
  {
    path: '/recipereview/:id/edit',
    name: 'RecipeReviewEdit',
    component: () => import('@/views/RecipeReviewForm.vue')
  },
  {
    path: '/recipereview/:id',
    name: 'RecipeReviewDetail',
    component: () => import('@/views/RecipeReviewDetail.vue')
  },
  
  // RecipeReviewImage 路由
  {
    path: '/recipereviewimage',
    name: 'RecipeReviewImageList',
    component: () => import('@/views/RecipeReviewImageList.vue')
  },
  {
    path: '/recipereviewimage/create',
    name: 'RecipeReviewImageForm',
    component: () => import('@/views/RecipeReviewImageForm.vue')
  },
  {
    path: '/recipereviewimage/:id/edit',
    name: 'RecipeReviewImageEdit',
    component: () => import('@/views/RecipeReviewImageForm.vue')
  },
  {
    path: '/recipereviewimage/:id',
    name: 'RecipeReviewImageDetail',
    component: () => import('@/views/RecipeReviewImageDetail.vue')
  },
  
  // RecipeReviewTag 路由
  {
    path: '/recipereviewtag',
    name: 'RecipeReviewTagList',
    component: () => import('@/views/RecipeReviewTagList.vue')
  },
  {
    path: '/recipereviewtag/create',
    name: 'RecipeReviewTagForm',
    component: () => import('@/views/RecipeReviewTagForm.vue')
  },
  {
    path: '/recipereviewtag/:id/edit',
    name: 'RecipeReviewTagEdit',
    component: () => import('@/views/RecipeReviewTagForm.vue')
  },
  {
    path: '/recipereviewtag/:id',
    name: 'RecipeReviewTagDetail',
    component: () => import('@/views/RecipeReviewTagDetail.vue')
  },
  
  // RecipeImprovementSuggestion 路由
  {
    path: '/recipeimprovementsuggestion',
    name: 'RecipeImprovementSuggestionList',
    component: () => import('@/views/RecipeImprovementSuggestionList.vue')
  },
  {
    path: '/recipeimprovementsuggestion/create',
    name: 'RecipeImprovementSuggestionForm',
    component: () => import('@/views/RecipeImprovementSuggestionForm.vue')
  },
  {
    path: '/recipeimprovementsuggestion/:id/edit',
    name: 'RecipeImprovementSuggestionEdit',
    component: () => import('@/views/RecipeImprovementSuggestionForm.vue')
  },
  {
    path: '/recipeimprovementsuggestion/:id',
    name: 'RecipeImprovementSuggestionDetail',
    component: () => import('@/views/RecipeImprovementSuggestionDetail.vue')
  },
  
  // RecipeVersion 路由
  {
    path: '/recipeversion',
    name: 'RecipeVersionList',
    component: () => import('@/views/RecipeVersionList.vue')
  },
  {
    path: '/recipeversion/create',
    name: 'RecipeVersionForm',
    component: () => import('@/views/RecipeVersionForm.vue')
  },
  {
    path: '/recipeversion/:id/edit',
    name: 'RecipeVersionEdit',
    component: () => import('@/views/RecipeVersionForm.vue')
  },
  {
    path: '/recipeversion/:id',
    name: 'RecipeVersionDetail',
    component: () => import('@/views/RecipeVersionDetail.vue')
  },
  
  // RecipeIngredientAlternative 路由
  {
    path: '/recipeingredientalternative',
    name: 'RecipeIngredientAlternativeList',
    component: () => import('@/views/RecipeIngredientAlternativeList.vue')
  },
  {
    path: '/recipeingredientalternative/create',
    name: 'RecipeIngredientAlternativeForm',
    component: () => import('@/views/RecipeIngredientAlternativeForm.vue')
  },
  {
    path: '/recipeingredientalternative/:id/edit',
    name: 'RecipeIngredientAlternativeEdit',
    component: () => import('@/views/RecipeIngredientAlternativeForm.vue')
  },
  {
    path: '/recipeingredientalternative/:id',
    name: 'RecipeIngredientAlternativeDetail',
    component: () => import('@/views/RecipeIngredientAlternativeDetail.vue')
  },
  
  // RecipeSeasonalInfo 路由
  {
    path: '/recipeseasonalinfo',
    name: 'RecipeSeasonalInfoList',
    component: () => import('@/views/RecipeSeasonalInfoList.vue')
  },
  {
    path: '/recipeseasonalinfo/create',
    name: 'RecipeSeasonalInfoForm',
    component: () => import('@/views/RecipeSeasonalInfoForm.vue')
  },
  {
    path: '/recipeseasonalinfo/:id/edit',
    name: 'RecipeSeasonalInfoEdit',
    component: () => import('@/views/RecipeSeasonalInfoForm.vue')
  },
  {
    path: '/recipeseasonalinfo/:id',
    name: 'RecipeSeasonalInfoDetail',
    component: () => import('@/views/RecipeSeasonalInfoDetail.vue')
  },
  
  // RecipeTag 路由
  {
    path: '/recipetag',
    name: 'RecipeTagList',
    component: () => import('@/views/RecipeTagList.vue')
  },
  {
    path: '/recipetag/create',
    name: 'RecipeTagForm',
    component: () => import('@/views/RecipeTagForm.vue')
  },
  {
    path: '/recipetag/:id/edit',
    name: 'RecipeTagEdit',
    component: () => import('@/views/RecipeTagForm.vue')
  },
  {
    path: '/recipetag/:id',
    name: 'RecipeTagDetail',
    component: () => import('@/views/RecipeTagDetail.vue')
  },
  
  // UserRecipeFavorite 路由
  {
    path: '/userrecipefavorite',
    name: 'UserRecipeFavoriteList',
    component: () => import('@/views/UserRecipeFavoriteList.vue')
  },
  {
    path: '/userrecipefavorite/create',
    name: 'UserRecipeFavoriteForm',
    component: () => import('@/views/UserRecipeFavoriteForm.vue')
  },
  {
    path: '/userrecipefavorite/:id/edit',
    name: 'UserRecipeFavoriteEdit',
    component: () => import('@/views/UserRecipeFavoriteForm.vue')
  },
  {
    path: '/userrecipefavorite/:id',
    name: 'UserRecipeFavoriteDetail',
    component: () => import('@/views/UserRecipeFavoriteDetail.vue')
  },
  
  // UserSearchHistory 路由
  {
    path: '/usersearchhistory',
    name: 'UserSearchHistoryList',
    component: () => import('@/views/UserSearchHistoryList.vue')
  },
  {
    path: '/usersearchhistory/create',
    name: 'UserSearchHistoryForm',
    component: () => import('@/views/UserSearchHistoryForm.vue')
  },
  {
    path: '/usersearchhistory/:id/edit',
    name: 'UserSearchHistoryEdit',
    component: () => import('@/views/UserSearchHistoryForm.vue')
  },
  {
    path: '/usersearchhistory/:id',
    name: 'UserSearchHistoryDetail',
    component: () => import('@/views/UserSearchHistoryDetail.vue')
  },
  
  // SupplierSchoolRelation 路由
  {
    path: '/supplierschoolrelation',
    name: 'SupplierSchoolRelationList',
    component: () => import('@/views/SupplierSchoolRelationList.vue')
  },
  {
    path: '/supplierschoolrelation/create',
    name: 'SupplierSchoolRelationForm',
    component: () => import('@/views/SupplierSchoolRelationForm.vue')
  },
  {
    path: '/supplierschoolrelation/:id/edit',
    name: 'SupplierSchoolRelationEdit',
    component: () => import('@/views/SupplierSchoolRelationForm.vue')
  },
  {
    path: '/supplierschoolrelation/:id',
    name: 'SupplierSchoolRelationDetail',
    component: () => import('@/views/SupplierSchoolRelationDetail.vue')
  },
  
  // ProductSpecParameter 路由
  {
    path: '/productspecparameter',
    name: 'ProductSpecParameterList',
    component: () => import('@/views/ProductSpecParameterList.vue')
  },
  {
    path: '/productspecparameter/create',
    name: 'ProductSpecParameterForm',
    component: () => import('@/views/ProductSpecParameterForm.vue')
  },
  {
    path: '/productspecparameter/:id/edit',
    name: 'ProductSpecParameterEdit',
    component: () => import('@/views/ProductSpecParameterForm.vue')
  },
  {
    path: '/productspecparameter/:id',
    name: 'ProductSpecParameterDetail',
    component: () => import('@/views/ProductSpecParameterDetail.vue')
  },
  
  // DeliveryInspection 路由
  {
    path: '/deliveryinspection',
    name: 'DeliveryInspectionList',
    component: () => import('@/views/DeliveryInspectionList.vue')
  },
  {
    path: '/deliveryinspection/create',
    name: 'DeliveryInspectionForm',
    component: () => import('@/views/DeliveryInspectionForm.vue')
  },
  {
    path: '/deliveryinspection/:id/edit',
    name: 'DeliveryInspectionEdit',
    component: () => import('@/views/DeliveryInspectionForm.vue')
  },
  {
    path: '/deliveryinspection/:id',
    name: 'DeliveryInspectionDetail',
    component: () => import('@/views/DeliveryInspectionDetail.vue')
  },
  
  // DeliveryItemInspection 路由
  {
    path: '/deliveryiteminspection',
    name: 'DeliveryItemInspectionList',
    component: () => import('@/views/DeliveryItemInspectionList.vue')
  },
  {
    path: '/deliveryiteminspection/create',
    name: 'DeliveryItemInspectionForm',
    component: () => import('@/views/DeliveryItemInspectionForm.vue')
  },
  {
    path: '/deliveryiteminspection/:id/edit',
    name: 'DeliveryItemInspectionEdit',
    component: () => import('@/views/DeliveryItemInspectionForm.vue')
  },
  {
    path: '/deliveryiteminspection/:id',
    name: 'DeliveryItemInspectionDetail',
    component: () => import('@/views/DeliveryItemInspectionDetail.vue')
  },
  
  // SystemSetting 路由
  {
    path: '/systemsetting',
    name: 'SystemSettingList',
    component: () => import('@/views/SystemSettingList.vue')
  },
  {
    path: '/systemsetting/create',
    name: 'SystemSettingForm',
    component: () => import('@/views/SystemSettingForm.vue')
  },
  {
    path: '/systemsetting/:id/edit',
    name: 'SystemSettingEdit',
    component: () => import('@/views/SystemSettingForm.vue')
  },
  {
    path: '/systemsetting/:id',
    name: 'SystemSettingDetail',
    component: () => import('@/views/SystemSettingDetail.vue')
  },
  
  // DatabaseBackup 路由
  {
    path: '/databasebackup',
    name: 'DatabaseBackupList',
    component: () => import('@/views/DatabaseBackupList.vue')
  },
  {
    path: '/databasebackup/create',
    name: 'DatabaseBackupForm',
    component: () => import('@/views/DatabaseBackupForm.vue')
  },
  {
    path: '/databasebackup/:id/edit',
    name: 'DatabaseBackupEdit',
    component: () => import('@/views/DatabaseBackupForm.vue')
  },
  {
    path: '/databasebackup/:id',
    name: 'DatabaseBackupDetail',
    component: () => import('@/views/DatabaseBackupDetail.vue')
  },
  
  // SystemLog 路由
  {
    path: '/systemlog',
    name: 'SystemLogList',
    component: () => import('@/views/SystemLogList.vue')
  },
  {
    path: '/systemlog/create',
    name: 'SystemLogForm',
    component: () => import('@/views/SystemLogForm.vue')
  },
  {
    path: '/systemlog/:id/edit',
    name: 'SystemLogEdit',
    component: () => import('@/views/SystemLogForm.vue')
  },
  {
    path: '/systemlog/:id',
    name: 'SystemLogDetail',
    component: () => import('@/views/SystemLogDetail.vue')
  },
  
  // ModuleVisibility 路由
  {
    path: '/modulevisibility',
    name: 'ModuleVisibilityList',
    component: () => import('@/views/ModuleVisibilityList.vue')
  },
  {
    path: '/modulevisibility/create',
    name: 'ModuleVisibilityForm',
    component: () => import('@/views/ModuleVisibilityForm.vue')
  },
  {
    path: '/modulevisibility/:id/edit',
    name: 'ModuleVisibilityEdit',
    component: () => import('@/views/ModuleVisibilityForm.vue')
  },
  {
    path: '/modulevisibility/:id',
    name: 'ModuleVisibilityDetail',
    component: () => import('@/views/ModuleVisibilityDetail.vue')
  },
  
  // WarehouseNew 路由
  {
    path: '/warehousenew',
    name: 'WarehouseNewList',
    component: () => import('@/views/WarehouseNewList.vue')
  },
  {
    path: '/warehousenew/create',
    name: 'WarehouseNewForm',
    component: () => import('@/views/WarehouseNewForm.vue')
  },
  {
    path: '/warehousenew/:id/edit',
    name: 'WarehouseNewEdit',
    component: () => import('@/views/WarehouseNewForm.vue')
  },
  {
    path: '/warehousenew/:id',
    name: 'WarehouseNewDetail',
    component: () => import('@/views/WarehouseNewDetail.vue')
  },
  
  // WeeklyMenuRecipesTemp 路由
  {
    path: '/weeklymenurecipestemp',
    name: 'WeeklyMenuRecipesTempList',
    component: () => import('@/views/WeeklyMenuRecipesTempList.vue')
  },
  {
    path: '/weeklymenurecipestemp/create',
    name: 'WeeklyMenuRecipesTempForm',
    component: () => import('@/views/WeeklyMenuRecipesTempForm.vue')
  },
  {
    path: '/weeklymenurecipestemp/:id/edit',
    name: 'WeeklyMenuRecipesTempEdit',
    component: () => import('@/views/WeeklyMenuRecipesTempForm.vue')
  },
  {
    path: '/weeklymenurecipestemp/:id',
    name: 'WeeklyMenuRecipesTempDetail',
    component: () => import('@/views/WeeklyMenuRecipesTempDetail.vue')
  },
  
]

const router = createRouter({
  history: createWebHistory('/new/'),
  routes
})

export default router
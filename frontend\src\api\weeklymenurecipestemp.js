import request from '@/utils/request'

const weeklymenurecipestempAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/weeklymenurecipestemp',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/weeklymenurecipestemp/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/weeklymenurecipestemp',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/weeklymenurecipestemp/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/weeklymenurecipestemp/${id}`,
      method: 'delete'
    })
  }
}

export default weeklymenurecipestempAPI
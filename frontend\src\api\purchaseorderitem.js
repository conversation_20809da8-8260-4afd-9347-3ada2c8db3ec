import request from '@/utils/request'

const purchaseorderitemAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/purchaseorderitem',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/purchaseorderitem/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/purchaseorderitem',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/purchaseorderitem/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/purchaseorderitem/${id}`,
      method: 'delete'
    })
  }
}

export default purchaseorderitemAPI
import { defineStore } from 'pinia'
import deliveryiteminspectionAPI from '@/api/deliveryiteminspection'

export const useDeliveryItemInspectionStore = defineStore('deliveryiteminspection', {
  state: () => ({
    list: [],
    total: 0,
    loading: false,
    current: null
  }),

  actions: {
    // 获取列表
    async fetchList(params = {}) {
      this.loading = true
      try {
        const response = await deliveryiteminspectionAPI.getList(params)
        this.list = response.data.items
        this.total = response.data.total
        return response.data
      } catch (error) {
        throw error
      } finally {
        this.loading = false
      }
    },

    // 获取详情
    async fetchById(id) {
      try {
        const response = await deliveryiteminspectionAPI.getById(id)
        this.current = response.data
        return response.data
      } catch (error) {
        throw error
      }
    },

    // 创建
    async create(data) {
      try {
        const response = await deliveryiteminspectionAPI.create(data)
        return response.data
      } catch (error) {
        throw error
      }
    },

    // 更新
    async update(id, data) {
      try {
        const response = await deliveryiteminspectionAPI.update(id, data)
        return response.data
      } catch (error) {
        throw error
      }
    },

    // 删除
    async delete(id) {
      try {
        await deliveryiteminspectionAPI.delete(id)
        // 从列表中移除
        const index = this.list.findIndex(item => item.id === id)
        if (index > -1) {
          this.list.splice(index, 1)
          this.total--
        }
        return true
      } catch (error) {
        throw error
      }
    }
  }
})
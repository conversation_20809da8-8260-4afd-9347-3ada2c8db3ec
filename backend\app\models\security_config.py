"""
安全配置模块
用于防护TLS/SSL攻击和恶意扫描
"""

from flask import request, abort
import time
from collections import defaultdict, deque
import ipaddress

# 存储IP访问记录
ip_access_log = defaultdict(lambda: deque(maxlen=100))
blocked_ips = set()

# 白名单IP地址 - 这些IP不会被安全检查拦截
WHITELIST_IPS = {
    '**************',  # 用户自己的IP地址
    '***************', # 用户当前IP地址
    '**************',  # 用户当前IP地址
    '127.0.0.1',       # 本地回环地址 - 完全安全
    '::1',             # IPv6本地回环地址
    'localhost',       # 本地主机名
    '0.0.0.0',         # 所有接口
    '***********',     # 常见的本地网关
    '********',        # 私有网络
    '**********',      # 私有网络
}

# 可疑User-Agent列表
SUSPICIOUS_USER_AGENTS = [
    'nmap', 'masscan', 'zmap', 'sqlmap', 'nikto', 'dirb', 'gobuster',
    'wfuzz', 'burp', 'scanner', 'bot', 'crawler', 'spider', 'exploit'
]

# 可疑请求路径 - 移除/admin，因为这是我们的合法管理路径
SUSPICIOUS_PATHS = [
    '/wp-admin', '/phpmyadmin', '/.env', '/config',
    '/backup', '/test', '/debug', '/xmlrpc.php',
    # 只阻止特定的恶意admin路径，而不是所有admin路径
    '/admin/config.php', '/admin/login.php', '/admin/index.php',
    '/admin/admin.php', '/admin/setup.php'
]

def is_private_ip(ip):
    """检查是否为私有IP地址"""
    try:
        ip_obj = ipaddress.ip_address(ip)
        return ip_obj.is_private
    except:
        return False

def rate_limit_check(ip, max_requests=50, time_window=60):
    """简单的速率限制检查"""
    now = time.time()

    # 清理过期记录
    while ip_access_log[ip] and ip_access_log[ip][0] < now - time_window:
        ip_access_log[ip].popleft()

    # 检查请求数量
    if len(ip_access_log[ip]) >= max_requests:
        return False

    # 记录当前请求
    ip_access_log[ip].append(now)
    return True

def security_middleware():
    """安全中间件"""
    from flask import current_app

    # 获取客户端IP
    client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
    if client_ip and ',' in client_ip:
        client_ip = client_ip.split(',')[0].strip()

    # 特殊处理：127.0.0.1 和本地回环地址完全跳过所有安全检查
    if client_ip in ['127.0.0.1', '::1', 'localhost', '0.0.0.0']:
        return

    # 跳过静态资源的安全检查
    if request.path.startswith('/static/') or request.path.startswith('/favicon'):
        return

    # 检查是否在白名单中，如果是则跳过所有安全检查
    if client_ip in WHITELIST_IPS:
        return

    # 检查是否为被阻止的IP
    if client_ip in blocked_ips:
        current_app.logger.warning(f'Blocked IP attempted access: {client_ip}')
        abort(403)

    # 跳过私有IP的检查（内网访问）
    if is_private_ip(client_ip):
        return

    # 对于公网IP进行安全检查
    # 速率限制检查 - 提高限制到200次/分钟
    if not rate_limit_check(client_ip, max_requests=200):
        current_app.logger.warning(f'Rate limit exceeded for IP: {client_ip}')
        blocked_ips.add(client_ip)
        abort(429)  # Too Many Requests

    # 检查User-Agent
    user_agent = request.headers.get('User-Agent', '').lower()
    if any(agent in user_agent for agent in SUSPICIOUS_USER_AGENTS):
        current_app.logger.warning(f'Suspicious User-Agent blocked: {user_agent} from {client_ip}')
        blocked_ips.add(client_ip)
        abort(403)

    # 检查请求路径
    path = request.path.lower()
    if any(suspicious_path in path for suspicious_path in SUSPICIOUS_PATHS):
        current_app.logger.warning(f'Suspicious path blocked: {path} from {client_ip}')
        abort(404)  # 返回404而不是403，避免暴露信息

    # 检查请求大小 - 提高到120MB以支持视频上传
    if request.content_length and request.content_length > 120 * 1024 * 1024:  # 120MB
        current_app.logger.warning(f'Large request blocked: {request.content_length} bytes from {client_ip}')
        abort(413)

    # 检查请求方法
    if request.method not in ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS']:
        current_app.logger.warning(f'Suspicious method blocked: {request.method} from {client_ip}')
        abort(405)

def add_security_headers(response):
    """添加安全响应头"""

    # 基本安全头
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'DENY'
    response.headers['X-XSS-Protection'] = '1; mode=block'
    response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'

    # 仅在HTTPS环境下添加HSTS
    if request.is_secure:
        response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'

    # 移除服务器信息
    response.headers.pop('Server', None)

    return response

def log_security_event(event_type, details, ip=None):
    """记录安全事件"""
    from flask import current_app

    if not ip:
        ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
        if ip and ',' in ip:
            ip = ip.split(',')[0].strip()

    current_app.logger.warning(f'SECURITY EVENT - {event_type}: {details} from {ip}')

def init_security(app):
    """初始化安全配置"""

    @app.before_request
    def before_request_security():
        """请求前安全检查"""
        try:
            # 暂时禁用安全中间件进行测试
            # security_middleware()
            pass
        except Exception as e:
            app.logger.error(f'Security middleware error: {str(e)}')
            # 不阻止正常请求，只记录错误

    @app.after_request
    def after_request_security(response):
        """请求后添加安全头"""
        try:
            return add_security_headers(response)
        except Exception as e:
            app.logger.error(f'Security headers error: {str(e)}')
            return response

    # 错误处理
    @app.errorhandler(403)
    def forbidden(error):
        with app.app_context():
            log_security_event('ACCESS_DENIED', 'Forbidden access attempt')
        return 'Access Denied', 403

    @app.errorhandler(429)
    def rate_limited(error):
        with app.app_context():
            log_security_event('RATE_LIMIT', 'Rate limit exceeded')
        return 'Too Many Requests', 429

    app.logger.info('Security middleware initialized')

def get_blocked_ips():
    """获取被阻止的IP列表"""
    return list(blocked_ips)

def unblock_ip(ip):
    """解除IP阻止"""
    from flask import current_app
    blocked_ips.discard(ip)
    try:
        current_app.logger.info(f'IP unblocked: {ip}')
    except RuntimeError:
        # 如果没有应用上下文，就不记录日志
        pass

def block_ip(ip):
    """手动阻止IP"""
    from flask import current_app
    blocked_ips.add(ip)
    try:
        current_app.logger.info(f'IP manually blocked: {ip}')
    except RuntimeError:
        # 如果没有应用上下文，就不记录日志
        pass

def add_to_whitelist(ip):
    """添加IP到白名单"""
    from flask import current_app
    WHITELIST_IPS.add(ip)
    # 如果IP在黑名单中，同时移除
    blocked_ips.discard(ip)
    try:
        current_app.logger.info(f'IP added to whitelist: {ip}')
    except RuntimeError:
        pass

def remove_from_whitelist(ip):
    """从白名单移除IP"""
    from flask import current_app
    WHITELIST_IPS.discard(ip)
    try:
        current_app.logger.info(f'IP removed from whitelist: {ip}')
    except RuntimeError:
        pass

def get_whitelist_ips():
    """获取白名单IP列表"""
    return list(WHITELIST_IPS)

def is_whitelisted(ip):
    """检查IP是否在白名单中"""
    return ip in WHITELIST_IPS

import request from '@/utils/request'

const consumptionplanAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/consumptionplan',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/consumptionplan/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/consumptionplan',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/consumptionplan/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/consumptionplan/${id}`,
      method: 'delete'
    })
  }
}

export default consumptionplanAPI
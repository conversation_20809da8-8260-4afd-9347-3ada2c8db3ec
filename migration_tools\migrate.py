#!/usr/bin/env python3
"""
学校食堂管理系统迁移主脚本
整合所有迁移工具，提供统一的迁移入口
"""

import os
import sys
import argparse
from pathlib import Path

# 添加当前目录到 Python 路径
sys.path.insert(0, str(Path(__file__).parent))

from project_analyzer import ProjectAnalyzer
from api_generator import APIGenerator
from frontend_generator import FrontendGenerator

class MigrationManager:
    def __init__(self, project_root: str = '.'):
        self.project_root = Path(project_root)
        self.analyzer = ProjectAnalyzer(project_root)
        self.api_generator = APIGenerator(project_root)
        self.frontend_generator = FrontendGenerator(project_root)

    def run_full_migration(self):
        """执行完整迁移流程"""
        print("🚀 开始学校食堂管理系统迁移")
        print("=" * 60)

        # 步骤1: 项目分析
        print("\n📊 步骤1: 项目分析")
        analysis_result = self.analyzer.analyze()
        self.analyzer.print_summary()
        self.analyzer.save_report()

        # 步骤2: 生成后端 API
        print("\n🔧 步骤2: 生成后端 API")
        self.api_generator.generate_apis()

        # 步骤3: 生成前端项目
        print("\n🎨 步骤3: 生成前端项目")
        self.frontend_generator.generate_frontend()

        # 步骤4: 生成部署配置
        print("\n🚀 步骤4: 生成部署配置")
        self._generate_deployment_configs()

        # 步骤5: 生成迁移文档
        print("\n📚 步骤5: 生成迁移文档")
        self._generate_migration_docs()

        print("\n" + "=" * 60)
        print("✅ 迁移完成！")
        print("\n📁 生成的文件:")
        print("  - migration_analysis_report.json  # 项目分析报告")
        print("  - backend/                        # 后端 API 项目")
        print("  - frontend/                       # 前端 Vue 项目")
        print("  - deployment/                     # 部署配置文件")
        print("  - docs/                          # 迁移文档")

        print("\n🎯 下一步操作:")
        print("  1. 进入 backend 目录，安装依赖: pip install -r requirements.txt")
        print("  2. 进入 frontend 目录，安装依赖: npm install")
        print("  3. 配置数据库连接")
        print("  4. 启动后端服务: python run.py")
        print("  5. 启动前端服务: npm run dev")
        print("  6. 配置 Nginx 反向代理")

    def run_analysis_only(self):
        """仅执行项目分析"""
        print("📊 执行项目分析...")
        analysis_result = self.analyzer.analyze()
        self.analyzer.print_summary()
        self.analyzer.save_report()
        print("✅ 分析完成")

    def run_backend_only(self):
        """仅生成后端 API"""
        print("🔧 生成后端 API...")
        self.api_generator.generate_apis()
        print("✅ 后端生成完成")

    def run_frontend_only(self):
        """仅生成前端项目"""
        print("🎨 生成前端项目...")
        self.frontend_generator.generate_frontend()
        print("✅ 前端生成完成")

    def _generate_deployment_configs(self):
        """生成部署配置文件"""
        deployment_dir = self.project_root / 'deployment'
        deployment_dir.mkdir(exist_ok=True)

        # 生成 Nginx 配置
        nginx_config = '''server {
    listen 80;
    server_name localhost;

    # 新系统前端
    location /new/ {
        alias /path/to/frontend/dist/;
        try_files $uri $uri/ /new/index.html;
    }

    # 新系统 API
    location /api/v1/ {
        proxy_pass http://localhost:5001/api/v1/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 现有系统
    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
'''

        with open(deployment_dir / 'nginx.conf', 'w', encoding='utf-8') as f:
            f.write(nginx_config)

        # 生成 Docker Compose 配置
        docker_compose = '''version: '3.8'

services:
  backend:
    build: ./backend
    ports:
      - "5001:5001"
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=mssql+pyodbc:///?odbc_connect=...
    volumes:
      - ./backend:/app
    restart: unless-stopped

  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./deployment/nginx.conf:/etc/nginx/conf.d/default.conf
      - ./frontend/dist:/usr/share/nginx/html/new
    depends_on:
      - backend
      - frontend
    restart: unless-stopped
'''

        with open(deployment_dir / 'docker-compose.yml', 'w', encoding='utf-8') as f:
            f.write(docker_compose)

        print("📦 部署配置文件已生成")

    def _generate_migration_docs(self):
        """生成迁移文档"""
        docs_dir = self.project_root / 'docs'
        docs_dir.mkdir(exist_ok=True)

        # 生成快速开始文档
        quick_start = '''# 快速开始指南

## 环境要求

- Python 3.8+
- Node.js 16+
- SQL Server
- Redis (可选)

## 安装步骤

### 1. 后端设置

```bash
cd backend
pip install -r requirements.txt
```

### 2. 前端设置

```bash
cd frontend
npm install
```

### 3. 数据库配置

编辑 `backend/config.py` 文件，配置数据库连接：

```python
SQLALCHEMY_DATABASE_URI = 'mssql+pyodbc:///?odbc_connect=...'
```

### 4. 启动服务

```bash
# 启动后端
cd backend
python run.py

# 启动前端
cd frontend
npm run dev
```

### 5. 访问系统

- 新系统前端: http://localhost:3000
- 后端 API: http://localhost:5001/api/v1
- 现有系统: http://localhost:8080

## 迁移策略

1. **并存运行**: 新旧系统同时运行
2. **逐步迁移**: 按模块逐步迁移功能
3. **数据同步**: 确保数据一致性
4. **用户培训**: 提供用户培训和支持
5. **完全切换**: 最终完全切换到新系统

## 注意事项

- 迁移过程中保持数据备份
- 充分测试新功能
- 收集用户反馈
- 监控系统性能
'''

        with open(docs_dir / 'quick-start.md', 'w', encoding='utf-8') as f:
            f.write(quick_start)

        print("📚 迁移文档已生成")

def main():
    parser = argparse.ArgumentParser(description='学校食堂管理系统迁移工具')
    parser.add_argument('--mode', choices=['full', 'analysis', 'backend', 'frontend'],
                       default='full', help='迁移模式')
    parser.add_argument('--project-root', default='.', help='项目根目录')

    args = parser.parse_args()

    manager = MigrationManager(args.project_root)

    if args.mode == 'full':
        manager.run_full_migration()
    elif args.mode == 'analysis':
        manager.run_analysis_only()
    elif args.mode == 'backend':
        manager.run_backend_only()
    elif args.mode == 'frontend':
        manager.run_frontend_only()

if __name__ == '__main__':
    main()

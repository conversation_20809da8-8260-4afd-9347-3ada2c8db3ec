import request from '@/utils/request'

const inventoryalertAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/inventoryalert',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/inventoryalert/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/inventoryalert',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/inventoryalert/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/inventoryalert/${id}`,
      method: 'delete'
    })
  }
}

export default inventoryalertAPI
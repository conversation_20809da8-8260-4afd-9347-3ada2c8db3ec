import request from '@/utils/request'

const ingredientAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/ingredient',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/ingredient/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/ingredient',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/ingredient/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/ingredient/${id}`,
      method: 'delete'
    })
  }
}

export default ingredientAPI
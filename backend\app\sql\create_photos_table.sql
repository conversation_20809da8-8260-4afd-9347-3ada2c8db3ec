-- 创建 photos 表
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[photos]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[photos](
        [id] [int] IDENTITY(1,1) NOT NULL,
        [reference_id] [int] NOT NULL,
        [reference_type] [nvarchar](20) NOT NULL,
        [file_name] [nvarchar](255) NOT NULL,
        [file_path] [nvarchar](255) NOT NULL,
        [description] [nvarchar](255) NULL,
        [rating] [int] NULL DEFAULT 3,
        [upload_time] [datetime2](1) NOT NULL DEFAULT GETDATE(),
        CONSTRAINT [PK_photos] PRIMARY KEY CLUSTERED 
        (
            [id] ASC
        )
    )

    -- 创建索引
    CREATE NONCLUSTERED INDEX [idx_photos_reference] ON [dbo].[photos]
    (
        [reference_type] ASC,
        [reference_id] ASC
    )

    PRINT 'photos 表创建成功'
END
ELSE
BEGIN
    PRINT 'photos 表已存在'
END

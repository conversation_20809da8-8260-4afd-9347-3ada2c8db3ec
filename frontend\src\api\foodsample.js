import request from '@/utils/request'

const foodsampleAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/foodsample',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/foodsample/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/foodsample',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/foodsample/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/foodsample/${id}`,
      method: 'delete'
    })
  }
}

export default foodsampleAPI
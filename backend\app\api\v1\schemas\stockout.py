"""
StockOut 序列化模式
"""

from marshmallow import Schema, fields, validate

class StockOutSchema(Schema):
    """StockOut 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    stock_out_number = fields.String(required=True)
    
    
    
    warehouse_id = fields.Integer(required=True)
    
    
    
    consumption_plan_id = fields.Integer()
    
    
    
    stock_out_date = fields.String(required=True)
    
    
    
    stock_out_type = fields.String(required=True)
    
    
    
    operator_id = fields.Integer(required=True)
    
    
    
    approver_id = fields.Integer()
    
    
    
    status = fields.String(required=True)
    
    
    
    notes = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
import request from '@/utils/request'

const purchaserequisitionAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/purchaserequisition',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/purchaserequisition/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/purchaserequisition',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/purchaserequisition/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/purchaserequisition/${id}`,
      method: 'delete'
    })
  }
}

export default purchaserequisitionAPI
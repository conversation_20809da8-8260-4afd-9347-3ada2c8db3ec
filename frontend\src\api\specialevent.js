import request from '@/utils/request'

const specialeventAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/specialevent',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/specialevent/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/specialevent',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/specialevent/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/specialevent/${id}`,
      method: 'delete'
    })
  }
}

export default specialeventAPI
#!/usr/bin/env python3
"""
数据库连接测试脚本
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.utils.database import test_connection

def main():
    """测试数据库连接"""
    print("🔍 测试数据库连接...")
    print("=" * 50)

    # 创建应用
    app = create_app('development')

    with app.app_context():
        # 测试连接
        if test_connection():
            print("✅ 数据库连接成功！")
            print(f"📊 服务器: {app.config['DB_SERVER']}")
            print(f"🗄️ 数据库: {app.config['DB_DATABASE']}")
            print(f"👤 用户: {app.config['DB_USERNAME']}")
            return True
        else:
            print("❌ 数据库连接失败！")
            print("请检查以下配置:")
            print(f"📊 服务器: {app.config['DB_SERVER']}")
            print(f"🗄️ 数据库: {app.config['DB_DATABASE']}")
            print(f"👤 用户: {app.config['DB_USERNAME']}")
            print(f"🔑 密码: {'*' * len(app.config['DB_PASSWORD'])}")
            return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)

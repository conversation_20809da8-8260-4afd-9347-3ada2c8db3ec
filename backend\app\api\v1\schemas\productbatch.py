"""
ProductBatch 序列化模式
"""

from marshmallow import Schema, fields, validate

class ProductBatchSchema(Schema):
    """ProductBatch 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    name = fields.String(required=True)
    
    
    
    category_id = fields.Integer(required=True)
    
    
    
    supplier_id = fields.Integer(required=True)
    
    
    
    created_by = fields.Integer(required=True)
    
    
    
    status = fields.String(required=True)
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
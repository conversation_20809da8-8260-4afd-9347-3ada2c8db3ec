"""
Warehouse 序列化模式
"""

from marshmallow import Schema, fields, validate

class WarehouseSchema(Schema):
    """Warehouse 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    name = fields.String(required=True)
    
    
    
    area_id = fields.Integer(required=True)
    
    
    
    location = fields.String(required=True)
    
    
    
    manager_id = fields.Integer(required=True)
    
    
    
    capacity = fields.Float()
    
    
    
    capacity_unit = fields.String()
    
    
    
    temperature_range = fields.String()
    
    
    
    humidity_range = fields.String()
    
    
    
    status = fields.String(required=True)
    
    
    
    notes = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
"""
RecipeImprovementSuggestion 序列化模式
"""

from marshmallow import Schema, fields, validate

class RecipeImprovementSuggestionSchema(Schema):
    """RecipeImprovementSuggestion 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    recipe_id = fields.Integer(required=True)
    
    
    
    user_id = fields.Integer(required=True)
    
    
    
    suggestion = fields.String(required=True)
    
    
    
    is_adopted = fields.Boolean()
    
    
    
    created_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
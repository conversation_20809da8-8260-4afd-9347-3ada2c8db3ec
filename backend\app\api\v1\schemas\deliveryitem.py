"""
DeliveryItem 序列化模式
"""

from marshmallow import Schema, fields, validate

class DeliveryItemSchema(Schema):
    """DeliveryItem 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    delivery_id = fields.Integer(required=True)
    
    
    
    order_item_id = fields.Integer(required=True)
    
    
    
    product_id = fields.Integer(required=True)
    
    
    
    quantity = fields.Float(required=True)
    
    
    
    unit = fields.String(required=True)
    
    
    
    batch_number = fields.String(required=True)
    
    
    
    production_date = fields.Date(required=True)
    
    
    
    expiry_date = fields.Date(required=True)
    
    
    
    certificate_numbers = fields.String()
    
    
    
    notes = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
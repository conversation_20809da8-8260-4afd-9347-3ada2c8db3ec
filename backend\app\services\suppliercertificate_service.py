"""
SupplierCertificate 服务层
"""

from typing import Optional, Dict, Any
from sqlalchemy.orm import Session
from app.models.models_phase3 import SupplierCertificate
from app.utils.database import get_db_session

class SupplierCertificateService:
    """SupplierCertificate 服务"""

    @staticmethod
    def get_list(page: int = 1, per_page: int = 10, **filters):
        """获取SupplierCertificate列表"""
        with get_db_session() as session:
            query = session.query(SupplierCertificate)

            # 应用过滤条件
            for key, value in filters.items():
                if hasattr(SupplierCertificate, key) and value is not None:
                    query = query.filter(getattr(SupplierCertificate, key) == value)

            return query.paginate(
                page=page,
                per_page=per_page,
                error_out=False
            )

    @staticmethod
    def get_by_id(id: int) -> Optional[SupplierCertificate]:
        """根据ID获取SupplierCertificate"""
        with get_db_session() as session:
            return session.query(SupplierCertificate).filter(SupplierCertificate.id == id).first()

    @staticmethod
    def create(data: Dict[str, Any]) -> SupplierCertificate:
        """创建SupplierCertificate"""
        with get_db_session() as session:
            item = SupplierCertificate(**data)
            session.add(item)
            session.commit()
            session.refresh(item)
            return item

    @staticmethod
    def update(id: int, data: Dict[str, Any]) -> Optional[SupplierCertificate]:
        """更新SupplierCertificate"""
        with get_db_session() as session:
            item = session.query(SupplierCertificate).filter(SupplierCertificate.id == id).first()
            if not item:
                return None

            for key, value in data.items():
                if hasattr(item, key):
                    setattr(item, key, value)

            session.commit()
            session.refresh(item)
            return item

    @staticmethod
    def delete(id: int) -> bool:
        """删除SupplierCertificate"""
        with get_db_session() as session:
            item = session.query(SupplierCertificate).filter(SupplierCertificate.id == id).first()
            if not item:
                return False

            session.delete(item)
            session.commit()
            return True
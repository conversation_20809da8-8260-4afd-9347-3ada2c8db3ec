"""
PurchaseRequisition API 资源
"""

from flask import request
from flask_restful import Resource
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.services.purchaserequisition_service import PurchaseRequisitionService
from app.api.v1.schemas.purchaserequisition import PurchaseRequisitionSchema
from app.utils.response import success_response, error_response

class PurchaseRequisitionListAPI(Resource):
    """PurchaseRequisition 列表 API"""

    @jwt_required()
    def get(self):
        """获取PurchaseRequisition列表"""
        try:
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 10, type=int)

            result = PurchaseRequisitionService.get_list(page=page, per_page=per_page)

            schema = PurchaseRequisitionSchema(many=True)
            return success_response(
                data={
                    'items': schema.dump(result.items),
                    'total': result.total,
                    'page': result.page,
                    'per_page': result.per_page
                }
            )
        except Exception as e:
            return error_response(message=str(e))

    @jwt_required()
    def post(self):
        """创建PurchaseRequisition"""
        try:
            data = request.get_json()
            schema = PurchaseRequisitionSchema()

            # 验证数据
            validated_data = schema.load(data)

            # 创建记录
            item = PurchaseRequisitionService.create(validated_data)

            return success_response(
                data=schema.dump(item),
                message='创建成功'
            )
        except Exception as e:
            return error_response(message=str(e))

class PurchaseRequisitionAPI(Resource):
    """PurchaseRequisition 详情 API"""

    @jwt_required()
    def get(self, id):
        """获取PurchaseRequisition详情"""
        try:
            item = PurchaseRequisitionService.get_by_id(id)
            if not item:
                return error_response(message='记录不存在', code=404)

            schema = PurchaseRequisitionSchema()
            return success_response(data=schema.dump(item))
        except Exception as e:
            return error_response(message=str(e))

    @jwt_required()
    def put(self, id):
        """更新PurchaseRequisition"""
        try:
            data = request.get_json()
            schema = PurchaseRequisitionSchema()

            # 验证数据
            validated_data = schema.load(data, partial=True)

            # 更新记录
            item = PurchaseRequisitionService.update(id, validated_data)
            if not item:
                return error_response(message='记录不存在', code=404)

            return success_response(
                data=schema.dump(item),
                message='更新成功'
            )
        except Exception as e:
            return error_response(message=str(e))

    @jwt_required()
    def delete(self, id):
        """删除PurchaseRequisition"""
        try:
            success = PurchaseRequisitionService.delete(id)
            if not success:
                return error_response(message='记录不存在', code=404)

            return success_response(message='删除成功')
        except Exception as e:
            return error_response(message=str(e))
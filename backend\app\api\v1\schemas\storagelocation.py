"""
StorageLocation 序列化模式
"""

from marshmallow import Schema, fields, validate

class StorageLocationSchema(Schema):
    """StorageLocation 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    warehouse_id = fields.Integer(required=True)
    
    
    
    name = fields.String(required=True)
    
    
    
    location_code = fields.String(required=True)
    
    
    
    storage_type = fields.String(required=True)
    
    
    
    capacity = fields.Float()
    
    
    
    capacity_unit = fields.String()
    
    
    
    temperature_range = fields.String()
    
    
    
    status = fields.String(required=True)
    
    
    
    notes = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
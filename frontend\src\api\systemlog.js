import request from '@/utils/request'

const systemlogAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/systemlog',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/systemlog/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/systemlog',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/systemlog/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/systemlog/${id}`,
      method: 'delete'
    })
  }
}

export default systemlogAPI
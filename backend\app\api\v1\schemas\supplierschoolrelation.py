"""
SupplierSchoolRelation 序列化模式
"""

from marshmallow import Schema, fields, validate

class SupplierSchoolRelationSchema(Schema):
    """SupplierSchoolRelation 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    supplier_id = fields.Integer(required=True)
    
    
    
    area_id = fields.Integer(required=True)
    
    
    
    contract_number = fields.String()
    
    
    
    start_date = fields.Date(required=True)
    
    
    
    end_date = fields.Date()
    
    
    
    status = fields.Integer(required=True)
    
    
    
    notes = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
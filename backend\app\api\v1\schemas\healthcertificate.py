"""
HealthCertificate 序列化模式
"""

from marshmallow import Schema, fields, validate

class HealthCertificateSchema(Schema):
    """HealthCertificate 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    employee_id = fields.Integer(required=True)
    
    
    
    certificate_no = fields.String(required=True)
    
    
    
    issue_authority = fields.String(required=True)
    
    
    
    issue_date = fields.Date(required=True)
    
    
    
    expire_date = fields.Date(required=True)
    
    
    
    certificate_img = fields.String()
    
    
    
    status = fields.Integer(required=True)
    
    
    
    notes = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
import request from '@/utils/request'

const categoryunitmappingAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/categoryunitmapping',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/categoryunitmapping/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/categoryunitmapping',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/categoryunitmapping/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/categoryunitmapping/${id}`,
      method: 'delete'
    })
  }
}

export default categoryunitmappingAPI
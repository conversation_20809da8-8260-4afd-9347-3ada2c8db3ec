"""
RecipeIngredientAlternative 序列化模式
"""

from marshmallow import Schema, fields, validate

class RecipeIngredientAlternativeSchema(Schema):
    """RecipeIngredientAlternative 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    recipe_id = fields.Integer(required=True)
    
    
    
    original_ingredient_id = fields.Integer(required=True)
    
    
    
    alternative_ingredient_id = fields.Integer(required=True)
    
    
    
    conversion_ratio = fields.Float()
    
    
    
    notes = fields.String()
    
    

    class Meta:
        ordered = True
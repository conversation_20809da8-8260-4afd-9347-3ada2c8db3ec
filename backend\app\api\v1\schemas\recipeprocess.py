"""
RecipeProcess 序列化模式
"""

from marshmallow import Schema, fields, validate

class RecipeProcessSchema(Schema):
    """RecipeProcess 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    recipe_id = fields.Integer(required=True)
    
    
    
    process_name = fields.String(required=True)
    
    
    
    process_order = fields.Integer(required=True)
    
    
    
    description = fields.String()
    
    
    
    estimated_time = fields.Integer()
    
    
    
    image = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
import request from '@/utils/request'

const inventorycheckitemAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/inventorycheckitem',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/inventorycheckitem/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/inventorycheckitem',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/inventorycheckitem/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/inventorycheckitem/${id}`,
      method: 'delete'
    })
  }
}

export default inventorycheckitemAPI
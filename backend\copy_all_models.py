#!/usr/bin/env python3
"""
批量复制所有模型文件到后端项目
"""

import os
import shutil
from pathlib import Path

def copy_all_models():
    """复制所有模型文件"""
    print("📋 批量复制所有模型文件...")
    
    # 源目录和目标目录
    source_dir = Path('../app')
    target_dir = Path('app/models')
    
    # 确保目标目录存在
    target_dir.mkdir(parents=True, exist_ok=True)
    
    # 需要复制的所有模型文件
    model_files = [
        'models_consultation.py',
        'models_daily_management.py',
        'models_financial.py',
        'models_homepage_carousel.py',
        'models_ingredient_traceability.py',
        'models_phase1.py',
        'models_phase3.py',
        'models_phase4.py',
        'models_product_batch.py',
        'models_recipe_advanced.py',
        'models_supplier.py',
        'models_system.py',
        'models_visibility.py',
        'models_warehouse_new.py',
        'models_weekly_menu_temp.py'
    ]
    
    copied_files = []
    failed_files = []
    
    for model_file in model_files:
        source_file = source_dir / model_file
        target_file = target_dir / model_file
        
        if source_file.exists():
            try:
                shutil.copy2(source_file, target_file)
                copied_files.append(model_file)
                print(f"✅ 复制: {model_file}")
            except Exception as e:
                failed_files.append((model_file, str(e)))
                print(f"❌ 复制失败 {model_file}: {e}")
        else:
            failed_files.append((model_file, "文件不存在"))
            print(f"⚠️ 文件不存在: {model_file}")
    
    print(f"\n📊 复制结果:")
    print(f"✅ 成功复制: {len(copied_files)} 个文件")
    print(f"❌ 失败: {len(failed_files)} 个文件")
    
    return copied_files, failed_files

def fix_model_imports():
    """修复所有模型文件的导入问题"""
    print("\n🔧 修复模型文件导入问题...")
    
    model_dir = Path('app/models')
    fixed_files = []
    
    # 获取所有模型文件
    model_files = list(model_dir.glob('models_*.py'))
    
    for model_file in model_files:
        try:
            with open(model_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 修复常见的导入问题
            original_content = content
            
            # 替换 app 导入
            content = content.replace(
                'from app import db, login_manager',
                '# 这些将在应用初始化时设置\ndb = None\nlogin_manager = None'
            )
            
            content = content.replace(
                'from app.utils.datetime_helper import format_datetime',
                '''def format_datetime(dt, fmt):
    """简单的日期时间格式化函数"""
    if dt and hasattr(dt, "strftime"):
        return dt.strftime(fmt)
    return str(dt) if dt else None'''
            )
            
            # 如果内容有变化，写回文件
            if content != original_content:
                with open(model_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                fixed_files.append(model_file.name)
                print(f"✅ 修复: {model_file.name}")
            
        except Exception as e:
            print(f"❌ 修复失败 {model_file.name}: {e}")
    
    print(f"\n📊 修复了 {len(fixed_files)} 个模型文件")
    return fixed_files

def main():
    """主函数"""
    print("🚀 开始批量复制和修复模型文件")
    print("=" * 60)
    
    try:
        # 复制所有模型文件
        copied_files, failed_files = copy_all_models()
        
        # 修复导入问题
        fixed_files = fix_model_imports()
        
        print("\n" + "=" * 60)
        print("🎉 批量操作完成！")
        print(f"✅ 复制了 {len(copied_files)} 个模型文件")
        print(f"🔧 修复了 {len(fixed_files)} 个模型文件")
        
        if failed_files:
            print(f"⚠️ {len(failed_files)} 个文件复制失败")
        
        print("\n📝 下一步:")
        print("1. 运行 python run.py 启动 API 服务器")
        print("2. 测试 API 端点")
        
        return len(failed_files) == 0
        
    except Exception as e:
        print(f"❌ 批量操作失败: {e}")
        return False

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)

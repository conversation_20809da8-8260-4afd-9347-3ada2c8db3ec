"""
模块可见性管理路由
"""

from flask import render_template, redirect, url_for, flash, request, jsonify, current_app, session
from flask_login import login_required, current_user
from app import db
from app.admin import system_bp
from app.utils import admin_required
from app.utils.log_activity import log_activity
from app.models import Role
from app.models_visibility import ModuleVisibility
from app.utils.menu import MENU_CONFIG
import json
from datetime import datetime

@system_bp.route('/module-visibility')
@login_required
@admin_required
def module_visibility():
    """模块可见性管理页面"""
    # 获取所有角色
    roles = Role.query.order_by(Role.name).all()

    # 获取所有模块
    modules = []
    for item in MENU_CONFIG:
        module = {
            'id': item['id'],
            'name': item['name'],
            'children': []
        }

        # 添加子模块
        if 'children' in item and item['children']:
            for child in item['children']:
                module['children'].append({
                    'id': child['id'],
                    'name': child['name']
                })

        modules.append(module)

    # 获取当前可见性设置
    visibilities = {}
    for role in roles:
        visibilities[role.id] = {}

        # 获取该角色的所有可见性设置
        role_visibilities = ModuleVisibility.get_role_visibilities(role.id)

        # 将可见性设置转换为字典
        for visibility in role_visibilities:
            visibilities[role.id][visibility.module_id] = visibility.is_visible

    return render_template(
        'admin/system/module_visibility.html',
        title='模块可见性管理',
        roles=roles,
        modules=modules,
        visibilities=visibilities,
        now=datetime.now()
    )

@system_bp.route('/module-visibility/update', methods=['POST'])
@login_required
@admin_required
def update_module_visibility():
    """更新模块可见性设置"""
    try:
        # 获取表单数据
        data = request.json
        role_id = data.get('role_id')
        module_id = data.get('module_id')
        is_visible = data.get('is_visible')

        # 验证数据
        if not role_id or not module_id:
            return jsonify({'success': False, 'message': '角色ID和模块ID不能为空'}), 400

        # 检查角色是否存在
        role = Role.query.get(role_id)
        if not role:
            return jsonify({'success': False, 'message': '角色不存在'}), 404

        # 更新可见性设置
        ModuleVisibility.set_visibility(
            module_id=module_id,
            role_id=role_id,
            is_visible=is_visible
        )

        # 记录活动
        log_activity(
            'update_module_visibility',
            f'更新模块可见性: {module_id} for {role.name} to {is_visible}',
            current_user.id
        )

        return jsonify({'success': True, 'message': '模块可见性设置已更新'})
    except Exception as e:
        current_app.logger.error(f'更新模块可见性设置失败: {str(e)}')
        return jsonify({'success': False, 'message': f'更新失败: {str(e)}'}), 500

@system_bp.route('/module-visibility/bulk-update', methods=['POST'])
@login_required
@admin_required
def bulk_update_module_visibility():
    """批量更新模块可见性设置"""
    try:
        # 获取表单数据
        data = request.json
        role_id = data.get('role_id')
        module_ids = data.get('module_ids', [])
        is_visible = data.get('is_visible')

        # 验证数据
        if not role_id or not module_ids:
            return jsonify({'success': False, 'message': '角色ID和模块ID不能为空'}), 400

        # 检查角色是否存在
        role = Role.query.get(role_id)
        if not role:
            return jsonify({'success': False, 'message': '角色不存在'}), 404

        # 批量更新可见性设置
        for module_id in module_ids:
            ModuleVisibility.set_visibility(
                module_id=module_id,
                role_id=role_id,
                is_visible=is_visible
            )

        # 记录活动
        log_activity(
            'bulk_update_module_visibility',
            f'批量更新模块可见性: {len(module_ids)} modules for {role.name} to {is_visible}',
            current_user.id
        )

        return jsonify({'success': True, 'message': f'已更新 {len(module_ids)} 个模块的可见性设置'})
    except Exception as e:
        current_app.logger.error(f'批量更新模块可见性设置失败: {str(e)}')
        return jsonify({'success': False, 'message': f'更新失败: {str(e)}'}), 500

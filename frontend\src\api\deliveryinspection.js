import request from '@/utils/request'

const deliveryinspectionAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/deliveryinspection',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/deliveryinspection/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/deliveryinspection',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/deliveryinspection/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/deliveryinspection/${id}`,
      method: 'delete'
    })
  }
}

export default deliveryinspectionAPI
from flask import jsonify, request, current_app
from app import db
from app.api import api_bp
from app.models import User, Supplier, Ingredient, Recipe, FoodSample, PurchaseOrder
from flask_login import login_required, current_user

# 用户API
@api_bp.route('/users', methods=['GET'])
@login_required
def get_users():
    """获取所有用户"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', current_app.config['ITEMS_PER_PAGE'], type=int)

    users = User.query.order_by(User.id.desc()).paginate(page=page, per_page=per_page, error_out=0)

    return jsonify({
        'items': [user.to_dict() for user in users.items],
        'total': users.total,
        'pages': users.pages,
        'current_page': users.page
    })

# 供应商API
@api_bp.route('/suppliers', methods=['GET'])
@login_required
def get_suppliers():
    """获取所有供应商"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', current_app.config['ITEMS_PER_PAGE'], type=int)

    suppliers = Supplier.query.order_by(Supplier.id.desc()).paginate(page=page, per_page=per_page, error_out=0)

    return jsonify({
        'items': [supplier.to_dict() for supplier in suppliers.items],
        'total': suppliers.total,
        'pages': suppliers.pages,
        'current_page': suppliers.page
    })

@api_bp.route('/suppliers/<int:id>', methods=['GET'])
@login_required
def get_supplier(id):
    """获取单个供应商"""
    supplier = Supplier.query.get_or_404(id)
    return jsonify(supplier.to_dict())

# 食材API
@api_bp.route('/ingredients', methods=['GET'])
@login_required
def get_ingredients():
    """获取所有食材 - 实现学校级数据隔离"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', current_app.config['ITEMS_PER_PAGE'], type=int)

    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 构建查询 - 只显示用户学校的食材和全局食材
    query = Ingredient.query.filter(
        db.or_(
            Ingredient.area_id.in_(area_ids),  # 用户学校的食材
            Ingredient.is_global == True,      # 全局食材（系统预设）
            Ingredient.area_id.is_(None)       # 兼容旧数据（无area_id的食材）
        )
    )

    ingredients = query.order_by(Ingredient.id.desc()).paginate(page=page, per_page=per_page, error_out=0)

    return jsonify({
        'items': [ingredient.to_dict() for ingredient in ingredients.items],
        'total': ingredients.total,
        'pages': ingredients.pages,
        'current_page': ingredients.page
    })

@api_bp.route('/ingredients/<int:id>', methods=['GET'])
@login_required
def get_ingredient(id):
    """获取单个食材"""
    ingredient = Ingredient.query.get_or_404(id)
    return jsonify(ingredient.to_dict())

# 食谱API
@api_bp.route('/recipes', methods=['GET'])
@login_required
def get_recipes():
    """获取所有食谱 - 实现学校级数据隔离"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', current_app.config['ITEMS_PER_PAGE'], type=int)

    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 构建查询 - 只显示用户学校的食谱和全局食谱
    query = Recipe.query.filter(
        db.or_(
            Recipe.area_id.in_(area_ids),  # 用户学校的食谱
            Recipe.is_global == True,      # 全局食谱（系统预设）
            Recipe.area_id.is_(None)       # 兼容旧数据（无area_id的食谱）
        )
    )

    recipes = query.order_by(Recipe.id.desc()).paginate(page=page, per_page=per_page, error_out=0)

    return jsonify({
        'items': [recipe.to_dict() for recipe in recipes.items],
        'total': recipes.total,
        'pages': recipes.pages,
        'current_page': recipes.page
    })

@api_bp.route('/recipes/<int:id>', methods=['GET'])
@login_required
def get_recipe(id):
    """获取单个食谱"""
    recipe = Recipe.query.get_or_404(id)
    return jsonify(recipe.to_dict())

# 登录状态检查API
@api_bp.route('/check-login-status', methods=['GET'])
def check_login_status():
    """检查用户登录状态"""
    return jsonify({
        'isLoggedIn': current_user.is_authenticated,
        'userId': current_user.id if current_user.is_authenticated else None,
        'username': current_user.username if current_user.is_authenticated else None
    })

# 食谱收藏API
@api_bp.route('/check-favorite/<int:recipe_id>', methods=['GET'])
def check_favorite(recipe_id):
    """检查食谱是否已收藏"""
    if not current_user.is_authenticated:
        return jsonify({'is_favorited': False})

    from app.models_recipe_advanced import UserRecipeFavorite
    favorite = UserRecipeFavorite.query.filter_by(
        recipe_id=recipe_id,
        user_id=current_user.id
    ).first()

    return jsonify({'is_favorited': favorite is not None})

@api_bp.route('/toggle-favorite/<int:recipe_id>', methods=['POST'])
@login_required
def toggle_favorite(recipe_id):
    """收藏/取消收藏食谱"""
    from app.models_recipe_advanced import UserRecipeFavorite

    favorite = UserRecipeFavorite.query.filter_by(
        recipe_id=recipe_id,
        user_id=current_user.id
    ).first()

    if favorite:
        # 取消收藏
        db.session.delete(favorite)
        db.session.commit()
        return jsonify({'action': 'unfavorited'})
    else:
        # 添加收藏
        recipe = Recipe.query.get_or_404(recipe_id)
        favorite = UserRecipeFavorite(
            recipe_id=recipe.id,
            user_id=current_user.id
        )
        db.session.add(favorite)
        db.session.commit()
        return jsonify({'action': 'favorited'})

# 食谱评分API
@api_bp.route('/rate-recipe/<int:recipe_id>', methods=['POST'])
@login_required
def rate_recipe(recipe_id):
    """评价食谱"""
    from app.models_recipe_advanced import RecipeReview

    rating = request.form.get('rating', type=int)
    comment = request.form.get('comment', '')

    if not rating or rating < 1 or rating > 5:
        return jsonify({'success': False, 'message': '请提供有效的评分（1-5）'}), 400

    recipe = Recipe.query.get_or_404(recipe_id)

    # 检查用户是否已经评价过该食谱
    existing_review = RecipeReview.query.filter_by(
        recipe_id=recipe.id,
        user_id=current_user.id
    ).first()

    if existing_review:
        # 更新现有评价
        existing_review.rating = rating
        existing_review.comment = comment
        existing_review.updated_at = db.func.now()
        db.session.commit()
        return jsonify({'success': True, 'message': '评价已更新', 'action': 'updated'})
    else:
        # 创建新评价
        review = RecipeReview(
            recipe_id=recipe.id,
            user_id=current_user.id,
            rating=rating,
            comment=comment,
            is_public=True
        )
        db.session.add(review)
        db.session.commit()
        return jsonify({'success': True, 'message': '评价已提交', 'action': 'created'})

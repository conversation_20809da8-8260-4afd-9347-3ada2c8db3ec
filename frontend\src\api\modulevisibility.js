import request from '@/utils/request'

const modulevisibilityAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/modulevisibility',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/modulevisibility/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/modulevisibility',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/modulevisibility/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/modulevisibility/${id}`,
      method: 'delete'
    })
  }
}

export default modulevisibilityAPI
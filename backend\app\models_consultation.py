"""
在线咨询模型
用于处理首页在线咨询表单的数据存储和管理
"""

from datetime import datetime
from app import db
from sqlalchemy.dialects.mssql import DATETIME2


class OnlineConsultation(db.Model):
    """在线咨询模型"""
    __tablename__ = 'online_consultations'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    name = db.Column(db.String(50), nullable=False, comment='姓名')
    contact_type = db.Column(db.String(20), nullable=False, default='微信', comment='联系方式类型：微信、电话、邮箱')
    contact_value = db.Column(db.String(100), nullable=False, comment='联系方式值')
    content = db.Column(db.String(None), nullable=False, comment='咨询内容')  # NVARCHAR(MAX)
    status = db.Column(db.String(20), nullable=False, default='待处理', comment='状态：待处理、已回复、已关闭')
    reply_content = db.Column(db.String(None), nullable=True, comment='回复内容')  # NVARCHAR(MAX)
    reply_time = db.Column(DATETIME2(precision=1), nullable=True, comment='回复时间')
    reply_user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True, comment='回复人ID')
    source = db.Column(db.String(50), nullable=False, default='官网首页', comment='来源')
    ip_address = db.Column(db.String(50), nullable=True, comment='提交者IP地址')
    user_agent = db.Column(db.String(500), nullable=True, comment='浏览器信息')
    created_at = db.Column(DATETIME2(precision=1),
                          default=lambda: datetime.now().replace(microsecond=0),
                          nullable=False, comment='创建时间')
    updated_at = db.Column(DATETIME2(precision=1),
                          default=lambda: datetime.now().replace(microsecond=0),
                          onupdate=lambda: datetime.now().replace(microsecond=0),
                          nullable=False, comment='更新时间')

    # 关系
    reply_user = db.relationship('User', foreign_keys=[reply_user_id], backref='consultation_replies')

    def __repr__(self):
        return f'<OnlineConsultation {self.name}: {self.content[:50]}...>'

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'name': self.name,
            'contact_type': self.contact_type,
            'contact_value': self.contact_value,
            'content': self.content,
            'status': self.status,
            'reply_content': self.reply_content,
            'reply_time': self.reply_time.strftime('%Y-%m-%d %H:%M') if self.reply_time else None,
            'reply_user_name': self.reply_user.real_name if self.reply_user else None,
            'source': self.source,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M'),
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M')
        }

    @staticmethod
    def get_status_options():
        """获取状态选项"""
        return [
            ('待处理', '待处理'),
            ('已回复', '已回复'),
            ('已关闭', '已关闭')
        ]

    @staticmethod
    def get_contact_type_options():
        """获取联系方式类型选项"""
        return [
            ('微信', '微信'),
            ('电话', '电话'),
            ('邮箱', '邮箱')
        ]

    @classmethod
    def create_consultation(cls, name, contact_type, contact_value, content, ip_address=None, user_agent=None):
        """创建新的咨询记录 - 使用原始SQL避免时间字段问题"""
        from sqlalchemy import text

        try:
            # 按照README.md最佳实践：不包含 created_at 和 updated_at 字段
            sql = text("""
                INSERT INTO online_consultations
                (name, contact_type, contact_value, content, status, source, ip_address, user_agent)
                OUTPUT inserted.id
                VALUES
                (:name, :contact_type, :contact_value, :content, :status, :source, :ip_address, :user_agent)
            """)

            params = {
                'name': name,
                'contact_type': contact_type,
                'contact_value': contact_value,
                'content': content,
                'status': '待处理',
                'source': '官网首页',
                'ip_address': ip_address,
                'user_agent': user_agent
            }

            result = db.session.execute(sql, params)
            consultation_id = result.fetchone()[0]
            db.session.commit()

            # 返回创建的记录
            return cls.query.get(consultation_id)

        except Exception as e:
            db.session.rollback()
            raise

    def reply(self, reply_content, reply_user_id):
        """回复咨询 - 使用原始SQL避免时间字段问题"""
        from sqlalchemy import text

        try:
            # 按照README.md最佳实践：不手动设置时间字段
            sql = text("""
                UPDATE online_consultations
                SET reply_content = :reply_content,
                    reply_time = GETDATE(),
                    reply_user_id = :reply_user_id,
                    status = :status
                WHERE id = :id
            """)

            params = {
                'reply_content': reply_content,
                'reply_user_id': reply_user_id,
                'status': '已回复',
                'id': self.id
            }

            db.session.execute(sql, params)
            db.session.commit()

            # 不更新对象属性，避免触发ORM的自动刷新
            # 如果需要获取最新数据，应该重新查询

        except Exception as e:
            db.session.rollback()
            raise

    def close(self):
        """关闭咨询 - 使用原始SQL避免时间字段问题"""
        from sqlalchemy import text

        try:
            # 按照README.md最佳实践：不手动设置时间字段
            sql = text("""
                UPDATE online_consultations
                SET status = :status
                WHERE id = :id
            """)

            params = {
                'status': '已关闭',
                'id': self.id
            }

            db.session.execute(sql, params)
            db.session.commit()

            # 不更新对象属性，避免触发ORM的自动刷新
            # 如果需要获取最新数据，应该重新查询

        except Exception as e:
            db.session.rollback()
            raise

    @classmethod
    def get_pending_count(cls):
        """获取待处理咨询数量"""
        return cls.query.filter_by(status='待处理').count()

    @classmethod
    def get_statistics(cls):
        """获取咨询统计信息"""
        from sqlalchemy import func

        # 按状态统计
        status_stats = db.session.query(
            cls.status,
            func.count(cls.id).label('count')
        ).group_by(cls.status).all()

        # 按联系方式类型统计
        contact_type_stats = db.session.query(
            cls.contact_type,
            func.count(cls.id).label('count')
        ).group_by(cls.contact_type).all()

        # 按日期统计（最近7天）
        from datetime import timedelta
        seven_days_ago = datetime.now() - timedelta(days=7)
        daily_stats = db.session.query(
            func.cast(cls.created_at, db.Date).label('date'),
            func.count(cls.id).label('count')
        ).filter(cls.created_at >= seven_days_ago).group_by(
            func.cast(cls.created_at, db.Date)
        ).all()

        return {
            'status_stats': {item.status: item.count for item in status_stats},
            'contact_type_stats': {item.contact_type: item.count for item in contact_type_stats},
            'daily_stats': {item.date.strftime('%Y-%m-%d'): item.count for item in daily_stats}
        }

"""
User 序列化模式
"""

from marshmallow import Schema, fields, validate

class UserSchema(Schema):
    """User 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    username = fields.String(required=True)
    
    
    
    password_hash = fields.String(required=True)
    
    
    
    email = fields.String()
    
    
    
    real_name = fields.String()
    
    
    
    phone = fields.String()
    
    
    
    avatar = fields.String()
    
    
    
    last_login = fields.String()
    
    
    
    status = fields.Integer(required=True)
    
    
    
    area_id = fields.Integer()
    
    
    
    area_level = fields.Integer()
    
    
    
    created_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
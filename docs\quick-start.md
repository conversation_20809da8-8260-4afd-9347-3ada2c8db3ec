# 快速开始指南

## 环境要求

- Python 3.8+
- Node.js 16+
- SQL Server
- Redis (可选)

## 安装步骤

### 1. 后端设置

```bash
cd backend
pip install -r requirements.txt
```

### 2. 前端设置

```bash
cd frontend
npm install
```

### 3. 数据库配置

编辑 `backend/config.py` 文件，配置数据库连接：

```python
SQLALCHEMY_DATABASE_URI = 'mssql+pyodbc:///?odbc_connect=...'
```

### 4. 启动服务

```bash
# 启动后端
cd backend
python run.py

# 启动前端
cd frontend
npm run dev
```

### 5. 访问系统

- 新系统前端: http://localhost:3000
- 后端 API: http://localhost:5001/api/v1
- 现有系统: http://localhost:8080

## 迁移策略

1. **并存运行**: 新旧系统同时运行
2. **逐步迁移**: 按模块逐步迁移功能
3. **数据同步**: 确保数据一致性
4. **用户培训**: 提供用户培训和支持
5. **完全切换**: 最终完全切换到新系统

## 注意事项

- 迁移过程中保持数据备份
- 充分测试新功能
- 收集用户反馈
- 监控系统性能

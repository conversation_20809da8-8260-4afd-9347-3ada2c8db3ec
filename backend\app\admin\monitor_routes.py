"""
系统监控路由模块 - 功能已禁用
"""

from flask import redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from app.admin import system_bp
from app.utils import admin_required
@system_bp.route('/monitor')
@login_required
@admin_required
def monitor():
    """系统监控页面 - 功能已禁用"""
    flash('系统监控功能暂时不可用', 'warning')
    return redirect(url_for('system.logs'))

@system_bp.route('/monitor/refresh', methods=['GET'])
@login_required
@admin_required
def refresh_monitor():
    """刷新系统监控数据 - 功能已禁用"""
    return jsonify({'error': '系统监控功能暂时不可用'}), 403

"""
系统设置模块
"""

from flask import render_template, redirect, url_for, flash, request, jsonify, current_app
from flask_login import login_required, current_user
from app import db
from app.admin import system_bp
from app.utils import admin_required, log_activity
from app.models_system import SystemSetting, DatabaseBackup, SystemLog
import json
import os
import subprocess
import pyodbc
from datetime import datetime
import shutil
from werkzeug.utils import secure_filename
import logging

# 默认系统设置
DEFAULT_SETTINGS = {
    'basic': {
        'project_name': {
            'value': '智慧食堂平台',
            'description': '项目名称',
            'category': '基本设置'
        },
        'items_per_page': {
            'value': '10',
            'description': '每页显示条目数',
            'category': '基本设置'
        },
        'system_logo': {
            'value': '',
            'description': '系统Logo路径',
            'category': '基本设置'
        }
    },
    'security': {
        'password_min_length': {
            'value': '6',
            'description': '密码最小长度',
            'category': '安全设置'
        },
        'password_complexity': {
            'value': '0',
            'description': '密码复杂度要求（0-无要求，1-要求包含字母和数字，2-要求包含大小写字母和数字）',
            'category': '安全设置'
        },
        'session_timeout': {
            'value': '7',
            'description': '会话超时时间（天）',
            'category': '安全设置'
        }
    },
    'display': {
        'theme_color': {
            'value': 'primary',
            'description': '主题颜色',
            'category': '显示设置'
        },
        'show_welcome_message': {
            'value': '1',
            'description': '显示欢迎消息',
            'category': '显示设置'
        }
    }
}

def ensure_default_settings():
    """确保默认设置存在"""
    for category, settings in DEFAULT_SETTINGS.items():
        for key, setting in settings.items():
            existing = SystemSetting.query.filter_by(key=key).first()
            if not existing:
                SystemSetting.set_value(
                    key=key,
                    value=setting['value'],
                    description=setting['description'],
                    category=setting['category'],
                    value_type='string',  # 默认值类型
                    group='system',
                    is_public=False
                )

def get_settings_by_category():
    """按分类获取设置"""
    settings = SystemSetting.query.all()
    result = {}

    for setting in settings:
        category = setting.setting_type or '其他'
        if category not in result:
            result[category] = []
        result[category].append({
            'id': setting.id,
            'key': setting.key,
            'value': setting.value,
            'description': setting.description,
            'value_type': setting.value_type
        })

    return result

def get_project_name():
    """获取项目名称"""
    return SystemSetting.get_value('project_name', '智慧食堂平台')

def backup_database():
    """备份数据库"""
    try:
        # 确保备份目录存在
        backup_dir = os.path.join(current_app.root_path, '..', 'backups')
        os.makedirs(backup_dir, exist_ok=True)

        # 生成备份文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f'backup_{timestamp}.bak'
        backup_path = os.path.join(backup_dir, backup_filename)

        # 获取数据库连接信息
        conn_str = current_app.config['SQLALCHEMY_DATABASE_URI']
        if 'mssql+pyodbc' in conn_str:
            # SQL Server备份
            # 从连接字符串中提取数据库名称
            db_name = 'StudentsCMSSP'  # 默认数据库名

            # 创建连接
            conn = pyodbc.connect('DRIVER={SQL Server};SERVER=localhost\\SQLEXPRESS;DATABASE=master;Trusted_Connection=yes')
            cursor = conn.cursor()

            # 执行备份
            backup_sql = f"""
                BACKUP DATABASE [{db_name}]
                TO DISK = N'{backup_path}'
                WITH COMPRESSION, INIT, NAME = N'{db_name}-Full Database Backup', STATS = 10
            """
            cursor.execute(backup_sql)
            conn.commit()
            conn.close()

            # 获取文件大小
            file_size = os.path.getsize(backup_path)

            # 记录备份信息
            backup = DatabaseBackup(
                filename=backup_filename,
                backup_type='完整备份',
                size=file_size,
                description=f'SQL Server完整备份 - {timestamp}',
                created_by=current_user.id if current_user.is_authenticated else None
            )
            db.session.add(backup)
            db.session.commit()

            return {
                'success': True,
                'message': '数据库备份成功',
                'backup': backup.to_dict()
            }
        else:
            # SQLite备份（已弃用，但保留代码以防万一）
            return {
                'success': False,
                'message': '不支持的数据库类型'
            }
    except Exception as e:
        current_app.logger.error(f"数据库备份失败: {str(e)}")
        return {
            'success': False,
            'message': f'数据库备份失败: {str(e)}'
        }

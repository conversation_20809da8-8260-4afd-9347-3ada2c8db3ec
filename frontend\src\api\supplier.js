import request from '@/utils/request'

const supplierAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/supplier',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/supplier/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/supplier',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/supplier/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/supplier/${id}`,
      method: 'delete'
    })
  }
}

export default supplierAPI
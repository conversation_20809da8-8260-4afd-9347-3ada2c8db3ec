import request from '@/utils/request'

const recipeprocessAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/recipeprocess',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/recipeprocess/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/recipeprocess',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/recipeprocess/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/recipeprocess/${id}`,
      method: 'delete'
    })
  }
}

export default recipeprocessAPI
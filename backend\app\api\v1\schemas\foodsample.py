"""
FoodSample 序列化模式
"""

from marshmallow import Schema, fields, validate

class FoodSampleSchema(Schema):
    """FoodSample 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    sample_number = fields.String()
    
    
    
    recipe_id = fields.Integer(required=True)
    
    
    
    area_id = fields.Integer(required=True)
    
    
    
    meal_date = fields.Date(required=True)
    
    
    
    meal_type = fields.String(required=True)
    
    
    
    sample_image = fields.String(required=True)
    
    
    
    sample_quantity = fields.Float()
    
    
    
    sample_unit = fields.String()
    
    
    
    storage_location = fields.String(required=True)
    
    
    
    storage_temperature = fields.String()
    
    
    
    start_time = fields.String(required=True)
    
    
    
    end_time = fields.String(required=True)
    
    
    
    operator_id = fields.Integer(required=True)
    
    
    
    status = fields.String(required=True)
    
    
    
    destruction_time = fields.String()
    
    
    
    destruction_operator_id = fields.Integer()
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
"""
问题记录服务

提供食堂问题记录管理所需的数据服务，包括问题记录的创建、获取、更新和删除等。
"""

from datetime import datetime
from app import db
from app.models_daily_management import Issue, Photo
from app.utils.log_activity import log_activity


class IssueService:
    """问题记录服务类"""

    @staticmethod
    def get_issues_by_area(area_id=None, status=None):
        """
        获取区域的问题记录
        
        Args:
            area_id: 区域ID
            status: 问题状态（pending, processing, resolved）
            
        Returns:
            list: 问题记录列表
        """
        query = Issue.query
        
        if area_id:
            query = query.filter_by(area_id=area_id)
            
        if status:
            query = query.filter_by(status=status)
            
        return query.order_by(Issue.found_time.desc()).all()

    @staticmethod
    def get_issue_by_id(issue_id):
        """
        根据ID获取问题记录
        
        Args:
            issue_id: 问题记录ID
            
        Returns:
            Issue: 问题记录对象
        """
        return Issue.query.get(issue_id)

    @staticmethod
    def create_issue(data):
        """
        创建问题记录
        
        Args:
            data: 问题记录数据
            
        Returns:
            Issue: 创建的问题记录对象
        """
        issue = Issue(
            area_id=data.get('area_id'),
            issue_type=data.get('issue_type'),
            title=data.get('title'),
            description=data.get('description'),
            found_time=data.get('found_time'),
            found_by=data.get('found_by'),
            status=data.get('status', 'pending'),
            priority=data.get('priority', 'normal'),
            expected_resolve_time=data.get('expected_resolve_time'),
            responsible_person=data.get('responsible_person'),
            handling_process=data.get('handling_process'),
            resolve_time=data.get('resolve_time'),
            resolve_result=data.get('resolve_result')
        )
        
        db.session.add(issue)
        db.session.commit()
        
        # 记录活动
        log_activity('创建了问题记录', f'标题: {issue.title}', 'issue', issue.id)
        
        return issue

    @staticmethod
    def update_issue(issue_id, data):
        """
        更新问题记录
        
        Args:
            issue_id: 问题记录ID
            data: 问题记录数据
            
        Returns:
            Issue: 更新后的问题记录对象
        """
        issue = Issue.query.get(issue_id)
        
        if not issue:
            return None
            
        # 更新问题记录
        issue.issue_type = data.get('issue_type', issue.issue_type)
        issue.title = data.get('title', issue.title)
        issue.description = data.get('description', issue.description)
        issue.found_time = data.get('found_time', issue.found_time)
        issue.found_by = data.get('found_by', issue.found_by)
        issue.status = data.get('status', issue.status)
        issue.priority = data.get('priority', issue.priority)
        issue.expected_resolve_time = data.get('expected_resolve_time', issue.expected_resolve_time)
        issue.responsible_person = data.get('responsible_person', issue.responsible_person)
        issue.handling_process = data.get('handling_process', issue.handling_process)
        issue.resolve_time = data.get('resolve_time', issue.resolve_time)
        issue.resolve_result = data.get('resolve_result', issue.resolve_result)
        
        # 如果状态变为已解决，自动设置解决时间
        if issue.status == 'resolved' and not issue.resolve_time:
            issue.resolve_time = datetime.now()
        
        db.session.commit()
        
        # 记录活动
        log_activity('更新了问题记录', f'标题: {issue.title}', 'issue', issue.id)
        
        return issue

    @staticmethod
    def delete_issue(issue_id):
        """
        删除问题记录
        
        Args:
            issue_id: 问题记录ID
            
        Returns:
            bool: 是否删除成功
        """
        issue = Issue.query.get(issue_id)
        
        if not issue:
            return False
            
        # 删除关联的照片
        photos = Photo.query.filter_by(reference_type='issue', reference_id=issue_id).all()
        for photo in photos:
            db.session.delete(photo)
            
        # 记录活动
        issue_title = issue.title
        
        # 删除问题记录
        db.session.delete(issue)
        db.session.commit()
        
        # 记录活动
        log_activity('删除了问题记录', f'标题: {issue_title}', 'issue', issue_id)
        
        return True

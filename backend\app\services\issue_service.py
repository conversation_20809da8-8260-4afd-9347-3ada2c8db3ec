"""
Issue 服务层
"""

from typing import Optional, Dict, Any
from sqlalchemy.orm import Session
from app.models.models_daily_management import Issue
from app.utils.database import get_db_session

class IssueService:
    """Issue 服务"""

    @staticmethod
    def get_list(page: int = 1, per_page: int = 10, **filters):
        """获取Issue列表"""
        with get_db_session() as session:
            query = session.query(Issue)

            # 应用过滤条件
            for key, value in filters.items():
                if hasattr(Issue, key) and value is not None:
                    query = query.filter(getattr(Issue, key) == value)

            return query.paginate(
                page=page,
                per_page=per_page,
                error_out=False
            )

    @staticmethod
    def get_by_id(id: int) -> Optional[Issue]:
        """根据ID获取Issue"""
        with get_db_session() as session:
            return session.query(Issue).filter(Issue.id == id).first()

    @staticmethod
    def create(data: Dict[str, Any]) -> Issue:
        """创建Issue"""
        with get_db_session() as session:
            item = Issue(**data)
            session.add(item)
            session.commit()
            session.refresh(item)
            return item

    @staticmethod
    def update(id: int, data: Dict[str, Any]) -> Optional[Issue]:
        """更新Issue"""
        with get_db_session() as session:
            item = session.query(Issue).filter(Issue.id == id).first()
            if not item:
                return None

            for key, value in data.items():
                if hasattr(item, key):
                    setattr(item, key, value)

            session.commit()
            session.refresh(item)
            return item

    @staticmethod
    def delete(id: int) -> bool:
        """删除Issue"""
        with get_db_session() as session:
            item = session.query(Issue).filter(Issue.id == id).first()
            if not item:
                return False

            session.delete(item)
            session.commit()
            return True
import request from '@/utils/request'

const userroleAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/userrole',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/userrole/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/userrole',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/userrole/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/userrole/${id}`,
      method: 'delete'
    })
  }
}

export default userroleAPI
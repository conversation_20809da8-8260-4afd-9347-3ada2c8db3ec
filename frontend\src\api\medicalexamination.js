import request from '@/utils/request'

const medicalexaminationAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/medicalexamination',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/medicalexamination/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/medicalexamination',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/medicalexamination/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/medicalexamination/${id}`,
      method: 'delete'
    })
  }
}

export default medicalexaminationAPI
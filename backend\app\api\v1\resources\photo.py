"""
Photo API 资源
"""

from flask import request
from flask_restful import Resource
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.services.photo_service import PhotoService
from app.api.v1.schemas.photo import PhotoSchema
from app.utils.response import success_response, error_response

class PhotoListAPI(Resource):
    """Photo 列表 API"""

    @jwt_required()
    def get(self):
        """获取Photo列表"""
        try:
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 10, type=int)

            result = PhotoService.get_list(page=page, per_page=per_page)

            schema = PhotoSchema(many=True)
            return success_response(
                data={
                    'items': schema.dump(result.items),
                    'total': result.total,
                    'page': result.page,
                    'per_page': result.per_page
                }
            )
        except Exception as e:
            return error_response(message=str(e))

    @jwt_required()
    def post(self):
        """创建Photo"""
        try:
            data = request.get_json()
            schema = PhotoSchema()

            # 验证数据
            validated_data = schema.load(data)

            # 创建记录
            item = PhotoService.create(validated_data)

            return success_response(
                data=schema.dump(item),
                message='创建成功'
            )
        except Exception as e:
            return error_response(message=str(e))

class PhotoAPI(Resource):
    """Photo 详情 API"""

    @jwt_required()
    def get(self, id):
        """获取Photo详情"""
        try:
            item = PhotoService.get_by_id(id)
            if not item:
                return error_response(message='记录不存在', code=404)

            schema = PhotoSchema()
            return success_response(data=schema.dump(item))
        except Exception as e:
            return error_response(message=str(e))

    @jwt_required()
    def put(self, id):
        """更新Photo"""
        try:
            data = request.get_json()
            schema = PhotoSchema()

            # 验证数据
            validated_data = schema.load(data, partial=True)

            # 更新记录
            item = PhotoService.update(id, validated_data)
            if not item:
                return error_response(message='记录不存在', code=404)

            return success_response(
                data=schema.dump(item),
                message='更新成功'
            )
        except Exception as e:
            return error_response(message=str(e))

    @jwt_required()
    def delete(self, id):
        """删除Photo"""
        try:
            success = PhotoService.delete(id)
            if not success:
                return error_response(message='记录不存在', code=404)

            return success_response(message='删除成功')
        except Exception as e:
            return error_response(message=str(e))
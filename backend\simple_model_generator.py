#!/usr/bin/env python3
"""
简单的模型生成器 - 使用原始 SQL 查询避免 ODBC 精度问题
"""

import urllib.parse
from sqlalchemy import create_engine, text

def generate_simple_models():
    """生成简单的模型文件"""
    print("🔍 生成简单模型...")
    
    # 数据库连接配置
    DB_SERVER = '(local)\\SQLEXPRESS'
    DB_DATABASE = 'StudentsCMSSP'
    DB_DRIVER = 'SQL Server'
    
    # 构建连接字符串
    conn_str = f"DRIVER={{{DB_DRIVER}}};SERVER={DB_SERVER};DATABASE={DB_DATABASE};Trusted_Connection=yes"
    quoted_conn_str = urllib.parse.quote_plus(conn_str)
    connection_string = f"mssql+pyodbc:///?odbc_connect={quoted_conn_str}"
    
    try:
        # 创建引擎
        engine = create_engine(
            connection_string,
            echo=False,
            pool_pre_ping=True,
            pool_recycle=300
        )
        
        # 测试连接
        with engine.connect() as connection:
            result = connection.execute(text("SELECT 1"))
            print("✅ 数据库连接成功")
            
            # 使用原始 SQL 查询获取表信息
            tables_query = text("""
                SELECT TABLE_NAME 
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_TYPE = 'BASE TABLE' 
                AND TABLE_SCHEMA = 'dbo'
                ORDER BY TABLE_NAME
            """)
            
            result = connection.execute(tables_query)
            tables = [row[0] for row in result.fetchall()]
            
            print(f"📊 发现 {len(tables)} 个表")
            
            # 显示前10个表名
            print("📋 表名示例:")
            for table in tables[:10]:
                print(f"  - {table}")
            if len(tables) > 10:
                print(f"  ... 还有 {len(tables) - 10} 个表")
        
        # 生成简单的模型代码
        model_code = create_simple_model_code(tables)
        
        # 写入文件
        output_file = 'app/models/models_simple.py'
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(model_code)
        
        print(f"✅ 简单模型已生成到: {output_file}")
        return True
        
    except Exception as e:
        print(f"❌ 生成简单模型失败: {e}")
        return False

def create_simple_model_code(tables):
    """创建简单的模型代码"""
    
    code = '''"""
简单模型 - 使用 SQLAlchemy automap 自动映射
避免复杂的字段定义，直接使用数据库反射
"""

from datetime import datetime, date
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import UserMixin
from flask import current_app
from sqlalchemy.ext.automap import automap_base

# 获取数据库实例
def get_db():
    """获取数据库实例"""
    return current_app.extensions['sqlalchemy']

def init_simple_models():
    """初始化简单模型"""
    db = get_db()
    
    # 使用 automap 自动映射所有表
    Base = automap_base()
    
    # 准备映射（这会自动反射所有表）
    Base.prepare(autoload_with=db.engine)
    
    # 创建模型字典
    models = {}
    
    # 为每个表创建模型类
    for table_name, table_class in Base.classes.items():
        # 转换表名为类名
        class_name = table_name_to_class_name(table_name)
        
        # 创建动态模型类
        model_class = type(class_name, (table_class,), {
            '__doc__': f'自动映射的 {table_name} 模型',
            'to_dict': to_dict_method,
            'update_from_dict': update_from_dict_method
        })
        
        models[class_name] = model_class
    
    # 特殊处理 User 模型
    if hasattr(Base.classes, 'users'):
        class User(Base.classes.users, UserMixin):
            """用户模型 - 添加认证功能"""
            
            def set_password(self, password):
                """设置密码"""
                if hasattr(self, 'password_hash'):
                    self.password_hash = generate_password_hash(password)
            
            def check_password(self, password):
                """检查密码"""
                if hasattr(self, 'password_hash'):
                    return check_password_hash(self.password_hash, password)
                return False
            
            def to_dict(self):
                """转换为字典"""
                return to_dict_method(self)
            
            def update_from_dict(self, data):
                """从字典更新"""
                return update_from_dict_method(self, data)
            
            def __repr__(self):
                username = getattr(self, 'username', getattr(self, 'name', 'Unknown'))
                return f'<User {username}>'
        
        models['User'] = User
    
    return models

def to_dict_method(self):
    """通用的 to_dict 方法"""
    result = {}
    for column in self.__table__.columns:
        value = getattr(self, column.name)
        if isinstance(value, datetime):
            result[column.name] = value.isoformat()
        elif isinstance(value, date):
            result[column.name] = value.isoformat()
        else:
            result[column.name] = value
    return result

def update_from_dict_method(self, data):
    """通用的 update_from_dict 方法"""
    for key, value in data.items():
        if hasattr(self, key):
            setattr(self, key, value)

def table_name_to_class_name(table_name):
    """将表名转换为类名"""
    # 移除复数形式
    if table_name.endswith('ies'):
        table_name = table_name[:-3] + 'y'
    elif table_name.endswith('s') and not table_name.endswith('ss'):
        table_name = table_name[:-1]
    
    # 转换为驼峰命名
    parts = table_name.split('_')
    return ''.join(word.capitalize() for word in parts)

# 全局变量
_models = None

def get_models():
    """获取模型字典"""
    global _models
    if _models is None:
        _models = init_simple_models()
    return _models

def get_model(name):
    """获取指定模型"""
    models = get_models()
    return models.get(name)

# 常用模型的快捷访问
def get_user_model():
    return get_model('User')

def get_supplier_model():
    return get_model('Supplier')

def get_ingredient_model():
    return get_model('Ingredient')

def get_recipe_model():
    return get_model('Recipe')

def get_warehouse_model():
    return get_model('Warehouse')
'''
    
    # 添加表名列表作为注释
    code += f'''
# 数据库中的表 ({len(tables)} 个):
# {', '.join(tables[:20])}
# {'...' if len(tables) > 20 else ''}
'''
    
    return code

def main():
    """主函数"""
    print("🚀 开始生成简单模型")
    print("=" * 50)
    
    success = generate_simple_models()
    
    if success:
        print("\n" + "=" * 50)
        print("🎉 简单模型生成完成！")
        print("📝 下一步:")
        print("1. 更新应用初始化代码")
        print("2. 测试应用启动")
        print("3. 测试 API 端点")
    else:
        print("\n❌ 简单模型生成失败")
    
    return success

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)

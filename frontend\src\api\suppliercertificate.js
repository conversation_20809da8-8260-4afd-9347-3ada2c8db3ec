import request from '@/utils/request'

const suppliercertificateAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/suppliercertificate',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/suppliercertificate/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/suppliercertificate',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/suppliercertificate/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/suppliercertificate/${id}`,
      method: 'delete'
    })
  }
}

export default suppliercertificateAPI
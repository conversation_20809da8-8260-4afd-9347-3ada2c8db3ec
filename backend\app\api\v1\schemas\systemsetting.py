"""
SystemSetting 序列化模式
"""

from marshmallow import Schema, fields, validate

class SystemSettingSchema(Schema):
    """SystemSetting 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    key = fields.String(required=True)
    
    
    
    value = fields.String()
    
    
    
    value_type = fields.String(required=True)
    
    
    
    description = fields.String()
    
    
    
    group = fields.String()
    
    
    
    is_public = fields.Integer()
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    
    
    setting_type = fields.String()
    
    

    class Meta:
        ordered = True
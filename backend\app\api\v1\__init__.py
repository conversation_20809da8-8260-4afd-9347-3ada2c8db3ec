"""
API v1 初始化
"""

from flask import Blueprint
from flask_restful import Api

# 创建蓝图
api_v1_bp = Blueprint('api_v1', __name__)
api = Api(api_v1_bp)

# 导入资源

from app.api.v1.resources.user import UserListAPI, UserAPI

from app.api.v1.resources.role import RoleListAPI, RoleAPI

from app.api.v1.resources.userrole import UserRoleListAPI, UserRoleAPI

from app.api.v1.resources.suppliercategory import SupplierCategoryListAPI, SupplierCategoryAPI

from app.api.v1.resources.supplier import SupplierListAPI, SupplierAPI

from app.api.v1.resources.ingredientcategory import IngredientCategoryListAPI, IngredientCategoryAPI

from app.api.v1.resources.ingredient import IngredientListAPI, IngredientAPI

from app.api.v1.resources.supplierproduct import SupplierProductListAPI, SupplierProductAPI

from app.api.v1.resources.purchaseorder import PurchaseOrder<PERSON>istAP<PERSON>, PurchaseOrderAPI

from app.api.v1.resources.purchaseorderitem import PurchaseOrderItemListAPI, PurchaseOrderItemAPI

from app.api.v1.resources.recipecategory import RecipeCategoryListAPI, RecipeCategoryAPI

from app.api.v1.resources.recipe import RecipeListAPI, RecipeAPI

from app.api.v1.resources.recipeingredient import RecipeIngredientListAPI, RecipeIngredientAPI

from app.api.v1.resources.consumptionplan import ConsumptionPlanListAPI, ConsumptionPlanAPI

from app.api.v1.resources.consumptiondetail import ConsumptionDetailListAPI, ConsumptionDetailAPI

from app.api.v1.resources.foodsample import FoodSampleListAPI, FoodSampleAPI

from app.api.v1.resources.suppliercertificate import SupplierCertificateListAPI, SupplierCertificateAPI

from app.api.v1.resources.supplierschoolrelation import SupplierSchoolRelationListAPI, SupplierSchoolRelationAPI

from app.api.v1.resources.productspecparameter import ProductSpecParameterListAPI, ProductSpecParameterAPI

from app.api.v1.resources.deliveryinspection import DeliveryInspectionListAPI, DeliveryInspectionAPI

from app.api.v1.resources.deliveryiteminspection import DeliveryItemInspectionListAPI, DeliveryItemInspectionAPI

from app.api.v1.resources.purchaserequisition import PurchaseRequisitionListAPI, PurchaseRequisitionAPI

from app.api.v1.resources.purchaserequisitionitem import PurchaseRequisitionItemListAPI, PurchaseRequisitionItemAPI

from app.api.v1.resources.supplierdelivery import SupplierDeliveryListAPI, SupplierDeliveryAPI

from app.api.v1.resources.videoguide import VideoGuideListAPI, VideoGuideAPI

from app.api.v1.resources.deliveryitem import DeliveryItemListAPI, DeliveryItemAPI

from app.api.v1.resources.warehouse import WarehouseListAPI, WarehouseAPI

from app.api.v1.resources.storagelocation import StorageLocationListAPI, StorageLocationAPI

from app.api.v1.resources.inventory import InventoryListAPI, InventoryAPI

from app.api.v1.resources.stockin import StockInListAPI, StockInAPI

from app.api.v1.resources.stockinitem import StockInItemListAPI, StockInItemAPI

from app.api.v1.resources.stockindocument import StockInDocumentListAPI, StockInDocumentAPI

from app.api.v1.resources.ingredientinspection import IngredientInspectionListAPI, IngredientInspectionAPI

from app.api.v1.resources.stockout import StockOutListAPI, StockOutAPI

from app.api.v1.resources.stockoutitem import StockOutItemListAPI, StockOutItemAPI

from app.api.v1.resources.inventorycheck import InventoryCheckListAPI, InventoryCheckAPI

from app.api.v1.resources.inventorycheckitem import InventoryCheckItemListAPI, InventoryCheckItemAPI

from app.api.v1.resources.employee import EmployeeListAPI, EmployeeAPI

from app.api.v1.resources.healthcertificate import HealthCertificateListAPI, HealthCertificateAPI

from app.api.v1.resources.medicalexamination import MedicalExaminationListAPI, MedicalExaminationAPI

from app.api.v1.resources.dailyhealthcheck import DailyHealthCheckListAPI, DailyHealthCheckAPI

from app.api.v1.resources.trainingrecord import TrainingRecordListAPI, TrainingRecordAPI

from app.api.v1.resources.administrativearea import AdministrativeAreaListAPI, AdministrativeAreaAPI

from app.api.v1.resources.areachangehistory import AreaChangeHistoryListAPI, AreaChangeHistoryAPI

from app.api.v1.resources.recipeprocess import RecipeProcessListAPI, RecipeProcessAPI

from app.api.v1.resources.recipeprocessingredient import RecipeProcessIngredientListAPI, RecipeProcessIngredientAPI

from app.api.v1.resources.auditlog import AuditLogListAPI, AuditLogAPI

from app.api.v1.resources.inventoryalert import InventoryAlertListAPI, InventoryAlertAPI

from app.api.v1.resources.weeklymenu import WeeklyMenuListAPI, WeeklyMenuAPI

from app.api.v1.resources.weeklymenurecipe import WeeklyMenuRecipeListAPI, WeeklyMenuRecipeAPI

from app.api.v1.resources.notification import NotificationListAPI, NotificationAPI

from app.api.v1.resources.onlineconsultation import OnlineConsultationListAPI, OnlineConsultationAPI

from app.api.v1.resources.dailylog import DailyLogListAPI, DailyLogAPI

from app.api.v1.resources.inspectionrecord import InspectionRecordListAPI, InspectionRecordAPI

from app.api.v1.resources.diningcompanion import DiningCompanionListAPI, DiningCompanionAPI

from app.api.v1.resources.canteentrainingrecord import CanteenTrainingRecordListAPI, CanteenTrainingRecordAPI

from app.api.v1.resources.specialevent import SpecialEventListAPI, SpecialEventAPI

from app.api.v1.resources.issue import IssueListAPI, IssueAPI

from app.api.v1.resources.photo import PhotoListAPI, PhotoAPI

from app.api.v1.resources.inspectiontemplate import InspectionTemplateListAPI, InspectionTemplateAPI

from app.api.v1.resources.voucherdetail import VoucherDetailListAPI, VoucherDetailAPI

from app.api.v1.resources.homepagecarousel import HomepageCarouselListAPI, HomepageCarouselAPI

from app.api.v1.resources.materialbatch import MaterialBatchListAPI, MaterialBatchAPI

from app.api.v1.resources.tracedocument import TraceDocumentListAPI, TraceDocumentAPI

from app.api.v1.resources.batchflow import BatchFlowListAPI, BatchFlowAPI

from app.api.v1.resources.ingredientcategory import IngredientCategoryListAPI, IngredientCategoryAPI

from app.api.v1.resources.recipecategory import RecipeCategoryListAPI, RecipeCategoryAPI

from app.api.v1.resources.recipeprocess import RecipeProcessListAPI, RecipeProcessAPI

from app.api.v1.resources.recipeprocessingredient import RecipeProcessIngredientListAPI, RecipeProcessIngredientAPI

from app.api.v1.resources.suppliercertificate import SupplierCertificateListAPI, SupplierCertificateAPI

from app.api.v1.resources.purchaserequisition import PurchaseRequisitionListAPI, PurchaseRequisitionAPI

from app.api.v1.resources.purchaserequisitionitem import PurchaseRequisitionItemListAPI, PurchaseRequisitionItemAPI

from app.api.v1.resources.supplierdelivery import SupplierDeliveryListAPI, SupplierDeliveryAPI

from app.api.v1.resources.deliveryitem import DeliveryItemListAPI, DeliveryItemAPI

from app.api.v1.resources.warehouse import WarehouseListAPI, WarehouseAPI

from app.api.v1.resources.storagelocation import StorageLocationListAPI, StorageLocationAPI

from app.api.v1.resources.inventory import InventoryListAPI, InventoryAPI

from app.api.v1.resources.stockin import StockInListAPI, StockInAPI

from app.api.v1.resources.stockinitem import StockInItemListAPI, StockInItemAPI

from app.api.v1.resources.stockout import StockOutListAPI, StockOutAPI

from app.api.v1.resources.stockoutitem import StockOutItemListAPI, StockOutItemAPI

from app.api.v1.resources.inventorycheck import InventoryCheckListAPI, InventoryCheckAPI

from app.api.v1.resources.inventorycheckitem import InventoryCheckItemListAPI, InventoryCheckItemAPI

from app.api.v1.resources.standardunit import StandardUnitListAPI, StandardUnitAPI

from app.api.v1.resources.categoryunitmapping import CategoryUnitMappingListAPI, CategoryUnitMappingAPI

from app.api.v1.resources.productbatch import ProductBatchListAPI, ProductBatchAPI

from app.api.v1.resources.recipereview import RecipeReviewListAPI, RecipeReviewAPI

from app.api.v1.resources.recipereviewimage import RecipeReviewImageListAPI, RecipeReviewImageAPI

from app.api.v1.resources.recipereviewtag import RecipeReviewTagListAPI, RecipeReviewTagAPI

from app.api.v1.resources.recipeimprovementsuggestion import RecipeImprovementSuggestionListAPI, RecipeImprovementSuggestionAPI

from app.api.v1.resources.recipeversion import RecipeVersionListAPI, RecipeVersionAPI

from app.api.v1.resources.recipeingredientalternative import RecipeIngredientAlternativeListAPI, RecipeIngredientAlternativeAPI

from app.api.v1.resources.recipeseasonalinfo import RecipeSeasonalInfoListAPI, RecipeSeasonalInfoAPI

from app.api.v1.resources.recipetag import RecipeTagListAPI, RecipeTagAPI

from app.api.v1.resources.userrecipefavorite import UserRecipeFavoriteListAPI, UserRecipeFavoriteAPI

from app.api.v1.resources.usersearchhistory import UserSearchHistoryListAPI, UserSearchHistoryAPI

from app.api.v1.resources.supplierschoolrelation import SupplierSchoolRelationListAPI, SupplierSchoolRelationAPI

from app.api.v1.resources.productspecparameter import ProductSpecParameterListAPI, ProductSpecParameterAPI

from app.api.v1.resources.deliveryinspection import DeliveryInspectionListAPI, DeliveryInspectionAPI

from app.api.v1.resources.deliveryiteminspection import DeliveryItemInspectionListAPI, DeliveryItemInspectionAPI

from app.api.v1.resources.systemsetting import SystemSettingListAPI, SystemSettingAPI

from app.api.v1.resources.databasebackup import DatabaseBackupListAPI, DatabaseBackupAPI

from app.api.v1.resources.systemlog import SystemLogListAPI, SystemLogAPI

from app.api.v1.resources.modulevisibility import ModuleVisibilityListAPI, ModuleVisibilityAPI

from app.api.v1.resources.warehousenew import WarehouseNewListAPI, WarehouseNewAPI

from app.api.v1.resources.weeklymenurecipestemp import WeeklyMenuRecipesTempListAPI, WeeklyMenuRecipesTempAPI


# 注册路由

api.add_resource(UserListAPI, '/user')
api.add_resource(UserAPI, '/user/<int:id>')

api.add_resource(RoleListAPI, '/role')
api.add_resource(RoleAPI, '/role/<int:id>')

api.add_resource(UserRoleListAPI, '/userrole')
api.add_resource(UserRoleAPI, '/userrole/<int:id>')

api.add_resource(SupplierCategoryListAPI, '/suppliercategory')
api.add_resource(SupplierCategoryAPI, '/suppliercategory/<int:id>')

api.add_resource(SupplierListAPI, '/supplier')
api.add_resource(SupplierAPI, '/supplier/<int:id>')

api.add_resource(IngredientCategoryListAPI, '/ingredientcategory')
api.add_resource(IngredientCategoryAPI, '/ingredientcategory/<int:id>')

api.add_resource(IngredientListAPI, '/ingredient')
api.add_resource(IngredientAPI, '/ingredient/<int:id>')

api.add_resource(SupplierProductListAPI, '/supplierproduct')
api.add_resource(SupplierProductAPI, '/supplierproduct/<int:id>')

api.add_resource(PurchaseOrderListAPI, '/purchaseorder')
api.add_resource(PurchaseOrderAPI, '/purchaseorder/<int:id>')

api.add_resource(PurchaseOrderItemListAPI, '/purchaseorderitem')
api.add_resource(PurchaseOrderItemAPI, '/purchaseorderitem/<int:id>')

api.add_resource(RecipeCategoryListAPI, '/recipecategory')
api.add_resource(RecipeCategoryAPI, '/recipecategory/<int:id>')

api.add_resource(RecipeListAPI, '/recipe')
api.add_resource(RecipeAPI, '/recipe/<int:id>')

api.add_resource(RecipeIngredientListAPI, '/recipeingredient')
api.add_resource(RecipeIngredientAPI, '/recipeingredient/<int:id>')

api.add_resource(ConsumptionPlanListAPI, '/consumptionplan')
api.add_resource(ConsumptionPlanAPI, '/consumptionplan/<int:id>')

api.add_resource(ConsumptionDetailListAPI, '/consumptiondetail')
api.add_resource(ConsumptionDetailAPI, '/consumptiondetail/<int:id>')

api.add_resource(FoodSampleListAPI, '/foodsample')
api.add_resource(FoodSampleAPI, '/foodsample/<int:id>')

api.add_resource(SupplierCertificateListAPI, '/suppliercertificate')
api.add_resource(SupplierCertificateAPI, '/suppliercertificate/<int:id>')

api.add_resource(SupplierSchoolRelationListAPI, '/supplierschoolrelation')
api.add_resource(SupplierSchoolRelationAPI, '/supplierschoolrelation/<int:id>')

api.add_resource(ProductSpecParameterListAPI, '/productspecparameter')
api.add_resource(ProductSpecParameterAPI, '/productspecparameter/<int:id>')

api.add_resource(DeliveryInspectionListAPI, '/deliveryinspection')
api.add_resource(DeliveryInspectionAPI, '/deliveryinspection/<int:id>')

api.add_resource(DeliveryItemInspectionListAPI, '/deliveryiteminspection')
api.add_resource(DeliveryItemInspectionAPI, '/deliveryiteminspection/<int:id>')

api.add_resource(PurchaseRequisitionListAPI, '/purchaserequisition')
api.add_resource(PurchaseRequisitionAPI, '/purchaserequisition/<int:id>')

api.add_resource(PurchaseRequisitionItemListAPI, '/purchaserequisitionitem')
api.add_resource(PurchaseRequisitionItemAPI, '/purchaserequisitionitem/<int:id>')

api.add_resource(SupplierDeliveryListAPI, '/supplierdelivery')
api.add_resource(SupplierDeliveryAPI, '/supplierdelivery/<int:id>')

api.add_resource(VideoGuideListAPI, '/videoguide')
api.add_resource(VideoGuideAPI, '/videoguide/<int:id>')

api.add_resource(DeliveryItemListAPI, '/deliveryitem')
api.add_resource(DeliveryItemAPI, '/deliveryitem/<int:id>')

api.add_resource(WarehouseListAPI, '/warehouse')
api.add_resource(WarehouseAPI, '/warehouse/<int:id>')

api.add_resource(StorageLocationListAPI, '/storagelocation')
api.add_resource(StorageLocationAPI, '/storagelocation/<int:id>')

api.add_resource(InventoryListAPI, '/inventory')
api.add_resource(InventoryAPI, '/inventory/<int:id>')

api.add_resource(StockInListAPI, '/stockin')
api.add_resource(StockInAPI, '/stockin/<int:id>')

api.add_resource(StockInItemListAPI, '/stockinitem')
api.add_resource(StockInItemAPI, '/stockinitem/<int:id>')

api.add_resource(StockInDocumentListAPI, '/stockindocument')
api.add_resource(StockInDocumentAPI, '/stockindocument/<int:id>')

api.add_resource(IngredientInspectionListAPI, '/ingredientinspection')
api.add_resource(IngredientInspectionAPI, '/ingredientinspection/<int:id>')

api.add_resource(StockOutListAPI, '/stockout')
api.add_resource(StockOutAPI, '/stockout/<int:id>')

api.add_resource(StockOutItemListAPI, '/stockoutitem')
api.add_resource(StockOutItemAPI, '/stockoutitem/<int:id>')

api.add_resource(InventoryCheckListAPI, '/inventorycheck')
api.add_resource(InventoryCheckAPI, '/inventorycheck/<int:id>')

api.add_resource(InventoryCheckItemListAPI, '/inventorycheckitem')
api.add_resource(InventoryCheckItemAPI, '/inventorycheckitem/<int:id>')

api.add_resource(EmployeeListAPI, '/employee')
api.add_resource(EmployeeAPI, '/employee/<int:id>')

api.add_resource(HealthCertificateListAPI, '/healthcertificate')
api.add_resource(HealthCertificateAPI, '/healthcertificate/<int:id>')

api.add_resource(MedicalExaminationListAPI, '/medicalexamination')
api.add_resource(MedicalExaminationAPI, '/medicalexamination/<int:id>')

api.add_resource(DailyHealthCheckListAPI, '/dailyhealthcheck')
api.add_resource(DailyHealthCheckAPI, '/dailyhealthcheck/<int:id>')

api.add_resource(TrainingRecordListAPI, '/trainingrecord')
api.add_resource(TrainingRecordAPI, '/trainingrecord/<int:id>')

api.add_resource(AdministrativeAreaListAPI, '/administrativearea')
api.add_resource(AdministrativeAreaAPI, '/administrativearea/<int:id>')

api.add_resource(AreaChangeHistoryListAPI, '/areachangehistory')
api.add_resource(AreaChangeHistoryAPI, '/areachangehistory/<int:id>')

api.add_resource(RecipeProcessListAPI, '/recipeprocess')
api.add_resource(RecipeProcessAPI, '/recipeprocess/<int:id>')

api.add_resource(RecipeProcessIngredientListAPI, '/recipeprocessingredient')
api.add_resource(RecipeProcessIngredientAPI, '/recipeprocessingredient/<int:id>')

api.add_resource(AuditLogListAPI, '/auditlog')
api.add_resource(AuditLogAPI, '/auditlog/<int:id>')

api.add_resource(InventoryAlertListAPI, '/inventoryalert')
api.add_resource(InventoryAlertAPI, '/inventoryalert/<int:id>')

api.add_resource(WeeklyMenuListAPI, '/weeklymenu')
api.add_resource(WeeklyMenuAPI, '/weeklymenu/<int:id>')

api.add_resource(WeeklyMenuRecipeListAPI, '/weeklymenurecipe')
api.add_resource(WeeklyMenuRecipeAPI, '/weeklymenurecipe/<int:id>')

api.add_resource(NotificationListAPI, '/notification')
api.add_resource(NotificationAPI, '/notification/<int:id>')

api.add_resource(OnlineConsultationListAPI, '/onlineconsultation')
api.add_resource(OnlineConsultationAPI, '/onlineconsultation/<int:id>')

api.add_resource(DailyLogListAPI, '/dailylog')
api.add_resource(DailyLogAPI, '/dailylog/<int:id>')

api.add_resource(InspectionRecordListAPI, '/inspectionrecord')
api.add_resource(InspectionRecordAPI, '/inspectionrecord/<int:id>')

api.add_resource(DiningCompanionListAPI, '/diningcompanion')
api.add_resource(DiningCompanionAPI, '/diningcompanion/<int:id>')

api.add_resource(CanteenTrainingRecordListAPI, '/canteentrainingrecord')
api.add_resource(CanteenTrainingRecordAPI, '/canteentrainingrecord/<int:id>')

api.add_resource(SpecialEventListAPI, '/specialevent')
api.add_resource(SpecialEventAPI, '/specialevent/<int:id>')

api.add_resource(IssueListAPI, '/issue')
api.add_resource(IssueAPI, '/issue/<int:id>')

api.add_resource(PhotoListAPI, '/photo')
api.add_resource(PhotoAPI, '/photo/<int:id>')

api.add_resource(InspectionTemplateListAPI, '/inspectiontemplate')
api.add_resource(InspectionTemplateAPI, '/inspectiontemplate/<int:id>')

api.add_resource(VoucherDetailListAPI, '/voucherdetail')
api.add_resource(VoucherDetailAPI, '/voucherdetail/<int:id>')

api.add_resource(HomepageCarouselListAPI, '/homepagecarousel')
api.add_resource(HomepageCarouselAPI, '/homepagecarousel/<int:id>')

api.add_resource(MaterialBatchListAPI, '/materialbatch')
api.add_resource(MaterialBatchAPI, '/materialbatch/<int:id>')

api.add_resource(TraceDocumentListAPI, '/tracedocument')
api.add_resource(TraceDocumentAPI, '/tracedocument/<int:id>')

api.add_resource(BatchFlowListAPI, '/batchflow')
api.add_resource(BatchFlowAPI, '/batchflow/<int:id>')

api.add_resource(IngredientCategoryListAPI, '/ingredientcategory')
api.add_resource(IngredientCategoryAPI, '/ingredientcategory/<int:id>')

api.add_resource(RecipeCategoryListAPI, '/recipecategory')
api.add_resource(RecipeCategoryAPI, '/recipecategory/<int:id>')

api.add_resource(RecipeProcessListAPI, '/recipeprocess')
api.add_resource(RecipeProcessAPI, '/recipeprocess/<int:id>')

api.add_resource(RecipeProcessIngredientListAPI, '/recipeprocessingredient')
api.add_resource(RecipeProcessIngredientAPI, '/recipeprocessingredient/<int:id>')

api.add_resource(SupplierCertificateListAPI, '/suppliercertificate')
api.add_resource(SupplierCertificateAPI, '/suppliercertificate/<int:id>')

api.add_resource(PurchaseRequisitionListAPI, '/purchaserequisition')
api.add_resource(PurchaseRequisitionAPI, '/purchaserequisition/<int:id>')

api.add_resource(PurchaseRequisitionItemListAPI, '/purchaserequisitionitem')
api.add_resource(PurchaseRequisitionItemAPI, '/purchaserequisitionitem/<int:id>')

api.add_resource(SupplierDeliveryListAPI, '/supplierdelivery')
api.add_resource(SupplierDeliveryAPI, '/supplierdelivery/<int:id>')

api.add_resource(DeliveryItemListAPI, '/deliveryitem')
api.add_resource(DeliveryItemAPI, '/deliveryitem/<int:id>')

api.add_resource(WarehouseListAPI, '/warehouse')
api.add_resource(WarehouseAPI, '/warehouse/<int:id>')

api.add_resource(StorageLocationListAPI, '/storagelocation')
api.add_resource(StorageLocationAPI, '/storagelocation/<int:id>')

api.add_resource(InventoryListAPI, '/inventory')
api.add_resource(InventoryAPI, '/inventory/<int:id>')

api.add_resource(StockInListAPI, '/stockin')
api.add_resource(StockInAPI, '/stockin/<int:id>')

api.add_resource(StockInItemListAPI, '/stockinitem')
api.add_resource(StockInItemAPI, '/stockinitem/<int:id>')

api.add_resource(StockOutListAPI, '/stockout')
api.add_resource(StockOutAPI, '/stockout/<int:id>')

api.add_resource(StockOutItemListAPI, '/stockoutitem')
api.add_resource(StockOutItemAPI, '/stockoutitem/<int:id>')

api.add_resource(InventoryCheckListAPI, '/inventorycheck')
api.add_resource(InventoryCheckAPI, '/inventorycheck/<int:id>')

api.add_resource(InventoryCheckItemListAPI, '/inventorycheckitem')
api.add_resource(InventoryCheckItemAPI, '/inventorycheckitem/<int:id>')

api.add_resource(StandardUnitListAPI, '/standardunit')
api.add_resource(StandardUnitAPI, '/standardunit/<int:id>')

api.add_resource(CategoryUnitMappingListAPI, '/categoryunitmapping')
api.add_resource(CategoryUnitMappingAPI, '/categoryunitmapping/<int:id>')

api.add_resource(ProductBatchListAPI, '/productbatch')
api.add_resource(ProductBatchAPI, '/productbatch/<int:id>')

api.add_resource(RecipeReviewListAPI, '/recipereview')
api.add_resource(RecipeReviewAPI, '/recipereview/<int:id>')

api.add_resource(RecipeReviewImageListAPI, '/recipereviewimage')
api.add_resource(RecipeReviewImageAPI, '/recipereviewimage/<int:id>')

api.add_resource(RecipeReviewTagListAPI, '/recipereviewtag')
api.add_resource(RecipeReviewTagAPI, '/recipereviewtag/<int:id>')

api.add_resource(RecipeImprovementSuggestionListAPI, '/recipeimprovementsuggestion')
api.add_resource(RecipeImprovementSuggestionAPI, '/recipeimprovementsuggestion/<int:id>')

api.add_resource(RecipeVersionListAPI, '/recipeversion')
api.add_resource(RecipeVersionAPI, '/recipeversion/<int:id>')

api.add_resource(RecipeIngredientAlternativeListAPI, '/recipeingredientalternative')
api.add_resource(RecipeIngredientAlternativeAPI, '/recipeingredientalternative/<int:id>')

api.add_resource(RecipeSeasonalInfoListAPI, '/recipeseasonalinfo')
api.add_resource(RecipeSeasonalInfoAPI, '/recipeseasonalinfo/<int:id>')

api.add_resource(RecipeTagListAPI, '/recipetag')
api.add_resource(RecipeTagAPI, '/recipetag/<int:id>')

api.add_resource(UserRecipeFavoriteListAPI, '/userrecipefavorite')
api.add_resource(UserRecipeFavoriteAPI, '/userrecipefavorite/<int:id>')

api.add_resource(UserSearchHistoryListAPI, '/usersearchhistory')
api.add_resource(UserSearchHistoryAPI, '/usersearchhistory/<int:id>')

api.add_resource(SupplierSchoolRelationListAPI, '/supplierschoolrelation')
api.add_resource(SupplierSchoolRelationAPI, '/supplierschoolrelation/<int:id>')

api.add_resource(ProductSpecParameterListAPI, '/productspecparameter')
api.add_resource(ProductSpecParameterAPI, '/productspecparameter/<int:id>')

api.add_resource(DeliveryInspectionListAPI, '/deliveryinspection')
api.add_resource(DeliveryInspectionAPI, '/deliveryinspection/<int:id>')

api.add_resource(DeliveryItemInspectionListAPI, '/deliveryiteminspection')
api.add_resource(DeliveryItemInspectionAPI, '/deliveryiteminspection/<int:id>')

api.add_resource(SystemSettingListAPI, '/systemsetting')
api.add_resource(SystemSettingAPI, '/systemsetting/<int:id>')

api.add_resource(DatabaseBackupListAPI, '/databasebackup')
api.add_resource(DatabaseBackupAPI, '/databasebackup/<int:id>')

api.add_resource(SystemLogListAPI, '/systemlog')
api.add_resource(SystemLogAPI, '/systemlog/<int:id>')

api.add_resource(ModuleVisibilityListAPI, '/modulevisibility')
api.add_resource(ModuleVisibilityAPI, '/modulevisibility/<int:id>')

api.add_resource(WarehouseNewListAPI, '/warehousenew')
api.add_resource(WarehouseNewAPI, '/warehousenew/<int:id>')

api.add_resource(WeeklyMenuRecipesTempListAPI, '/weeklymenurecipestemp')
api.add_resource(WeeklyMenuRecipesTempAPI, '/weeklymenurecipestemp/<int:id>')

import request from '@/utils/request'

const recipecategoryAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/recipecategory',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/recipecategory/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/recipecategory',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/recipecategory/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/recipecategory/${id}`,
      method: 'delete'
    })
  }
}

export default recipecategoryAPI
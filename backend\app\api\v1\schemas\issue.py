"""
Issue 序列化模式
"""

from marshmallow import Schema, fields, validate

class IssueSchema(Schema):
    """Issue 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    daily_log_id = fields.Integer(required=True)
    
    
    
    issue_type = fields.String(required=True)
    
    
    
    description = fields.String(required=True)
    
    
    
    status = fields.String()
    
    
    
    found_time = fields.String(required=True)
    
    
    
    fixed_time = fields.String()
    
    
    
    responsible_person = fields.String()
    
    
    
    verification_result = fields.String()
    
    
    
    photo_paths = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
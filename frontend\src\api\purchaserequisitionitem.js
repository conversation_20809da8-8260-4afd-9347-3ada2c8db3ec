import request from '@/utils/request'

const purchaserequisitionitemAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/purchaserequisitionitem',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/purchaserequisitionitem/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/purchaserequisitionitem',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/purchaserequisitionitem/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/purchaserequisitionitem/${id}`,
      method: 'delete'
    })
  }
}

export default purchaserequisitionitemAPI
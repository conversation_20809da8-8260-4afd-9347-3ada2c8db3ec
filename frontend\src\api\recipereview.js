import request from '@/utils/request'

const recipereviewAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/recipereview',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/recipereview/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/recipereview',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/recipereview/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/recipereview/${id}`,
      method: 'delete'
    })
  }
}

export default recipereviewAPI
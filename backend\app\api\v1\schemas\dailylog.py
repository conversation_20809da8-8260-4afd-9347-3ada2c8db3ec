"""
DailyLog 序列化模式
"""

from marshmallow import Schema, fields, validate

class DailyLogSchema(Schema):
    """DailyLog 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    log_date = fields.String(required=True)
    
    
    
    weather = fields.String()
    
    
    
    manager = fields.String()
    
    
    
    student_count = fields.Integer()
    
    
    
    teacher_count = fields.Integer()
    
    
    
    other_count = fields.Integer()
    
    
    
    breakfast_menu = fields.String()
    
    
    
    lunch_menu = fields.String()
    
    
    
    dinner_menu = fields.String()
    
    
    
    food_waste = fields.Float()
    
    
    
    special_events = fields.String()
    
    
    
    operation_summary = fields.String()
    
    
    
    area_id = fields.Integer()
    
    
    
    created_by = fields.Integer()
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
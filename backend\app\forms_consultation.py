"""
在线咨询表单
用于处理首页在线咨询表单的验证和处理
"""

from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, SelectField, SubmitField
from wtforms.validators import DataRequired, Length, ValidationError
import re


class OnlineConsultationForm(FlaskForm):
    """在线咨询表单"""
    name = StringField('姓名', validators=[
        DataRequired(message='请输入您的姓名'),
        Length(min=2, max=50, message='姓名长度应在2-50个字符之间')
    ])
    
    contact_type = SelectField('联系方式类型', 
                              choices=[('微信', '微信'), ('电话', '电话'), ('邮箱', '邮箱')],
                              default='微信',
                              validators=[DataRequired(message='请选择联系方式类型')])
    
    contact_value = StringField('联系方式', validators=[
        DataRequired(message='请输入您的联系方式'),
        Length(min=3, max=100, message='联系方式长度应在3-100个字符之间')
    ])
    
    content = TextAreaField('咨询内容', validators=[
        DataRequired(message='请输入咨询内容'),
        Length(min=10, max=1000, message='咨询内容长度应在10-1000个字符之间')
    ])
    
    submit = SubmitField('提交咨询')
    
    def validate_contact_value(self, field):
        """验证联系方式格式"""
        contact_type = self.contact_type.data
        contact_value = field.data
        
        if contact_type == '电话':
            # 验证手机号格式
            phone_pattern = r'^1[3-9]\d{9}$'
            if not re.match(phone_pattern, contact_value):
                raise ValidationError('请输入正确的手机号码格式')
        
        elif contact_type == '邮箱':
            # 验证邮箱格式
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, contact_value):
                raise ValidationError('请输入正确的邮箱格式')
        
        elif contact_type == '微信':
            # 验证微信号格式（字母、数字、下划线、减号，6-20位）
            wechat_pattern = r'^[a-zA-Z0-9_-]{6,20}$'
            if not re.match(wechat_pattern, contact_value):
                raise ValidationError('微信号应为6-20位字母、数字、下划线或减号组合')


class ConsultationReplyForm(FlaskForm):
    """咨询回复表单（管理员使用）"""
    reply_content = TextAreaField('回复内容', validators=[
        DataRequired(message='请输入回复内容'),
        Length(min=10, max=2000, message='回复内容长度应在10-2000个字符之间')
    ])
    
    submit = SubmitField('发送回复')


class ConsultationSearchForm(FlaskForm):
    """咨询搜索表单"""
    keyword = StringField('关键词', validators=[
        Length(max=100, message='关键词长度不能超过100个字符')
    ])
    
    status = SelectField('状态', 
                        choices=[('', '全部状态'), ('待处理', '待处理'), ('已回复', '已回复'), ('已关闭', '已关闭')],
                        default='')
    
    contact_type = SelectField('联系方式类型',
                              choices=[('', '全部类型'), ('微信', '微信'), ('电话', '电话'), ('邮箱', '邮箱')],
                              default='')
    
    submit = SubmitField('搜索')

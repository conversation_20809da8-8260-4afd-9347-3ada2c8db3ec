"""
RecipeProcessIngredient 序列化模式
"""

from marshmallow import Schema, fields, validate

class RecipeProcessIngredientSchema(Schema):
    """RecipeProcessIngredient 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    process_id = fields.Integer(required=True)
    
    
    
    ingredient_id = fields.Integer(required=True)
    
    
    
    quantity = fields.Float(required=True)
    
    
    
    unit = fields.String(required=True)
    
    
    
    processing_method = fields.String()
    
    
    
    notes = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
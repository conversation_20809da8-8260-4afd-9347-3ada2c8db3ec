import request from '@/utils/request'

const supplierdeliveryAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/supplierdelivery',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/supplierdelivery/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/supplierdelivery',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/supplierdelivery/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/supplierdelivery/${id}`,
      method: 'delete'
    })
  }
}

export default supplierdeliveryAPI
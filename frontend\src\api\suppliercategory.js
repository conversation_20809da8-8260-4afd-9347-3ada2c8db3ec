import request from '@/utils/request'

const suppliercategoryAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/suppliercategory',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/suppliercategory/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/suppliercategory',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/suppliercategory/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/suppliercategory/${id}`,
      method: 'delete'
    })
  }
}

export default suppliercategoryAPI
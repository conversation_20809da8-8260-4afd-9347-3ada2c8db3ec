"""
Supplier 序列化模式
"""

from marshmallow import Schema, fields, validate

class SupplierSchema(Schema):
    """Supplier 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    name = fields.String(required=True)
    
    
    
    contact_person = fields.String(required=True)
    
    
    
    phone = fields.String(required=True)
    
    
    
    email = fields.String()
    
    
    
    address = fields.String(required=True)
    
    
    
    business_license = fields.String(required=True)
    
    
    
    tax_id = fields.String()
    
    
    
    bank_name = fields.String()
    
    
    
    bank_account = fields.String()
    
    
    
    status = fields.Integer(required=True)
    
    
    
    rating = fields.Float()
    
    
    
    category_id = fields.Integer()
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
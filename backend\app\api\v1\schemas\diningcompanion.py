"""
DiningCompanion 序列化模式
"""

from marshmallow import Schema, fields, validate

class DiningCompanionSchema(Schema):
    """DiningCompanion 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    daily_log_id = fields.Integer(required=True)
    
    
    
    companion_name = fields.String(required=True)
    
    
    
    companion_role = fields.String(required=True)
    
    
    
    meal_type = fields.String(required=True)
    
    
    
    dining_time = fields.String(required=True)
    
    
    
    taste_rating = fields.Integer()
    
    
    
    hygiene_rating = fields.Integer()
    
    
    
    service_rating = fields.Integer()
    
    
    
    comments = fields.String()
    
    
    
    suggestions = fields.String()
    
    
    
    photo_paths = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
from flask import render_template, redirect, url_for, flash, request, current_app, jsonify
from flask_login import login_required, current_user
from app import db
from app.main import main_bp
from app.models import Supplier, Ingredient, Recipe, FoodSample, PurchaseOrder, WeeklyMenu, WeeklyMenuRecipe
from datetime import datetime, date, timedelta
from app.utils.process_status import (
    get_menu_plan_status, get_purchase_plan_status,
    get_storage_in_status, get_consumption_plan_status, get_storage_out_status,
    get_inventory_status, get_samples_status, get_tracing_status,
    get_today_tasks, calculate_progress_percentage
)
from app.models_daily_management import DiningCompanion, Photo, DailyLog
from sqlalchemy import desc

@main_bp.route('/')
def index():
    """首页"""
    if current_user.is_authenticated:
        # 获取各环节状态
        menu_plan = get_menu_plan_status()
        purchase_plan = get_purchase_plan_status()
        storage_in = get_storage_in_status()
        consumption_plan = get_consumption_plan_status()
        storage_out = get_storage_out_status()
        inventory = get_inventory_status()
        samples = get_samples_status()
        tracing = get_tracing_status()

        # 获取今日待办任务
        today_tasks = get_today_tasks()

        # 计算整体进度
        all_steps = [menu_plan, purchase_plan, storage_in,
                    consumption_plan, storage_out, inventory, samples, tracing]
        progress_percentage = calculate_progress_percentage(all_steps)

        # 获取可用路由
        available_routes = []

        # 数据现在通过API获取，这里只需要传递空的占位数据
        recent_companions = []
        today_menu = {
            '早餐': None,
            '午餐': None,
            '晚餐': None
        }

        # 检查是否是新注册用户（今天创建的学校）
        is_new_user = False
        if current_user.area and current_user.area.created_at:
            try:
                # 处理不同类型的 created_at 字段
                if hasattr(current_user.area.created_at, 'date'):
                    # datetime 对象
                    area_date = current_user.area.created_at.date()
                elif isinstance(current_user.area.created_at, str):
                    # 字符串类型，尝试解析
                    from datetime import datetime as dt
                    area_date = dt.strptime(current_user.area.created_at[:10], '%Y-%m-%d').date()
                else:
                    # 其他类型，跳过检查
                    area_date = None

                if area_date:
                    is_new_user = area_date == datetime.now().date()
            except (ValueError, AttributeError, TypeError):
                # 如果解析失败，默认不是新用户
                is_new_user = False

        # 使用改版后的食堂管理仪表盘，突出食堂日常管理功能
        return render_template('main/canteen_dashboard_new.html',
                              title='食堂管理仪表盘',
                              now=datetime.now(),
                              is_new_user=is_new_user,
                              menu_plan=menu_plan,
                              purchase_plan=purchase_plan,
                              storage_in=storage_in,
                              consumption_plan=consumption_plan,
                              storage_out=storage_out,
                              inventory=inventory,
                              samples=samples,
                              tracing=tracing,
                              progress_percentage=progress_percentage,
                              today_tasks=today_tasks,
                              available_routes=available_routes,
                              recent_companions=recent_companions,
                              today_menu=today_menu)
    return render_template('main/index.html', title='首页', now=datetime.now())

@main_bp.route('/suppliers')
@login_required
def suppliers():
    """供应商管理（重定向到新的供应商管理模块）"""
    return redirect(url_for('supplier.index'))

@main_bp.route('/ingredients')
@login_required
def ingredients():
    """食材管理 - 实现学校级数据隔离"""
    page = request.args.get('page', 1, type=int)

    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 构建查询 - 只显示用户学校的食材和全局食材
    query = Ingredient.query.filter(
        db.or_(
            Ingredient.area_id.in_(area_ids),  # 用户学校的食材
            Ingredient.is_global == True,      # 全局食材（系统预设）
            Ingredient.area_id.is_(None)       # 兼容旧数据（无area_id的食材）
        )
    )

    ingredients = query.order_by(Ingredient.id.desc()).paginate(
        page=page,
        per_page=current_app.config['ITEMS_PER_PAGE'],
        error_out=0
    )
    return render_template('main/ingredients.html', title='食材管理', ingredients=ingredients, now=datetime.now())

@main_bp.route('/recipes')
@login_required
def recipes():
    """食谱管理 - 实现学校级数据隔离"""
    page = request.args.get('page', 1, type=int)

    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 构建查询 - 只显示用户学校的食谱和全局食谱
    query = Recipe.query.filter(
        db.or_(
            Recipe.area_id.in_(area_ids),  # 用户学校的食谱
            Recipe.is_global == True,      # 全局食谱（系统预设）
            Recipe.area_id.is_(None)       # 兼容旧数据（无area_id的食谱）
        )
    )

    recipes = query.order_by(Recipe.id.desc()).paginate(
        page=page,
        per_page=current_app.config['ITEMS_PER_PAGE'],
        error_out=0
    )
    return render_template('main/recipes.html', title='食谱管理', recipes=recipes, now=datetime.now())

@main_bp.route('/food-samples')
@login_required
def food_samples():
    """留样管理"""
    page = request.args.get('page', 1, type=int)
    samples = FoodSample.query.order_by(FoodSample.id.desc()).paginate(
        page=page,
        per_page=current_app.config['ITEMS_PER_PAGE'],
        error_out=0
    )
    return render_template('main/food_samples.html', title='留样管理', samples=samples, now=datetime.now())

@main_bp.route('/purchase-orders')
@login_required
def purchase_orders():
    """采购订单"""
    page = request.args.get('page', 1, type=int)
    orders = PurchaseOrder.query.order_by(PurchaseOrder.id.desc()).paginate(
        page=page,
        per_page=current_app.config['ITEMS_PER_PAGE'],
        error_out=0
    )
    return render_template('main/purchase_orders.html', title='采购订单', orders=orders, now=datetime.now())


@main_bp.route('/theme-demo')
def theme_demo():
    """主题配色演示页面"""
    return render_template('theme_demo.html', title='主题配色演示', now=datetime.now())

@main_bp.route('/theme-test')
def theme_test():
    """增强版主题切换器测试页面"""
    return render_template('theme_test.html', title='主题切换功能测试', now=datetime.now())

@main_bp.route('/test-csp')
def test_csp():
    """CSP nonce 测试页面"""
    return render_template('test_csp.html', title='CSP 测试页面', now=datetime.now())

@main_bp.route('/mobile-demo')
def mobile_demo():
    """移动端优化演示页面"""
    return render_template('mobile-demo.html', title='移动端优化演示', now=datetime.now())

@main_bp.route('/mobile-menu-test')
def mobile_menu_test():
    """移动端菜单测试页面"""
    return render_template('mobile_menu_test.html', title='移动端菜单测试', now=datetime.now())

@main_bp.route('/help')
def help():
    """系统帮助文档"""
    return render_template('main/help.html', title='系统帮助文档', now=datetime.now())

@main_bp.route('/layout-test')
def layout_test():
    """左右布局测试页面"""
    return render_template('layout_test.html', title='左右布局测试', now=datetime.now())



"""
命令行工具
"""

import click
from flask.cli import with_appcontext
from app import db
from app.models import User, Role, AdministrativeArea, SupplierCategory, Supplier
from werkzeug.security import generate_password_hash
import os
import datetime

def register_commands(app):
    """注册命令行命令"""

    @app.cli.command('init-db')
    @with_appcontext
    def init_db():
        """初始化数据库"""
        db.create_all()
        click.echo('数据库表创建成功')

    @app.cli.command('create-admin')
    @click.argument('username')
    @click.argument('password')
    @with_appcontext
    def create_admin(username, password):
        """创建管理员用户"""
        # 检查用户是否已存在
        user = User.query.filter_by(username=username).first()
        if user:
            click.echo(f'用户 {username} 已存在')
            return

        # 创建管理员角色
        admin_role = Role.query.filter_by(name='管理员').first()
        if not admin_role:
            admin_role = Role(name='管理员', description='系统管理员')
            db.session.add(admin_role)
            db.session.flush()

        # 创建管理员用户
        admin = User(
            username=username,
            password_hash=generate_password_hash(password),
            real_name='系统管理员',
            status=1
        )
        db.session.add(admin)
        db.session.commit()
        click.echo(f'管理员用户 {username} 创建成功')

    @app.cli.command('backup-db')
    @with_appcontext
    def backup_db():
        """备份数据库"""
        # 确保备份目录存在
        backup_dir = os.path.join(app.root_path, '..', 'backups')
        os.makedirs(backup_dir, exist_ok=1)

        # 生成备份文件名
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_file = os.path.join(backup_dir, f'backup_{timestamp}.db')

        # 获取数据库文件路径
        db_path = app.config['SQLALCHEMY_DATABASE_URI'].replace('sqlite:///', '')

        # 复制数据库文件
        import shutil
        shutil.copy2(db_path, backup_file)
        click.echo(f'数据库备份成功: {backup_file}')

    @app.cli.command('add-traceability-tables')
    @with_appcontext
    def add_traceability_tables():
        """添加食材溯源模块的数据库表"""
        from migrations.add_material_traceability_tables import run_migration
        run_migration()
        click.echo('食材溯源模块的数据库表添加完成')

    @app.cli.command('add-system-settings-tables')
    @with_appcontext
    def add_system_settings_tables():
        """添加系统设置相关的数据库表"""
        from migrations.add_system_settings_tables import run_migration
        run_migration()
        click.echo('系统设置相关的数据库表添加完成')

    @app.cli.command('add-suppliers')
    @click.argument('phone')
    @with_appcontext
    def add_suppliers(phone):
        """为指定手机号添加8个不同类型的供应商"""
        # 查找用户
        user = User.query.filter_by(phone=phone).first()
        if not user:
            click.echo(f"用户 (手机号: {phone}) 不存在")
            return

        click.echo(f"为用户 {user.username} (手机号: {phone}) 添加供应商")

        # 添加供应商分类
        categories = [
            '肉类供应商',
            '蔬菜供应商',
            '水产供应商',
            '调味品供应商',
            '主食类供应商',
            '水果供应商',
            '干货供应商',
            '冷冻食品供应商'
        ]

        category_ids = {}

        for category_name in categories:
            category = SupplierCategory.query.filter_by(name=category_name).first()
            if not category:
                category = SupplierCategory(name=category_name)
                db.session.add(category)
                db.session.commit()
                click.echo(f"添加分类: {category_name}, ID: {category.id}")
            else:
                click.echo(f"分类已存在: {category_name}, ID: {category.id}")

            category_ids[category_name] = category.id

        # 添加供应商
        suppliers = [
            {
                'name': '湖南优质肉类供应有限公司',
                'category': '肉类供应商',
                'contact_person': '张先生',
                'phone': phone,
                'email': '<EMAIL>',
                'address': '湖南省长沙市岳麓区xxx路123号',
                'business_license': '**********',
                'tax_id': 'TX123456789',
                'bank_name': '中国农业银行',
                'bank_account': '****************',
                'status': 1,
                'rating': 4.5
            },
            {
                'name': '绿色蔬菜配送中心',
                'category': '蔬菜供应商',
                'contact_person': '李经理',
                'phone': phone,
                'email': '<EMAIL>',
                'address': '湖南省长沙市雨花区xxx街45号',
                'business_license': '**********',
                'tax_id': 'TX987654321',
                'bank_name': '中国工商银行',
                'bank_account': '****************',
                'status': 1,
                'rating': 4.8
            },
            {
                'name': '湘江水产品批发有限公司',
                'category': '水产供应商',
                'contact_person': '王总',
                'phone': phone,
                'email': '<EMAIL>',
                'address': '湖南省长沙市开福区xxx路78号',
                'business_license': '**********',
                'tax_id': 'TX234567890',
                'bank_name': '中国建设银行',
                'bank_account': '****************',
                'status': 1,
                'rating': 4.2
            },
            {
                'name': '湘味调味品有限公司',
                'category': '调味品供应商',
                'contact_person': '刘经理',
                'phone': phone,
                'email': '<EMAIL>',
                'address': '湖南省长沙市天心区xxx街56号',
                'business_license': '**********',
                'tax_id': 'TX345678901',
                'bank_name': '中国银行',
                'bank_account': '****************',
                'status': 1,
                'rating': 4.6
            },
            {
                'name': '湖南粮油食品有限公司',
                'category': '主食类供应商',
                'contact_person': '赵总',
                'phone': phone,
                'email': '<EMAIL>',
                'address': '湖南省长沙市芙蓉区xxx路34号',
                'business_license': '**********',
                'tax_id': 'TX456789012',
                'bank_name': '交通银行',
                'bank_account': '****************',
                'status': 1,
                'rating': 4.7
            },
            {
                'name': '鲜果配送服务有限公司',
                'category': '水果供应商',
                'contact_person': '钱经理',
                'phone': phone,
                'email': '<EMAIL>',
                'address': '湖南省长沙市望城区xxx路67号',
                'business_license': '**********',
                'tax_id': 'TX567890123',
                'bank_name': '招商银行',
                'bank_account': '****************',
                'status': 1,
                'rating': 4.9
            },
            {
                'name': '湖南干货批发中心',
                'category': '干货供应商',
                'contact_person': '孙先生',
                'phone': phone,
                'email': '<EMAIL>',
                'address': '湖南省长沙市长沙县xxx街89号',
                'business_license': '**********',
                'tax_id': 'TX678901234',
                'bank_name': '中信银行',
                'bank_account': '****************',
                'status': 1,
                'rating': 4.3
            },
            {
                'name': '冷冻食品配送有限公司',
                'category': '冷冻食品供应商',
                'contact_person': '周经理',
                'phone': phone,
                'email': '<EMAIL>',
                'address': '湖南省长沙市宁乡市xxx路12号',
                'business_license': '**********',
                'tax_id': 'TX789012345',
                'bank_name': '浦发银行',
                'bank_account': '****************',
                'status': 1,
                'rating': 4.4
            }
        ]

        for supplier_data in suppliers:
            # 检查供应商是否已存在
            existing_supplier = Supplier.query.filter_by(
                name=supplier_data['name'],
                phone=supplier_data['phone']
            ).first()

            if existing_supplier:
                click.echo(f"供应商已存在: {supplier_data['name']}")
                continue

            # 获取分类ID
            category_id = category_ids.get(supplier_data['category'])
            if not category_id:
                click.echo(f"分类不存在: {supplier_data['category']}")
                continue

            # 创建新供应商
            supplier = Supplier(
                name=supplier_data['name'],
                category_id=category_id,
                contact_person=supplier_data['contact_person'],
                phone=supplier_data['phone'],
                email=supplier_data['email'],
                address=supplier_data['address'],
                business_license=supplier_data['business_license'],
                tax_id=supplier_data['tax_id'],
                bank_name=supplier_data['bank_name'],
                bank_account=supplier_data['bank_account'],
                status=supplier_data['status'],
                rating=supplier_data['rating']
            )

            db.session.add(supplier)
            db.session.commit()
            click.echo(f"添加供应商: {supplier_data['name']}, ID: {supplier.id}")

        click.echo("完成!")

    @app.cli.command('init-accounting-subjects')
    @with_appcontext
    def init_accounting_subjects():
        """为所有现有学校初始化会计科目"""
        from app.services.school_registration_service import SchoolRegistrationService

        # 获取所有学校级别的区域
        schools = AdministrativeArea.query.filter_by(level=3).all()

        if not schools:
            click.echo("没有找到学校区域")
            return

        click.echo(f"找到 {len(schools)} 个学校，开始初始化会计科目...")

        success_count = 0
        error_count = 0

        for school in schools:
            try:
                click.echo(f"正在为学校 '{school.name}' 初始化会计科目...")
                click.echo(f"✅ 学校 '{school.name}' 会计科目初始化完成")
                success_count += 1
            except Exception as e:
                db.session.rollback()
                click.echo(f"❌ 学校 '{school.name}' 会计科目初始化失败: {str(e)}")
                error_count += 1

        click.echo(f"初始化完成！成功: {success_count} 个，失败: {error_count} 个")

import request from '@/utils/request'

const photoAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/photo',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/photo/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/photo',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/photo/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/photo/${id}`,
      method: 'delete'
    })
  }
}

export default photoAPI
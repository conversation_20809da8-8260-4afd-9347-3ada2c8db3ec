"""
系统设置路由模块
"""

from flask import render_template, redirect, url_for, flash, request, current_app
from flask_login import login_required, current_user
from app import db
from app.admin import system_bp
from app.utils import admin_required
from app.utils.log_activity import log_activity
from app.models_system import SystemSetting, SystemLog
from app.admin.system_settings import ensure_default_settings, get_settings_by_category
from datetime import datetime
from werkzeug.utils import secure_filename
from PIL import Image
import os

@system_bp.route('/dashboard')
@login_required
@admin_required
def dashboard():
    """系统管理仪表盘"""
    # 获取数据库统计信息
    from app.models import User, Supplier, Recipe, Ingredient

    # 简化的系统信息
    system_info = {
        'database': {
            'users': User.query.count(),
            'suppliers': Supplier.query.count() if 'Supplier' in globals() else 0,
            'recipes': Recipe.query.count() if 'Recipe' in globals() else 0,
            'ingredients': Ingredient.query.count() if 'Ingredient' in globals() else 0
        }
    }

    return render_template(
        'admin/system/dashboard.html',
        title='系统管理仪表盘',
        system_info=system_info,
        now=datetime.now()
    )

@system_bp.route('/settings')
@login_required
@admin_required
def settings():
    """系统设置页面"""
    # 确保默认设置存在
    ensure_default_settings()

    # 获取所有设置
    settings_by_category = get_settings_by_category()

    return render_template(
        'admin/system/settings.html',
        title='系统设置',
        settings=settings_by_category,
        now=datetime.now()
    )

def handle_logo_upload(logo_file):
    """处理LOGO文件上传"""
    if not logo_file or not logo_file.filename:
        return None

    # 检查文件类型
    allowed_extensions = {'png', 'jpg', 'jpeg', 'gif'}
    filename = secure_filename(logo_file.filename)
    if not filename or '.' not in filename:
        return None

    file_ext = filename.rsplit('.', 1)[1].lower()
    if file_ext not in allowed_extensions:
        return None

    # 生成唯一文件名
    unique_filename = f"logo_{datetime.now().strftime('%Y%m%d%H%M%S')}.{file_ext}"

    # 确保上传目录存在
    upload_folder = os.path.join(current_app.static_folder, 'uploads', 'system')
    os.makedirs(upload_folder, exist_ok=True)

    # 保存文件路径
    file_path = os.path.join(upload_folder, unique_filename)

    try:
        # 使用PIL处理图片
        img = Image.open(logo_file)

        # 调整图片大小（最大150x50，保持宽高比）
        img.thumbnail((150, 50), Image.Resampling.LANCZOS)

        # 保存处理后的图片
        img.save(file_path, optimize=True, quality=85)

        # 返回相对路径
        return f"/static/uploads/system/{unique_filename}"

    except Exception as e:
        current_app.logger.error(f"处理LOGO图片失败: {str(e)}")
        return None

@system_bp.route('/settings/update', methods=['POST'])
@login_required
@admin_required
def update_settings():
    """更新系统设置"""
    if not current_user.has_permission('setting', 'edit'):
        flash('您没有编辑系统设置的权限', 'danger')
        return redirect(url_for('system.settings'))

    try:
        # 处理LOGO上传
        logo_path = None
        if 'logo_file' in request.files:
            logo_file = request.files['logo_file']
            if logo_file and logo_file.filename:
                logo_path = handle_logo_upload(logo_file)
                if not logo_path:
                    flash('LOGO上传失败，请检查文件格式和大小', 'warning')

        # 获取表单数据
        form_data = request.form.to_dict()

        # 更新设置
        for key, value in form_data.items():
            if key.startswith('setting_'):
                setting_key = key.replace('setting_', '')
                value_type_key = f'value_type_{setting_key}'
                value_type = form_data.get(value_type_key, 'string')

                # 如果是system_logo且有新上传的文件，使用新路径
                if setting_key == 'system_logo' and logo_path:
                    value = logo_path

                SystemSetting.set_value(
                    key=setting_key,
                    value=value,
                    value_type=value_type,
                    group='system',
                    is_public=False
                )

        if logo_path:
            flash('系统设置已更新，LOGO已成功上传', 'success')
        else:
            flash('系统设置已更新', 'success')

        # 记录活动
        log_activity('update_settings', '更新系统设置', current_user.id)

        return redirect(url_for('system.settings'))
    except Exception as e:
        flash(f'更新系统设置失败: {str(e)}', 'danger')
        return redirect(url_for('system.settings'))

@system_bp.route('/backups')
@login_required
@admin_required
def backups():
    """数据库备份页面 - 功能已禁用"""
    flash('数据库备份功能暂时不可用', 'warning')
    return redirect(url_for('system.dashboard'))

@system_bp.route('/backups/create', methods=['POST'])
@login_required
@admin_required
def create_backup():
    """创建数据库备份 - 功能已禁用"""
    flash('数据库备份功能暂时不可用', 'warning')
    return redirect(url_for('system.dashboard'))

@system_bp.route('/backups/delete/<int:id>', methods=['POST'])
@login_required
@admin_required
def delete_backup(id):
    """删除数据库备份 - 功能已禁用"""
    flash('数据库备份功能暂时不可用', 'warning')
    return redirect(url_for('system.dashboard'))

@system_bp.route('/backups/download/<int:id>')
@login_required
@admin_required
def download_backup(id):
    """下载数据库备份 - 功能已禁用"""
    flash('数据库备份功能暂时不可用', 'warning')
    return redirect(url_for('system.dashboard'))

@system_bp.route('/logs')
@login_required
@admin_required
def logs():
    """系统日志页面 - 功能已禁用"""
    flash('系统日志功能暂时不可用', 'warning')
    return redirect(url_for('system.dashboard'))

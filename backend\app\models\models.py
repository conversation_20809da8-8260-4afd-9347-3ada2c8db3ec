"""
最简化的模型文件 - 避免所有 db=None 问题
只包含必要的类定义和方法，字段通过数据库反射自动获取
"""

from datetime import datetime, date
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import UserMixin
import json
from flask import session

# 全局变量，将在应用初始化时设置
db = None
login_manager = None

def format_datetime(dt, fmt):
    """简单的日期时间格式化函数"""
    if dt and hasattr(dt, "strftime"):
        return dt.strftime(fmt)
    return str(dt) if dt else None

# 简化的模型类 - 只包含方法，不包含字段定义
class User(UserMixin):
    """用户模型 - 字段通过数据库反射获取"""
    
    def set_password(self, password):
        """设置密码"""
        if hasattr(self, 'password_hash'):
            self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """检查密码"""
        if hasattr(self, 'password_hash'):
            return check_password_hash(self.password_hash, password)
        return False
    
    def __repr__(self):
        username = getattr(self, 'username', 'Unknown')
        return f'<User {username}>'

class Role:
    """角色模型"""
    
    def __repr__(self):
        name = getattr(self, 'name', 'Unknown')
        return f'<Role {name}>'

class UserRole:
    """用户角色关联模型"""
    
    def __repr__(self):
        user_id = getattr(self, 'user_id', 'Unknown')
        role_id = getattr(self, 'role_id', 'Unknown')
        return f'<UserRole user_id={user_id} role_id={role_id}>'

class Supplier:
    """供应商模型"""
    
    def __repr__(self):
        name = getattr(self, 'name', 'Unknown')
        return f'<Supplier {name}>'

class SupplierCategory:
    """供应商分类模型"""
    
    def __repr__(self):
        name = getattr(self, 'name', 'Unknown')
        return f'<SupplierCategory {name}>'

class SupplierProduct:
    """供应商产品模型"""
    
    def __repr__(self):
        name = getattr(self, 'product_name', getattr(self, 'name', 'Unknown'))
        return f'<SupplierProduct {name}>'

class Ingredient:
    """食材模型"""
    
    def __repr__(self):
        name = getattr(self, 'name', 'Unknown')
        return f'<Ingredient {name}>'

class IngredientCategory:
    """食材分类模型"""
    
    def __repr__(self):
        name = getattr(self, 'name', 'Unknown')
        return f'<IngredientCategory {name}>'

class Recipe:
    """菜谱模型"""
    
    def __repr__(self):
        name = getattr(self, 'name', 'Unknown')
        return f'<Recipe {name}>'

class RecipeCategory:
    """菜谱分类模型"""
    
    def __repr__(self):
        name = getattr(self, 'name', 'Unknown')
        return f'<RecipeCategory {name}>'

class RecipeIngredient:
    """菜谱食材关联模型"""
    
    def __repr__(self):
        recipe_id = getattr(self, 'recipe_id', 'Unknown')
        ingredient_id = getattr(self, 'ingredient_id', 'Unknown')
        return f'<RecipeIngredient recipe_id={recipe_id} ingredient_id={ingredient_id}>'

class PurchaseOrder:
    """采购订单模型"""
    
    def __repr__(self):
        order_number = getattr(self, 'order_number', getattr(self, 'id', 'Unknown'))
        return f'<PurchaseOrder {order_number}>'

class PurchaseOrderItem:
    """采购订单项模型"""
    
    def __repr__(self):
        order_id = getattr(self, 'order_id', 'Unknown')
        return f'<PurchaseOrderItem order_id={order_id}>'

class Warehouse:
    """仓库模型"""
    
    def __repr__(self):
        name = getattr(self, 'name', 'Unknown')
        return f'<Warehouse {name}>'

class Inventory:
    """库存模型"""
    
    def __repr__(self):
        ingredient_id = getattr(self, 'ingredient_id', 'Unknown')
        warehouse_id = getattr(self, 'warehouse_id', 'Unknown')
        return f'<Inventory ingredient_id={ingredient_id} warehouse_id={warehouse_id}>'

# 通用的 to_dict 方法
def to_dict_method(self):
    """通用的 to_dict 方法"""
    result = {}
    if hasattr(self, '__table__'):
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            if isinstance(value, datetime):
                result[column.name] = value.isoformat()
            elif isinstance(value, date):
                result[column.name] = value.isoformat()
            else:
                result[column.name] = value
    return result

# 为所有模型类添加 to_dict 方法
for model_class in [User, Role, UserRole, Supplier, SupplierCategory, SupplierProduct,
                   Ingredient, IngredientCategory, Recipe, RecipeCategory, RecipeIngredient,
                   PurchaseOrder, PurchaseOrderItem, Warehouse, Inventory]:
    model_class.to_dict = to_dict_method

def init_models():
    """初始化模型 - 在 db 设置后调用"""
    global db
    if db is None:
        raise RuntimeError("db 未设置，请先设置 models.db")
    
    print("✅ 模型初始化完成（使用数据库反射）")
    return True

# login_manager 用户加载函数
def load_user(user_id):
    """加载用户"""
    if db and hasattr(User, 'query'):
        return User.query.get(int(user_id))
    return None

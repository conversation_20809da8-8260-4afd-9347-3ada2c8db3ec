from datetime import datetime, date
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import UserMixin
# 延迟导入，避免循环导入
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager

# 这些将在应用初始化时设置
db = None
login_manager = None

def get_db():
    """获取数据库实例"""
    global db
    if db is None:
        from flask import current_app
        db = current_app.extensions['sqlalchemy']
    return db
import json
from flask import session
def format_datetime(dt, fmt):
    """简单的日期时间格式化函数"""
    if dt and hasattr(dt, "strftime"):
        return dt.strftime(fmt)
    return str(dt) if dt else None
from sqlalchemy.dialects.mssql import DATETIME2

# 暂时注释掉模型定义，将在应用启动后动态创建
# class StandardModel(db.Model):
#     """标准模型基类，包含通用字段和方法"""
#     __abstract__ = True

#     id = db.Column(db.Integer, primary_key=True, autoincrement=True)
#     created_at = db.Column(DATETIME2(precision=1),
#                           default=lambda: datetime.now().replace(microsecond=0),
#                           nullable=False)
#     updated_at = db.Column(DATETIME2(precision=1),
#                           default=lambda: datetime.now().replace(microsecond=0),
#                           onupdate=lambda: datetime.now().replace(microsecond=0),
#                           nullable=False)

class User(UserMixin, db.Model):
    """用户表"""
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password_hash = db.Column(db.String(128), nullable=False)
    email = db.Column(db.String(100), unique=True, nullable=True)
    real_name = db.Column(db.String(50), nullable=True)
    phone = db.Column(db.String(20), nullable=True)
    avatar = db.Column(db.String(200), nullable=True)
    last_login = db.Column(DATETIME2(precision=1), nullable=True)
    status = db.Column(db.Integer, default=1, nullable=False)  # 0-禁用, 1-启用
    area_id = db.Column(db.Integer, db.ForeignKey('administrative_areas.id'), nullable=True)
    area_level = db.Column(db.Integer, nullable=True)  # 1:县市区, 2:乡镇, 3:学校, 4:食堂
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系
    roles = db.relationship('Role', secondary='user_roles', backref=db.backref('users', lazy='dynamic'))

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def has_permission(self, module, action):
        """检查用户是否有指定模块的指定操作权限"""
        # 检查是否是系统管理员
        if self.is_admin():
            return 1

        for role in self.roles:
            permissions = json.loads(role.permissions)
            if '*' in permissions and '*' in permissions['*']:
                return 1
            if module in permissions and (action in permissions[module] or '*' in permissions[module]):
                return 1
        return 0

    def is_admin(self):
        """检查用户是否是系统管理员"""
        for role in self.roles:
            if role.name == '系统管理员':
                return 1
        return 0

    def has_role(self, role_name):
        """检查用户是否有指定角色"""
        for role in self.roles:
            if role.name == role_name:
                return 1
        return 0

    def can_access_area(self, area):
        """
        检查用户是否有权限访问指定区域的数据
        严格限制用户只能访问自己区域内的数据，不能访问任何其他区域或同类区域的信息
        """
        # 系统管理员可以访问所有区域
        if self.is_admin():
            return 1

        # 如果用户没有关联区域，则无权访问
        if not self.area:
            return 0

        # 用户可以访问自己的区域
        if self.area_id == area.id:
            return 1

        # 用户可以访问下级区域，但不能访问同级区域
        if area in self.area.get_descendants():
            return 1

        # 不再允许乡镇级别用户访问其他乡镇的学校，即使是标记为乡镇学校的学校
        # 只有当学校是该用户所在乡镇的下级区域时才能访问

        return 0

    def can_access_area_by_id(self, area_id):
        """
        根据区域ID检查用户是否有权限访问
        严格限制用户只能访问自己区域内的数据，不能访问任何其他区域或同类区域的信息
        """
        if not area_id:
            return 0

        # 系统管理员可以访问所有区域
        if self.is_admin():
            return 1

        # 如果用户没有关联区域，则无权访问
        if not self.area:
            return 0

        # 用户可以访问自己的区域
        if self.area_id == area_id:
            return 1

        # 获取区域对象
        area = db.session.query(AdministrativeArea).get(area_id)
        if not area:
            return 0

        # 用户可以访问下级区域，但不能访问同级区域
        if area in self.area.get_descendants():
            return 1

        # 不再允许乡镇级别用户访问其他乡镇的学校，即使是标记为乡镇学校的学校
        # 只有当学校是该用户所在乡镇的下级区域时才能访问

        return 0

    def get_accessible_areas(self):
        """
        获取用户可访问的所有区域
        严格限制用户只能访问自己区域内的数据，不能访问任何其他区域或同类区域的信息
        """
        # 系统管理员可以访问所有区域
        if self.is_admin():
            return db.session.query(AdministrativeArea).filter_by(status=1).all()

        # 如果用户没有关联区域，则无可访问区域
        if not self.area:
            return []

        # 用户可以访问自己的区域及其所有下级区域，但不能访问同级区域
        result = [self.area]
        result.extend(self.area.get_descendants())

        # 不再允许乡镇级别用户访问其他乡镇的学校，即使是标记为乡镇学校的学校
        # 只有当学校是该用户所在乡镇的下级区域时才能访问

        return result

    def get_current_area(self):
        """获取用户当前操作的区域"""
        # 从会话中获取当前区域ID
        current_area_id = session.get('current_area_id')

        # 如果会话中没有区域ID或者用户无权访问该区域，则使用用户关联的区域
        if not current_area_id or not self.can_access_area_by_id(current_area_id):
            return self.area

        return db.session.query(AdministrativeArea).get(current_area_id)

    @property
    def unread_notifications_count(self):
        """获取未读通知数量"""
        try:
            # 使用原始SQL查询，避免SQLAlchemy的ORM层
            from sqlalchemy import text
            from flask import current_app

            sql = text("""
                SELECT COUNT(*)
                FROM notifications
                WHERE user_id = :user_id AND is_read = 0
            """)

            # 获取一个新的数据库连接
            with db.engine.connect() as conn:
                result = conn.execute(sql, {"user_id": self.id})
                count = result.scalar()
                return count if count is not None else 0

        except Exception as e:
            # 记录错误但不中断应用
            from flask import current_app
            if current_app:
                current_app.logger.error(f"获取未读通知数量时出错: {str(e)}")
            return 0  # 出错时返回0

    @property
    def recent_notifications(self):
        """获取最近5条通知"""
        try:
            # 使用原始SQL查询，避免SQLAlchemy的ORM层
            from sqlalchemy import text
            from flask import current_app

            sql = text("""
                SELECT TOP 5 id, title, content, notification_type, level, reference_id, reference_type, is_read, created_at
                FROM notifications
                WHERE user_id = :user_id
                ORDER BY created_at DESC
            """)

            # 获取一个新的数据库连接
            with db.engine.connect() as conn:
                result = conn.execute(sql, {"user_id": self.id})
                notifications = []

                for row in result:
                    # 创建一个简单的通知对象字典
                    notification = {
                        'id': row.id,
                        'title': row.title,
                        'content': row.content,
                        'notification_type': row.notification_type,
                        'level': row.level,
                        'reference_id': row.reference_id,
                        'reference_type': row.reference_type,
                        'is_read': row.is_read,
                        'created_at': row.created_at
                    }
                    notifications.append(notification)

                return notifications

        except Exception as e:
            # 记录错误但不中断应用
            from flask import current_app
            if current_app:
                current_app.logger.error(f"获取最近通知时出错: {str(e)}")
            return []  # 出错时返回空列表

    def add_notification(self, title, content, notification_type, level=0, reference_id=None, reference_type=None):
        """添加通知"""
        notification = Notification(
            user_id=self.id,
            title=title,
            content=content,
            notification_type=notification_type,
            level=level,
            reference_id=reference_id,
            reference_type=reference_type
        )
        db.session.add(notification)
        db.session.commit()
        return notification

    def to_dict(self):
        area_info = None
        if self.area:
            area_info = {
                'id': self.area.id,
                'name': self.area.name,
                'level': self.area.level,
                'level_name': self.area.get_level_name()
            }

        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'real_name': self.real_name,
            'phone': self.phone,
            'avatar': self.avatar,
            'status': self.status,
            'area_id': self.area_id,
            'area_level': self.area_level,
            'area': area_info,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S'),
            'roles': [role.name for role in self.roles],
            'unread_notifications_count': self.unread_notifications_count
        }

@login_manager.user_loader
def load_user(id):
    return User.query.get(int(id))

class Role(db.Model):
    """角色表"""
    __tablename__ = 'roles'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), unique=True, nullable=False)
    description = db.Column(db.String(200), nullable=True)
    permissions = db.Column(db.Text, nullable=True)  # JSON格式的权限配置
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)

class UserRole(db.Model):
    """用户角色关联表"""
    __tablename__ = 'user_roles'

    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), primary_key=True)
    role_id = db.Column(db.Integer, db.ForeignKey('roles.id'), primary_key=True)

class SupplierCategory(db.Model):
    """供应商分类表"""
    __tablename__ = 'supplier_categories'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False)
    description = db.Column(db.String(500), nullable=True)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系
    suppliers = db.relationship('Supplier', backref='category', lazy='dynamic')

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S')
        }

class Supplier(db.Model):
    """供应商表"""
    __tablename__ = 'suppliers'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    contact_person = db.Column(db.String(50), nullable=False)
    phone = db.Column(db.String(20), nullable=False)
    email = db.Column(db.String(100), nullable=True)
    address = db.Column(db.String(200), nullable=False)
    business_license = db.Column(db.String(200), nullable=False)
    tax_id = db.Column(db.String(50), nullable=True)
    bank_name = db.Column(db.String(100), nullable=True)
    bank_account = db.Column(db.String(50), nullable=True)
    status = db.Column(db.Integer, default=1, nullable=False)  # 0-停用, 1-合作中
    rating = db.Column(db.Float, nullable=True)
    category_id = db.Column(db.Integer, db.ForeignKey('supplier_categories.id'), nullable=True)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系
    products = db.relationship('SupplierProduct', backref='supplier', lazy='dynamic')
    purchase_orders = db.relationship('PurchaseOrder', backref='supplier', lazy='dynamic')
    certificates = db.relationship('SupplierCertificate', backref='supplier', lazy='dynamic')
    deliveries = db.relationship('SupplierDelivery', backref='supplier', lazy='dynamic')

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'contact_person': self.contact_person,
            'phone': self.phone,
            'email': self.email,
            'address': self.address,
            'business_license': self.business_license,
            'tax_id': self.tax_id,
            'bank_name': self.bank_name,
            'bank_account': self.bank_account,
            'status': self.status,
            'rating': self.rating,
            'category_id': self.category_id,
            'category': self.category.name if self.category else None,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S'),
            'updated_at': format_datetime(self.updated_at, '%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

class IngredientCategory(db.Model):
    """食材分类表"""
    __tablename__ = 'ingredient_categories'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False)
    parent_id = db.Column(db.Integer, db.ForeignKey('ingredient_categories.id'), nullable=True)
    description = db.Column(db.String(200), nullable=True)
    # 这些字段在数据库中可能不存在，设置为可选
    # needs_inspection = db.Column(db.Boolean, default=False, nullable=True)  # 是否需要检验检疫
    # inspection_type = db.Column(db.String(50), nullable=True)  # 检验检疫类型
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系
    parent = db.relationship('IngredientCategory', remote_side=lambda: [IngredientCategory.id], backref='children')

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'parent_id': self.parent_id,
            'description': self.description,
            # 移除对不存在字段的引用
            # 'needs_inspection': self.needs_inspection,
            # 'inspection_type': self.inspection_type,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S')
        }

class Ingredient(db.Model):
    """基础食材表"""
    __tablename__ = 'ingredients'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    category = db.Column(db.String(50), nullable=False)
    category_id = db.Column(db.Integer, db.ForeignKey('ingredient_categories.id'), nullable=True)
    area_id = db.Column(db.Integer, db.ForeignKey('administrative_areas.id'), nullable=True)  # 学校级隔离字段
    unit = db.Column(db.String(20), nullable=False)
    standard_unit = db.Column(db.String(20), nullable=True)  # 标准单位
    base_image = db.Column(db.String(200), nullable=True)
    storage_temp = db.Column(db.String(50), nullable=True)
    storage_condition = db.Column(db.String(100), nullable=True)
    shelf_life = db.Column(db.Integer, nullable=True)  # 保质期(天)
    specification = db.Column(db.String(100), nullable=True)
    nutrition_info = db.Column(db.Text, nullable=True)  # JSON格式
    is_condiment = db.Column(db.Boolean, default=False, nullable=False)  # 是否为调味品
    is_global = db.Column(db.Boolean, default=False, nullable=False)  # 是否为全局食材（系统预设）
    status = db.Column(db.Integer, default=1, nullable=False)  # 0-停用, 1-启用
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系
    area = db.relationship('AdministrativeArea', foreign_keys=[area_id], backref='ingredients')  # 学校关联
    supplier_products = db.relationship('SupplierProduct', backref='ingredient', lazy='dynamic')
    recipe_ingredients = db.relationship('RecipeIngredient', backref=db.backref('ingredient'), lazy='dynamic')
    category_rel = db.relationship('IngredientCategory', backref='ingredients')

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'category': self.category,
            'category_id': self.category_id,
            'category_name': self.category_rel.name if self.category_rel else None,
            'area_id': self.area_id,
            'area_name': self.area.name if self.area else None,
            'unit': self.unit,
            'standard_unit': self.standard_unit,
            'base_image': self.base_image,
            'storage_temp': self.storage_temp,
            'storage_condition': self.storage_condition,
            'shelf_life': self.shelf_life,
            'specification': self.specification,
            'nutrition_info': self.nutrition_info,
            'is_condiment': self.is_condiment,
            'is_global': self.is_global,
            'status': self.status,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S') if hasattr(self.created_at, 'strftime') else self.created_at,
            'updated_at': format_datetime(self.updated_at, '%Y-%m-%d %H:%M:%S') if hasattr(self.updated_at, 'strftime') and self.updated_at else self.updated_at
        }

class SupplierProduct(db.Model):
    """供应商品表"""
    __tablename__ = 'supplier_products'

    id = db.Column(db.Integer, primary_key=True)
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'), nullable=False)
    ingredient_id = db.Column(db.Integer, db.ForeignKey('ingredients.id'), nullable=False)
    product_code = db.Column(db.String(50), nullable=True)  # 产品编码
    product_name = db.Column(db.String(100), nullable=True)  # 产品名称（可与食材名称不同）
    model_number = db.Column(db.String(50), nullable=True)
    specification = db.Column(db.String(100), nullable=True)
    price = db.Column(db.Numeric(10, 2), nullable=False)
    quality_cert = db.Column(db.String(200), nullable=False)
    quality_standard = db.Column(db.String(200), nullable=True)
    product_image = db.Column(db.String(200), nullable=True)
    lead_time = db.Column(db.Integer, nullable=True)  # 供货周期（天）
    min_order_quantity = db.Column(db.Float, nullable=True)
    is_available = db.Column(db.Integer, default=0, nullable=False)  # 0-未上架, 1-已上架
    shelf_status = db.Column(db.Integer, default=0, nullable=False)  # 0-待审核, 1-已审核, 2-已拒绝
    shelf_time = db.Column(DATETIME2(precision=1), nullable=True)  # 上架时间
    shelf_operator_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)  # 上架操作人
    description = db.Column(db.Text, nullable=True)  # 产品描述
    batch_id = db.Column(db.Integer, db.ForeignKey('product_batches.id'), nullable=True)  # 批次ID
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系
    shelf_operator = db.relationship('User', foreign_keys=[shelf_operator_id])

    def to_dict(self):
        return {
            'id': self.id,
            'supplier_id': self.supplier_id,
            'supplier_name': self.supplier.name,
            'ingredient_id': self.ingredient_id,
            'ingredient_name': self.ingredient.name,
            'product_code': self.product_code,
            'product_name': self.product_name,
            'model_number': self.model_number,
            'specification': self.specification,
            'price': float(self.price),
            'quality_cert': self.quality_cert,
            'quality_standard': self.quality_standard,
            'product_image': self.product_image,
            'lead_time': self.lead_time,
            'min_order_quantity': self.min_order_quantity,
            'is_available': self.is_available,
            'shelf_status': self.shelf_status,
            'shelf_time': format_datetime(self.shelf_time, '%Y-%m-%d %H:%M:%S') if self.shelf_time else None,
            'description': self.description,
            'batch_id': self.batch_id,
            'batch_name': self.batch.name if self.batch else None,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S'),
            'updated_at': format_datetime(self.updated_at, '%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

class PurchaseOrder(db.Model):
    """采购订单表"""
    __tablename__ = 'purchase_orders'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    order_number = db.Column(db.String(50), nullable=False, unique=True)
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'), nullable=True)  # 允许为NULL，表示自购
    requisition_id = db.Column(db.Integer, db.ForeignKey('purchase_requisitions.id'), nullable=True)
    area_id = db.Column(db.Integer, db.ForeignKey('administrative_areas.id'), nullable=False)
    total_amount = db.Column(db.Numeric(12, 2), nullable=False)
    order_date = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    expected_delivery_date = db.Column(db.Date, nullable=True)
    payment_terms = db.Column(db.String(200), nullable=True)
    delivery_terms = db.Column(db.String(200), nullable=True)
    status = db.Column(db.String(20), default='待确认', nullable=False)  # 待确认/已确认/已送达/已取消
    delivery_date = db.Column(DATETIME2(precision=1), nullable=True)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    approved_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    notes = db.Column(db.Text, nullable=True)  # 备注
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系
    area = db.relationship('AdministrativeArea', backref='purchase_orders')
    requisition = db.relationship('PurchaseRequisition', backref='purchase_orders')
    order_items = db.relationship('PurchaseOrderItem', backref='order', lazy='dynamic')
    creator = db.relationship('User', foreign_keys=[created_by], backref='created_purchase_orders')
    approver = db.relationship('User', foreign_keys=[approved_by], backref='approved_purchase_orders')
    deliveries = db.relationship('SupplierDelivery', backref='order', lazy='dynamic')

    @property
    def has_stock_in(self):
        """检查是否已有入库单"""
        return self.stock_ins.filter(StockIn.status != '已取消').count() > 0

    @property
    def active_stock_in(self):
        """获取有效的入库单（非已取消状态）"""
        return self.stock_ins.filter(StockIn.status != '已取消').first()

    def can_create_stock_in(self):
        """检查是否可以创建入库单"""
        # 一个采购订单只能有一个有效的入库单
        return not self.has_stock_in

    def get_status_display(self):
        """获取状态的显示文本"""
        # 如果状态已经是中文，直接返回
        if self.status in ['待确认', '已确认', '已送达', '已取消']:
            return self.status

        # 否则，尝试将英文状态转换为中文
        status_map = {
            'pending': '待确认',
            'confirmed': '已确认',
            'delivered': '已送达',
            'cancelled': '已取消'
        }
        return status_map.get(self.status, self.status)

    def to_dict(self):
        return {
            'id': self.id,
            'order_number': self.order_number,
            'supplier_id': self.supplier_id,
            'supplier_name': self.supplier.name if self.supplier else '自购',
            'requisition_id': self.requisition_id,
            'area_id': self.area_id,
            'area_name': self.area.name if self.area else None,
            'total_amount': float(self.total_amount),
            'order_date': format_datetime(self.order_date, '%Y-%m-%d %H:%M:%S'),
            'expected_delivery_date': format_datetime(self.expected_delivery_date, '%Y-%m-%d') if self.expected_delivery_date else None,
            'payment_terms': self.payment_terms,
            'delivery_terms': self.delivery_terms,
            'status': self.status,
            'delivery_date': format_datetime(self.delivery_date, '%Y-%m-%d %H:%M:%S') if self.delivery_date else None,
            'created_by': self.created_by,
            'creator_name': self.creator.real_name or self.creator.username,
            'approved_by': self.approved_by,
            'approver_name': self.approver.real_name or self.approver.username if self.approver else None,
            'notes': self.notes,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S'),
            'updated_at': format_datetime(self.updated_at, '%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            'items': [item.to_dict() for item in self.order_items]
        }

class PurchaseOrderItem(db.Model):
    """采购订单项表"""
    __tablename__ = 'purchase_order_items'

    id = db.Column(db.Integer, primary_key=True)
    order_id = db.Column(db.Integer, db.ForeignKey('purchase_orders.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('supplier_products.id'), nullable=True)  # 允许为NULL，表示自购
    ingredient_id = db.Column(db.Integer, db.ForeignKey('ingredients.id'), nullable=False)
    quantity = db.Column(db.Float, nullable=False)
    unit = db.Column(db.String(20), nullable=False)
    unit_price = db.Column(db.Numeric(10, 2), nullable=False)
    total_price = db.Column(db.Numeric(10, 2), nullable=False)
    received_quantity = db.Column(db.Float, nullable=True, default=0)
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系
    product = db.relationship('SupplierProduct')
    ingredient = db.relationship('Ingredient')

    def to_dict(self):
        return {
            'id': self.id,
            'order_id': self.order_id,
            'product_id': self.product_id,
            'product_name': self.product.ingredient.name if self.product else self.ingredient.name,
            'ingredient_id': self.ingredient_id,
            'ingredient_name': self.ingredient.name,
            'quantity': self.quantity,
            'unit': self.unit,
            'unit_price': float(self.unit_price),
            'total_price': float(self.total_price),
            'received_quantity': self.received_quantity,
            'notes': self.notes,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S'),
            'updated_at': format_datetime(self.updated_at, '%Y-%m-%d %H:%M:%S')
        }

class RecipeCategory(db.Model):
    """食谱分类表"""
    __tablename__ = 'recipe_categories'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False)
    description = db.Column(db.String(200), nullable=True)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S')
        }

class Recipe(db.Model):
    """食谱表 - 简化版，作为系统参考库"""
    __tablename__ = 'recipes'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    category = db.Column(db.String(50), nullable=False)  # 主食/荤菜/素菜/汤品等
    category_id = db.Column(db.Integer, db.ForeignKey('recipe_categories.id'), nullable=True)
    meal_type = db.Column(db.String(20), nullable=True)  # 早餐/午餐/晚餐
    main_image = db.Column(db.String(200), nullable=True)
    description = db.Column(db.Text, nullable=True)
    calories = db.Column(db.Integer, nullable=True)
    nutrition_info = db.Column(db.Text, nullable=True)  # JSON格式
    cooking_method = db.Column(db.String(100), nullable=True)
    cooking_steps = db.Column(db.Text, nullable=True)
    cooking_time = db.Column(db.Integer, nullable=True)
    serving_size = db.Column(db.Integer, nullable=True)
    status = db.Column(db.Integer, default=1, nullable=False)  # 0-停用, 1-启用
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)
    parent_id = db.Column(db.Integer, nullable=True)
    is_template = db.Column(db.Boolean, default=False, nullable=False)
    template_type = db.Column(db.String(20), nullable=True)
    variation_reason = db.Column(db.String(200), nullable=True)
    version = db.Column(db.Integer, default=1, nullable=False)
    # 新增字段 - 与数据库表结构保持一致
    is_user_defined = db.Column(db.Boolean, default=False, nullable=False)  # 是否为用户自定义食谱
    priority = db.Column(db.Integer, default=0, nullable=False)  # 优先级
    area_id = db.Column(db.Integer, db.ForeignKey('administrative_areas.id'), nullable=True)  # 学校级隔离字段
    is_global = db.Column(db.Boolean, default=False, nullable=False)  # 是否为全局食谱（系统预设）
    is_deleted = db.Column(db.Boolean, default=False, nullable=False)  # 软删除标记
    deleted_at = db.Column(DATETIME2(precision=1), nullable=True)  # 删除时间
    deleted_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)  # 删除人

    # 关系
    ingredients = db.relationship('RecipeIngredient', backref='recipe', lazy='dynamic')
    creator = db.relationship('User', foreign_keys=[created_by], backref='created_recipes')
    food_samples = db.relationship('FoodSample', backref='recipe', lazy='dynamic')
    category_rel = db.relationship('RecipeCategory', backref='recipes')
    area = db.relationship('AdministrativeArea', foreign_keys=[area_id], backref='recipes')
    deleter = db.relationship('User', foreign_keys=[deleted_by], backref='deleted_recipes')

    @property
    def is_soft_deleted(self):
        """检查是否被软删除"""
        return self.is_deleted == True

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'category': self.category,
            'category_id': self.category_id,
            'category_name': self.category_rel.name if self.category_rel else None,
            'meal_type': self.meal_type,
            'main_image': self.main_image,
            'description': self.description,
            'calories': self.calories,
            'nutrition_info': self.nutrition_info,
            'cooking_method': self.cooking_method,
            'cooking_steps': self.cooking_steps,
            'cooking_time': self.cooking_time,
            'serving_size': self.serving_size,
            'status': self.status,
            'created_by': self.created_by,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S'),
            'updated_at': format_datetime(self.updated_at, '%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            'parent_id': self.parent_id,
            'is_template': self.is_template,
            'template_type': self.template_type,
            'variation_reason': self.variation_reason,
            'version': self.version,
            'is_user_defined': self.is_user_defined,
            'priority': self.priority,
            'area_id': self.area_id,
            'area_name': self.area.name if self.area else None,
            'is_global': self.is_global,
            'is_deleted': self.is_deleted,
            'deleted_at': format_datetime(self.deleted_at, '%Y-%m-%d %H:%M:%S') if self.deleted_at else None,
            'deleted_by': self.deleted_by,
            'deleter_name': self.deleter.real_name or self.deleter.username if self.deleter else None,
            'suggested_ingredients': [ri.to_dict() for ri in self.ingredients]
        }

class RecipeIngredient(db.Model):
    """食谱参考食材清单 - 仅作为参考，不与实际采购量关联"""
    __tablename__ = 'recipe_ingredients'

    recipe_id = db.Column(db.Integer, db.ForeignKey('recipes.id'), primary_key=True)
    ingredient_id = db.Column(db.Integer, db.ForeignKey('ingredients.id'), primary_key=True)
    quantity = db.Column(db.Float, nullable=False)  # 参考用量
    unit = db.Column(db.String(20), nullable=False)  # 单位

    # 关系 - 已通过Ingredient模型中的recipe_ingredients关系定义

    def to_dict(self):
        return {
            'recipe_id': self.recipe_id,
            'ingredient_id': self.ingredient_id,
            'ingredient_name': self.ingredient.name if hasattr(self, 'ingredient') and self.ingredient else '未知食材',
            'quantity': self.quantity,
            'unit': self.unit,
            'is_reference': True  # 标记为参考食材
        }



class ConsumptionPlan(db.Model):
    """消耗计划表"""
    __tablename__ = 'consumption_plans'

    id = db.Column(db.Integer, primary_key=True)
    area_id = db.Column(db.Integer, db.ForeignKey('administrative_areas.id'), nullable=False)  # 学校区域ID，必填
    consumption_date = db.Column(db.Date, nullable=False)  # 消耗日期，必填
    meal_type = db.Column(db.String(20), nullable=False)  # 餐次：早餐/午餐/晚餐/加餐，必填
    diners_count = db.Column(db.Integer, nullable=True)  # 用餐人数
    status = db.Column(db.String(20), nullable=False, default='计划中')  # 计划中/已审核/已执行/已取消
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    approved_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系
    area = db.relationship('AdministrativeArea', backref='consumption_plans')
    creator = db.relationship('User', foreign_keys=[created_by], backref='created_consumption_plans')
    approver = db.relationship('User', foreign_keys=[approved_by], backref='approved_consumption_plans')
    consumption_details = db.relationship('ConsumptionDetail', backref='consumption_plan', lazy='dynamic')
    stock_outs = db.relationship('StockOut', backref='consumption_plan', lazy='dynamic')

    def to_dict(self):
        return {
            'id': self.id,
            'area_id': self.area_id,
            'area_name': self.area.name if self.area else None,
            'consumption_date': format_datetime(self.consumption_date, '%Y-%m-%d'),
            'meal_type': self.meal_type,
            'diners_count': self.diners_count,
            'status': self.status,
            'created_by': self.created_by,
            'creator_name': self.creator.real_name or self.creator.username,
            'approved_by': self.approved_by,
            'approver_name': self.approver.real_name or self.approver.username if self.approver else None,
            'notes': self.notes,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S'),
            'updated_at': format_datetime(self.updated_at, '%Y-%m-%d %H:%M:%S'),
            'details': [detail.to_dict() for detail in self.consumption_details]
        }

class ConsumptionDetail(db.Model):
    """消耗明细表"""
    __tablename__ = 'consumption_details'

    id = db.Column(db.Integer, primary_key=True)
    consumption_plan_id = db.Column(db.Integer, db.ForeignKey('consumption_plans.id'), nullable=False)
    ingredient_id = db.Column(db.Integer, db.ForeignKey('ingredients.id'), nullable=False)
    planned_quantity = db.Column(db.Float, nullable=False)
    actual_quantity = db.Column(db.Float, nullable=True)
    unit = db.Column(db.String(20), nullable=False)
    status = db.Column(db.String(20), nullable=False, default='待出库')  # 待出库/已出库
    is_main_ingredient = db.Column(db.Boolean, default=True, nullable=False)  # 是否为主要食材
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系
    ingredient = db.relationship('Ingredient')

    def to_dict(self):
        return {
            'id': self.id,
            'consumption_plan_id': self.consumption_plan_id,
            'ingredient_id': self.ingredient_id,
            'ingredient_name': self.ingredient.name,
            'planned_quantity': self.planned_quantity,
            'actual_quantity': self.actual_quantity,
            'unit': self.unit,
            'status': self.status,
            'is_main_ingredient': self.is_main_ingredient,
            'notes': self.notes,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S'),
            'updated_at': format_datetime(self.updated_at, '%Y-%m-%d %H:%M:%S')
        }

class FoodSample(db.Model):
    """留样记录表"""
    __tablename__ = 'food_samples'

    id = db.Column(db.Integer, primary_key=True)
    sample_number = db.Column(db.String(50), nullable=True)
    recipe_id = db.Column(db.Integer, db.ForeignKey('recipes.id'), nullable=False)
    area_id = db.Column(db.Integer, db.ForeignKey('administrative_areas.id'), nullable=False)  # 学校区域ID，必填
    meal_date = db.Column(db.Date, nullable=False)  # 用餐日期，必填
    meal_type = db.Column(db.String(20), nullable=False)  # 早餐/午餐/晚餐，必填
    sample_image = db.Column(db.String(200), nullable=False)
    sample_quantity = db.Column(db.Float, nullable=True)
    sample_unit = db.Column(db.String(20), nullable=True)
    storage_location = db.Column(db.String(100), nullable=False)
    storage_temperature = db.Column(db.String(50), nullable=True)
    start_time = db.Column(DATETIME2(precision=1), nullable=False)
    end_time = db.Column(DATETIME2(precision=1), nullable=False)
    operator_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    status = db.Column(db.String(20), nullable=False, default='已留样')  # 已留样/已销毁
    destruction_time = db.Column(DATETIME2(precision=1), nullable=True)
    destruction_operator_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系
    area = db.relationship('AdministrativeArea', backref='food_samples')
    operator = db.relationship('User', foreign_keys=[operator_id], backref='operated_food_samples')
    destruction_operator = db.relationship('User', foreign_keys=[destruction_operator_id], backref='destroyed_food_samples')

    def to_dict(self):
        return {
            'id': self.id,
            'sample_number': self.sample_number,
            'recipe_id': self.recipe_id,
            'recipe_name': self.recipe.name,
            'area_id': self.area_id,
            'area_name': self.area.name if self.area else None,
            'meal_date': format_datetime(self.meal_date, '%Y-%m-%d'),
            'meal_type': self.meal_type,
            'sample_image': self.sample_image,
            'sample_quantity': self.sample_quantity,
            'sample_unit': self.sample_unit,
            'storage_location': self.storage_location,
            'storage_temperature': self.storage_temperature,
            'start_time': format_datetime(self.start_time, '%Y-%m-%d %H:%M:%S'),
            'end_time': format_datetime(self.end_time, '%Y-%m-%d %H:%M:%S'),
            'operator_id': self.operator_id,
            'operator_name': self.operator.real_name or self.operator.username,
            'status': self.status,
            'destruction_time': format_datetime(self.destruction_time, '%Y-%m-%d %H:%M:%S') if self.destruction_time else None,
            'destruction_operator_id': self.destruction_operator_id,
            'destruction_operator_name': (self.destruction_operator.real_name or self.destruction_operator.username) if self.destruction_operator else None,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S'),
            'updated_at': format_datetime(self.updated_at, '%Y-%m-%d %H:%M:%S')
        }

class SupplierCertificate(db.Model):
    """供应商证书表"""
    __tablename__ = 'supplier_certificates'

    id = db.Column(db.Integer, primary_key=True)
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'), nullable=False)
    certificate_type = db.Column(db.String(50), nullable=False)  # 营业执照/食品经营许可证/检验检疫证明等
    certificate_number = db.Column(db.String(100), nullable=False)
    issue_date = db.Column(db.Date, nullable=False)
    expiry_date = db.Column(db.Date, nullable=False)
    issuing_authority = db.Column(db.String(100), nullable=False)
    certificate_image = db.Column(db.String(200), nullable=True)
    status = db.Column(db.String(20), nullable=False, default='有效')  # 有效/即将过期/已过期
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    def to_dict(self):
        return {
            'id': self.id,
            'supplier_id': self.supplier_id,
            'supplier_name': self.supplier.name,
            'certificate_type': self.certificate_type,
            'certificate_number': self.certificate_number,
            'issue_date': format_datetime(self.issue_date, '%Y-%m-%d'),
            'expiry_date': format_datetime(self.expiry_date, '%Y-%m-%d'),
            'issuing_authority': self.issuing_authority,
            'certificate_image': self.certificate_image,
            'status': self.status,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S'),
            'updated_at': format_datetime(self.updated_at, '%Y-%m-%d %H:%M:%S')
        }

class SupplierSchoolRelation(db.Model):
    """供应商-学校关联表"""
    __tablename__ = 'supplier_school_relations'

    id = db.Column(db.Integer, primary_key=True)
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'), nullable=False)
    area_id = db.Column(db.Integer, db.ForeignKey('administrative_areas.id'), nullable=False)  # 学校级别的区域
    contract_number = db.Column(db.String(100), nullable=True)  # 合同编号
    start_date = db.Column(db.Date, nullable=False)  # 合作开始日期
    end_date = db.Column(db.Date, nullable=True)  # 合作结束日期（可为空表示长期合作）
    status = db.Column(db.Integer, default=1, nullable=False)  # 0-已终止, 1-有效
    notes = db.Column(db.Text, nullable=True)  # 备注
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系
    supplier = db.relationship('Supplier', backref='school_relations')
    area = db.relationship('AdministrativeArea', backref='supplier_relations')

    def to_dict(self):
        return {
            'id': self.id,
            'supplier_id': self.supplier_id,
            'supplier_name': self.supplier.name,
            'area_id': self.area_id,
            'area_name': self.area.name,
            'contract_number': self.contract_number,
            'start_date': format_datetime(self.start_date, '%Y-%m-%d'),
            'end_date': format_datetime(self.end_date, '%Y-%m-%d') if self.end_date else None,
            'status': self.status,
            'notes': self.notes,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S'),
            'updated_at': format_datetime(self.updated_at, '%Y-%m-%d %H:%M:%S')
        }

class ProductSpecParameter(db.Model):
    """产品规格参数表"""
    __tablename__ = 'product_spec_parameters'

    id = db.Column(db.Integer, primary_key=True)
    product_id = db.Column(db.Integer, db.ForeignKey('supplier_products.id'), nullable=False)
    param_name = db.Column(db.String(50), nullable=False)  # 参数名称
    param_value = db.Column(db.String(100), nullable=False)  # 参数值
    param_unit = db.Column(db.String(20), nullable=True)  # 参数单位
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系
    product = db.relationship('SupplierProduct', backref='spec_parameters')

    def to_dict(self):
        return {
            'id': self.id,
            'product_id': self.product_id,
            'param_name': self.param_name,
            'param_value': self.param_value,
            'param_unit': self.param_unit,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S')
        }

class DeliveryInspection(db.Model):
    """送货验收表"""
    __tablename__ = 'delivery_inspections'

    id = db.Column(db.Integer, primary_key=True)
    delivery_id = db.Column(db.Integer, db.ForeignKey('supplier_deliveries.id'), nullable=False)
    inspector_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    inspection_date = db.Column(DATETIME2(precision=1), nullable=False)
    inspection_result = db.Column(db.String(20), nullable=False)  # 全部通过/部分通过/全部拒收
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系
    delivery = db.relationship('SupplierDelivery', backref='inspections')
    inspector = db.relationship('User')

    def to_dict(self):
        return {
            'id': self.id,
            'delivery_id': self.delivery_id,
            'inspector_id': self.inspector_id,
            'inspector_name': self.inspector.real_name or self.inspector.username,
            'inspection_date': format_datetime(self.inspection_date, '%Y-%m-%d %H:%M:%S'),
            'inspection_result': self.inspection_result,
            'notes': self.notes,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S')
        }

class DeliveryItemInspection(db.Model):
    """送货项验收表"""
    __tablename__ = 'delivery_item_inspections'

    id = db.Column(db.Integer, primary_key=True)
    inspection_id = db.Column(db.Integer, db.ForeignKey('delivery_inspections.id'), nullable=False)
    delivery_item_id = db.Column(db.Integer, db.ForeignKey('delivery_items.id'), nullable=False)
    accepted_quantity = db.Column(db.Float, nullable=False)  # 验收合格数量
    rejected_quantity = db.Column(db.Float, nullable=False)  # 拒收数量
    rejection_reason = db.Column(db.String(200), nullable=True)  # 拒收原因
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系
    inspection = db.relationship('DeliveryInspection', backref='item_inspections')
    delivery_item = db.relationship('DeliveryItem', backref='inspections')

    def to_dict(self):
        return {
            'id': self.id,
            'inspection_id': self.inspection_id,
            'delivery_item_id': self.delivery_item_id,
            'accepted_quantity': self.accepted_quantity,
            'rejected_quantity': self.rejected_quantity,
            'rejection_reason': self.rejection_reason,
            'notes': self.notes,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S')
        }

class PurchaseRequisition(db.Model):
    """采购申请表"""
    __tablename__ = 'purchase_requisitions'

    id = db.Column(db.Integer, primary_key=True)
    requisition_number = db.Column(db.String(50), nullable=False, unique=True)
    area_id = db.Column(db.Integer, db.ForeignKey('administrative_areas.id'), nullable=False)
    requisition_date = db.Column(db.Date, nullable=False)
    required_date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(20), nullable=False, default='待审核')  # 待审核/已审核/已转采购/已取消
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    approved_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系
    area = db.relationship('AdministrativeArea', backref='purchase_requisitions')
    creator = db.relationship('User', foreign_keys=[created_by], backref='created_purchase_requisitions')
    approver = db.relationship('User', foreign_keys=[approved_by], backref='approved_purchase_requisitions')
    requisition_items = db.relationship('PurchaseRequisitionItem', backref='requisition', lazy='dynamic')

    def to_dict(self):
        return {
            'id': self.id,
            'requisition_number': self.requisition_number,
            'area_id': self.area_id,
            'area_name': self.area.name if self.area else None,
            'requisition_date': format_datetime(self.requisition_date, '%Y-%m-%d'),
            'required_date': format_datetime(self.required_date, '%Y-%m-%d'),
            'status': self.status,
            'created_by': self.created_by,
            'creator_name': self.creator.real_name or self.creator.username,
            'approved_by': self.approved_by,
            'approver_name': self.approver.real_name or self.approver.username if self.approver else None,
            'notes': self.notes,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S'),
            'updated_at': format_datetime(self.updated_at, '%Y-%m-%d %H:%M:%S'),
            'items': [item.to_dict() for item in self.requisition_items]
        }

class PurchaseRequisitionItem(db.Model):
    """采购申请明细表"""
    __tablename__ = 'purchase_requisition_items'

    id = db.Column(db.Integer, primary_key=True)
    requisition_id = db.Column(db.Integer, db.ForeignKey('purchase_requisitions.id'), nullable=False)
    ingredient_id = db.Column(db.Integer, db.ForeignKey('ingredients.id'), nullable=False)
    quantity = db.Column(db.Float, nullable=False)
    unit = db.Column(db.String(20), nullable=False)
    estimated_price = db.Column(db.Numeric(10, 2), nullable=True)
    total_estimated_price = db.Column(db.Numeric(10, 2), nullable=True)
    purpose = db.Column(db.String(200), nullable=True)
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系
    ingredient = db.relationship('Ingredient')

    def to_dict(self):
        return {
            'id': self.id,
            'requisition_id': self.requisition_id,
            'ingredient_id': self.ingredient_id,
            'ingredient_name': self.ingredient.name,
            'quantity': self.quantity,
            'unit': self.unit,
            'estimated_price': float(self.estimated_price) if self.estimated_price else None,
            'total_estimated_price': float(self.total_estimated_price) if self.total_estimated_price else None,
            'purpose': self.purpose,
            'notes': self.notes,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S'),
            'updated_at': format_datetime(self.updated_at, '%Y-%m-%d %H:%M:%S')
        }

class SupplierDelivery(db.Model):
    """供货商送货记录表"""
    __tablename__ = 'supplier_deliveries'

    id = db.Column(db.Integer, primary_key=True)
    delivery_number = db.Column(db.String(50), nullable=False, unique=True)
    order_id = db.Column(db.Integer, db.ForeignKey('purchase_orders.id'), nullable=False)
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'), nullable=False)
    delivery_date = db.Column(db.Date, nullable=False)
    carrier_name = db.Column(db.String(50), nullable=True)
    carrier_phone = db.Column(db.String(20), nullable=True)
    vehicle_number = db.Column(db.String(20), nullable=True)
    status = db.Column(db.String(20), nullable=False, default='运输中')  # 运输中/已送达/已验收/已拒收
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系
    delivery_items = db.relationship('DeliveryItem', backref='delivery', lazy='dynamic')

    def to_dict(self):
        return {
            'id': self.id,
            'delivery_number': self.delivery_number,
            'order_id': self.order_id,
            'order_number': self.order.order_number,
            'supplier_id': self.supplier_id,
            'supplier_name': self.supplier.name,
            'delivery_date': format_datetime(self.delivery_date, '%Y-%m-%d'),
            'carrier_name': self.carrier_name,
            'carrier_phone': self.carrier_phone,
            'vehicle_number': self.vehicle_number,
            'status': self.status,
            'notes': self.notes,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S'),
            'updated_at': format_datetime(self.updated_at, '%Y-%m-%d %H:%M:%S'),
            'items': [item.to_dict() for item in self.delivery_items]
        }

class VideoGuide(db.Model):
    """视频引导表"""
    __tablename__ = 'video_guides'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    step_name = db.Column(db.String(64), nullable=False)  # 引导步骤名称
    name = db.Column(db.String(128), nullable=False)  # 视频名称
    description = db.Column(db.String(500), nullable=True)  # 视频描述
    file_path = db.Column(db.String(256), nullable=False)  # 文件路径
    thumbnail = db.Column(db.String(256), nullable=True)  # 缩略图路径
    duration = db.Column(db.String(32), nullable=True)  # 视频时长
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    def to_dict(self):
        return {
            'id': self.id,
            'step_name': self.step_name,
            'name': self.name,
            'description': self.description,
            'file_path': self.file_path,
            'thumbnail': self.thumbnail,
            'duration': self.duration,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S'),
            'updated_at': format_datetime(self.updated_at, '%Y-%m-%d %H:%M:%S')
        }

class DeliveryItem(db.Model):
    """送货明细表"""
    __tablename__ = 'delivery_items'

    id = db.Column(db.Integer, primary_key=True)
    delivery_id = db.Column(db.Integer, db.ForeignKey('supplier_deliveries.id'), nullable=False)
    order_item_id = db.Column(db.Integer, db.ForeignKey('purchase_order_items.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('supplier_products.id'), nullable=False)
    quantity = db.Column(db.Float, nullable=False)
    unit = db.Column(db.String(20), nullable=False)
    batch_number = db.Column(db.String(50), nullable=False)
    production_date = db.Column(db.Date, nullable=False)
    expiry_date = db.Column(db.Date, nullable=False)
    certificate_numbers = db.Column(db.String(200), nullable=True)  # 多个，逗号分隔
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系
    order_item = db.relationship('PurchaseOrderItem')
    product = db.relationship('SupplierProduct')

    def to_dict(self):
        return {
            'id': self.id,
            'delivery_id': self.delivery_id,
            'order_item_id': self.order_item_id,
            'product_id': self.product_id,
            'product_name': self.product.ingredient.name,
            'quantity': self.quantity,
            'unit': self.unit,
            'batch_number': self.batch_number,
            'production_date': format_datetime(self.production_date, '%Y-%m-%d'),
            'expiry_date': format_datetime(self.expiry_date, '%Y-%m-%d'),
            'certificate_numbers': self.certificate_numbers,
            'notes': self.notes,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S'),
            'updated_at': format_datetime(self.updated_at, '%Y-%m-%d %H:%M:%S')
        }

class Warehouse(db.Model):
    """仓库表"""
    __tablename__ = 'warehouses'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    name = db.Column(db.String(100), nullable=False)
    area_id = db.Column(db.Integer, db.ForeignKey('administrative_areas.id'), nullable=False)
    location = db.Column(db.String(200), nullable=False)
    manager_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    capacity = db.Column(db.Float, nullable=True)  # 容量
    capacity_unit = db.Column(db.String(20), nullable=True)  # 容量单位
    temperature_range = db.Column(db.String(50), nullable=True)  # 温度范围
    humidity_range = db.Column(db.String(50), nullable=True)  # 湿度范围
    status = db.Column(db.String(20), nullable=False, default='正常')  # 正常/维护中/已关闭
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系
    area = db.relationship('AdministrativeArea', backref='warehouses')
    manager = db.relationship('User', backref='managed_warehouses')
    storage_locations = db.relationship('StorageLocation', backref='warehouse', lazy='dynamic')
    # 移除与Inventory的backref，避免属性冲突
    stock_ins = db.relationship('StockIn', backref='warehouse', lazy='dynamic')
    stock_outs = db.relationship('StockOut', backref='warehouse', lazy='dynamic')
    inventory_checks = db.relationship('InventoryCheck', backref='warehouse', lazy='dynamic')

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'area_id': self.area_id,
            'area_name': self.area.name if self.area else None,
            'location': self.location,
            'manager_id': self.manager_id,
            'manager_name': self.manager.real_name or self.manager.username,
            'capacity': self.capacity,
            'capacity_unit': self.capacity_unit,
            'temperature_range': self.temperature_range,
            'humidity_range': self.humidity_range,
            'status': self.status,
            'notes': self.notes,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S'),
            'updated_at': format_datetime(self.updated_at, '%Y-%m-%d %H:%M:%S')
        }

class StorageLocation(db.Model):
    """存储位置表"""
    __tablename__ = 'storage_locations'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    warehouse_id = db.Column(db.Integer, db.ForeignKey('warehouses.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    location_code = db.Column(db.String(50), nullable=False)  # 位置编码
    storage_type = db.Column(db.String(50), nullable=False)  # 冷藏/冷冻/常温
    capacity = db.Column(db.Float, nullable=True)  # 容量
    capacity_unit = db.Column(db.String(20), nullable=True)  # 容量单位
    temperature_range = db.Column(db.String(50), nullable=True)  # 温度范围
    status = db.Column(db.String(20), nullable=False, default='正常')  # 正常/维护中/已关闭
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系 - 移除与Inventory的backref，避免属性冲突

    def to_dict(self):
        return {
            'id': self.id,
            'warehouse_id': self.warehouse_id,
            'warehouse_name': self.warehouse.name,
            'name': self.name,
            'location_code': self.location_code,
            'storage_type': self.storage_type,
            'capacity': self.capacity,
            'capacity_unit': self.capacity_unit,
            'temperature_range': self.temperature_range,
            'status': self.status,
            'notes': self.notes,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S'),
            'updated_at': format_datetime(self.updated_at, '%Y-%m-%d %H:%M:%S')
        }

class Inventory(db.Model):
    """库存表"""
    __tablename__ = 'inventories'

    id = db.Column(db.Integer, primary_key=True)
    warehouse_id = db.Column(db.Integer, db.ForeignKey('warehouses.id'), nullable=False)
    storage_location_id = db.Column(db.Integer, db.ForeignKey('storage_locations.id'), nullable=False)
    ingredient_id = db.Column(db.Integer, db.ForeignKey('ingredients.id'), nullable=False)
    # area_id = db.Column(db.Integer, db.ForeignKey('administrative_areas.id'), nullable=False)  # 学校区域ID - 暂时注释，数据库中不存在
    batch_number = db.Column(db.String(50), nullable=False)  # 批次号
    quantity = db.Column(db.Float, nullable=False)
    unit = db.Column(db.String(20), nullable=False)
    production_date = db.Column(db.Date, nullable=False)
    expiry_date = db.Column(db.Date, nullable=False)
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'), nullable=True)
    status = db.Column(db.String(20), nullable=False, default='正常')  # 正常/待检/冻结/已过期
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系 - 使用不同的属性名避免与backref冲突
    ingredient = db.relationship('Ingredient')
    supplier = db.relationship('Supplier')
    warehouse_info = db.relationship('Warehouse', foreign_keys=[warehouse_id])
    storage_location_info = db.relationship('StorageLocation', foreign_keys=[storage_location_id])
    # area = db.relationship('AdministrativeArea', backref='inventories')  # 暂时注释，数据库中不存在area_id字段

    def to_dict(self):
        return {
            'id': self.id,
            'warehouse_id': self.warehouse_id,
            'warehouse_name': self.warehouse_info.name if self.warehouse_info else None,
            'storage_location_id': self.storage_location_id,
            'storage_location_name': self.storage_location_info.name if self.storage_location_info else None,
            'ingredient_id': self.ingredient_id,
            'ingredient_name': self.ingredient.name if self.ingredient else None,
            'batch_number': self.batch_number,
            'quantity': self.quantity,
            'unit': self.unit,
            'production_date': format_datetime(self.production_date, '%Y-%m-%d'),
            'expiry_date': format_datetime(self.expiry_date, '%Y-%m-%d'),
            'supplier_id': self.supplier_id,
            'supplier_name': self.supplier.name if self.supplier else None,
            'status': self.status,
            'notes': self.notes,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S'),
            'updated_at': format_datetime(self.updated_at, '%Y-%m-%d %H:%M:%S')
        }

class StockIn(db.Model):
    """入库记录表"""
    __tablename__ = 'stock_ins'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    stock_in_number = db.Column(db.String(50), nullable=False, unique=True)
    warehouse_id = db.Column(db.Integer, db.ForeignKey('warehouses.id'), nullable=False)
    delivery_id = db.Column(db.Integer, db.ForeignKey('supplier_deliveries.id'), nullable=True)
    purchase_order_id = db.Column(db.Integer, db.ForeignKey('purchase_orders.id'), nullable=True)  # 关联采购订单
    stock_in_date = db.Column(db.Date, nullable=False)
    stock_in_type = db.Column(db.String(20), nullable=False)  # 采购入库/调拨入库/退货入库
    operator_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    inspector_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    status = db.Column(db.String(20), nullable=False, default='待审核')  # 待审核/已审核/已入库/已取消
    notes = db.Column(db.Text, nullable=True)
    # 财务相关字段
    is_financial_confirmed = db.Column(db.Boolean, default=False, nullable=False)  # 是否财务确认
    total_cost = db.Column(db.Numeric(15, 2), nullable=True)  # 总成本
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'), nullable=True)  # 供应商ID
    payable_id = db.Column(db.Integer, nullable=True)  # 应付账款ID
    voucher_id = db.Column(db.Integer, nullable=True)  # 财务凭证ID
    area_id = db.Column(db.Integer, db.ForeignKey('administrative_areas.id'), nullable=True)  # 学校级隔离字段
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系
    operator = db.relationship('User', foreign_keys=[operator_id], backref='operated_stock_ins')
    inspector = db.relationship('User', foreign_keys=[inspector_id], backref='inspected_stock_ins')
    delivery = db.relationship('SupplierDelivery')
    purchase_order = db.relationship('PurchaseOrder', backref=db.backref('stock_ins', lazy='dynamic'))  # 关联采购订单
    stock_in_items = db.relationship('StockInItem', backref='stock_in', lazy='dynamic')
    supplier = db.relationship('Supplier', foreign_keys=[supplier_id])  # 供应商关系
    area = db.relationship('AdministrativeArea', foreign_keys=[area_id])  # 区域关系
    # warehouse 关系通过 Warehouse.stock_ins 的 backref 自动创建，不需要重复定义

    def to_dict(self):
        return {
            'id': self.id,
            'stock_in_number': self.stock_in_number,
            'warehouse_id': self.warehouse_id,
            'warehouse_name': self.warehouse.name,
            'delivery_id': self.delivery_id,
            'delivery_number': self.delivery.delivery_number if self.delivery else None,
            'purchase_order_id': self.purchase_order_id,
            'purchase_order_number': self.purchase_order.order_number if self.purchase_order else None,
            'stock_in_date': format_datetime(self.stock_in_date, '%Y-%m-%d'),
            'stock_in_type': self.stock_in_type,
            'operator_id': self.operator_id,
            'operator_name': self.operator.real_name or self.operator.username,
            'inspector_id': self.inspector_id,
            'inspector_name': self.inspector.real_name or self.inspector.username if self.inspector else None,
            'status': self.status,
            'notes': self.notes,
            # 财务相关字段
            'is_financial_confirmed': self.is_financial_confirmed,
            'total_cost': float(self.total_cost) if self.total_cost else None,
            'supplier_id': self.supplier_id,
            'supplier_name': self.supplier.name if self.supplier else None,
            'payable_id': self.payable_id,
            'voucher_id': self.voucher_id,
            'area_id': self.area_id,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S'),
            'updated_at': format_datetime(self.updated_at, '%Y-%m-%d %H:%M:%S'),
            'items': [item.to_dict() for item in self.stock_in_items]
        }

    def calculate_total_cost(self):
        """计算入库单总成本"""
        total = 0
        for item in self.stock_in_items:
            if item.unit_price and item.quantity:
                total += item.unit_price * item.quantity
        return total

    def update_total_cost(self):
        """更新总成本"""
        self.total_cost = self.calculate_total_cost()

class StockInItem(db.Model):
    """入库明细表"""
    __tablename__ = 'stock_in_items'

    id = db.Column(db.Integer, primary_key=True)
    stock_in_id = db.Column(db.Integer, db.ForeignKey('stock_ins.id'), nullable=False)
    ingredient_id = db.Column(db.Integer, db.ForeignKey('ingredients.id'), nullable=False)
    batch_number = db.Column(db.String(50), nullable=False)  # 批次号
    quantity = db.Column(db.Float, nullable=False)
    unit = db.Column(db.String(20), nullable=False)
    unit_price = db.Column(db.Float, nullable=True)  # 单价
    production_date = db.Column(db.Date, nullable=False)
    expiry_date = db.Column(db.Date, nullable=False)
    storage_location_id = db.Column(db.Integer, db.ForeignKey('storage_locations.id'), nullable=False)
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'), nullable=True)
    purchase_order_item_id = db.Column(db.Integer, db.ForeignKey('purchase_order_items.id'), nullable=True)  # 关联采购订单明细
    quality_check_result = db.Column(db.String(20), nullable=True)  # 合格/不合格
    quality_check_notes = db.Column(db.Text, nullable=True)
    quality_status = db.Column(db.String(20), default='良好', nullable=False)  # 质量状态：良好/一般/较差
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系
    ingredient = db.relationship('Ingredient')
    storage_location = db.relationship('StorageLocation')
    supplier = db.relationship('Supplier')
    purchase_order_item = db.relationship('PurchaseOrderItem', backref='stock_in_items')  # 关联采购订单明细

# 入库单据与入库明细的多对多关联表
stock_in_document_items = db.Table('stock_in_document_items',
    db.Column('document_id', db.Integer, db.ForeignKey('stock_in_documents.id'), primary_key=True),
    db.Column('item_id', db.Integer, db.ForeignKey('stock_in_items.id'), primary_key=True)
)

class StockInDocument(db.Model):
    """入库单据表"""
    __tablename__ = 'stock_in_documents'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    stock_in_id = db.Column(db.Integer, db.ForeignKey('stock_ins.id'), nullable=False)
    document_type = db.Column(db.String(50), nullable=False)  # 单据类型：送货单、检验检疫证明等
    file_path = db.Column(db.String(255), nullable=False)  # 文件路径
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'), nullable=True)  # 关联供应商
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系
    stock_in = db.relationship('StockIn', backref='documents')
    supplier = db.relationship('Supplier')

    # 关联的食材项
    items = db.relationship('StockInItem', secondary=stock_in_document_items, backref='documents')

    def to_dict(self):
        return {
            'id': self.id,
            'stock_in_id': self.stock_in_id,
            'document_type': self.document_type,
            'file_path': self.file_path,
            'supplier_id': self.supplier_id,
            'supplier_name': self.supplier.name if self.supplier else None,
            'notes': self.notes,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S'),
            'items': [item.id for item in self.items]
        }

class IngredientInspection(db.Model):
    """食材检验检疫记录表"""
    __tablename__ = 'ingredient_inspections'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    stock_in_item_id = db.Column(db.Integer, db.ForeignKey('stock_in_items.id'), nullable=False)
    inspector_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    inspection_date = db.Column(db.Date, default=lambda: datetime.now().date(), nullable=False)
    inspection_type = db.Column(db.String(50), nullable=False)  # 检验类型：感官检验、理化检验、微生物检验等
    result = db.Column(db.String(20), nullable=False)  # 合格/不合格
    notes = db.Column(db.Text, nullable=True)
    document_id = db.Column(db.Integer, db.ForeignKey('stock_in_documents.id'), nullable=True)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系
    stock_in_item = db.relationship('StockInItem', backref='inspections')
    inspector = db.relationship('User')
    document = db.relationship('StockInDocument')

    def to_dict(self):
        return {
            'id': self.id,
            'stock_in_id': self.stock_in_id,
            'ingredient_id': self.ingredient_id,
            'ingredient_name': self.ingredient.name,
            'batch_number': self.batch_number,
            'quantity': self.quantity,
            'unit': self.unit,
            'production_date': format_datetime(self.production_date, '%Y-%m-%d'),
            'expiry_date': format_datetime(self.expiry_date, '%Y-%m-%d'),
            'storage_location_id': self.storage_location_id,
            'storage_location_name': self.storage_location.name,
            'supplier_id': self.supplier_id,
            'supplier_name': self.supplier.name if self.supplier else None,
            'purchase_order_item_id': self.purchase_order_item_id,
            'purchase_order_item': self.purchase_order_item.to_dict() if self.purchase_order_item else None,
            'quality_check_result': self.quality_check_result,
            'quality_check_notes': self.quality_check_notes,
            'quality_status': self.quality_status,
            'notes': self.notes,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S'),
            'updated_at': format_datetime(self.updated_at, '%Y-%m-%d %H:%M:%S')
        }

class StockOut(db.Model):
    """出库记录表"""
    __tablename__ = 'stock_outs'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    stock_out_number = db.Column(db.String(50), nullable=False, unique=True)
    warehouse_id = db.Column(db.Integer, db.ForeignKey('warehouses.id'), nullable=False)
    consumption_plan_id = db.Column(db.Integer, db.ForeignKey('consumption_plans.id'), nullable=True)
    stock_out_date = db.Column(db.Date, nullable=False)
    stock_out_type = db.Column(db.String(20), nullable=False)  # 消耗出库/调拨出库/报废出库
    recipient = db.Column(db.String(50), nullable=True)  # 领用人
    department = db.Column(db.String(50), nullable=True)  # 领用部门
    operator_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    approver_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    status = db.Column(db.String(20), nullable=False, default='待审核')  # 待审核/已审核/已出库/已取消
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系
    operator = db.relationship('User', foreign_keys=[operator_id], backref='operated_stock_outs')
    approver = db.relationship('User', foreign_keys=[approver_id], backref='approved_stock_outs')
    stock_out_items = db.relationship('StockOutItem', backref='stock_out', lazy='dynamic')

    def to_dict(self):
        return {
            'id': self.id,
            'stock_out_number': self.stock_out_number,
            'warehouse_id': self.warehouse_id,
            'warehouse_name': self.warehouse.name,
            'consumption_plan_id': self.consumption_plan_id,
            'stock_out_date': format_datetime(self.stock_out_date, '%Y-%m-%d'),
            'stock_out_type': self.stock_out_type,
            'recipient': self.recipient,
            'department': self.department,
            'operator_id': self.operator_id,
            'operator_name': self.operator.real_name or self.operator.username,
            'approver_id': self.approver_id,
            'approver_name': self.approver.real_name or self.approver.username if self.approver else None,
            'status': self.status,
            'notes': self.notes,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S'),
            'updated_at': format_datetime(self.updated_at, '%Y-%m-%d %H:%M:%S'),
            'items': [item.to_dict() for item in self.stock_out_items]
        }

class StockOutItem(db.Model):
    """出库明细表"""
    __tablename__ = 'stock_out_items'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    stock_out_id = db.Column(db.Integer, db.ForeignKey('stock_outs.id'), nullable=False)
    inventory_id = db.Column(db.Integer, db.ForeignKey('inventories.id'), nullable=False)
    ingredient_id = db.Column(db.Integer, db.ForeignKey('ingredients.id'), nullable=False)
    batch_number = db.Column(db.String(50), nullable=False)  # 批次号
    quantity = db.Column(db.Float, nullable=False)
    unit = db.Column(db.String(20), nullable=False)
    consumption_detail_id = db.Column(db.Integer, db.ForeignKey('consumption_details.id'), nullable=True)
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系
    inventory = db.relationship('Inventory')
    ingredient = db.relationship('Ingredient')
    consumption_detail = db.relationship('ConsumptionDetail')

    def to_dict(self):
        return {
            'id': self.id,
            'stock_out_id': self.stock_out_id,
            'inventory_id': self.inventory_id,
            'ingredient_id': self.ingredient_id,
            'ingredient_name': self.ingredient.name,
            'batch_number': self.batch_number,
            'quantity': self.quantity,
            'unit': self.unit,
            'consumption_detail_id': self.consumption_detail_id,
            'notes': self.notes,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S'),
            'updated_at': format_datetime(self.updated_at, '%Y-%m-%d %H:%M:%S')
        }

class InventoryCheck(db.Model):
    """库存盘点表"""
    __tablename__ = 'inventory_checks'

    id = db.Column(db.Integer, primary_key=True)
    check_number = db.Column(db.String(50), nullable=False, unique=True)
    warehouse_id = db.Column(db.Integer, db.ForeignKey('warehouses.id'), nullable=False)
    check_date = db.Column(db.Date, nullable=False)
    check_type = db.Column(db.String(20), nullable=False)  # 全面盘点/抽样盘点
    operator_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    approver_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    status = db.Column(db.String(20), nullable=False, default='待盘点')  # 待盘点/盘点中/已完成/已取消
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系
    operator = db.relationship('User', foreign_keys=[operator_id], backref='operated_inventory_checks')
    approver = db.relationship('User', foreign_keys=[approver_id], backref='approved_inventory_checks')
    check_items = db.relationship('InventoryCheckItem', backref='inventory_check', lazy='dynamic')

    def to_dict(self):
        return {
            'id': self.id,
            'check_number': self.check_number,
            'warehouse_id': self.warehouse_id,
            'warehouse_name': self.warehouse.name,
            'check_date': format_datetime(self.check_date, '%Y-%m-%d'),
            'check_type': self.check_type,
            'operator_id': self.operator_id,
            'operator_name': self.operator.real_name or self.operator.username,
            'approver_id': self.approver_id,
            'approver_name': self.approver.real_name or self.approver.username if self.approver else None,
            'status': self.status,
            'notes': self.notes,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S'),
            'updated_at': format_datetime(self.updated_at, '%Y-%m-%d %H:%M:%S'),
            'items': [item.to_dict() for item in self.check_items]
        }

class InventoryCheckItem(db.Model):
    """库存盘点明细表"""
    __tablename__ = 'inventory_check_items'

    id = db.Column(db.Integer, primary_key=True)
    check_id = db.Column(db.Integer, db.ForeignKey('inventory_checks.id'), nullable=False)
    inventory_id = db.Column(db.Integer, db.ForeignKey('inventories.id'), nullable=False)
    system_quantity = db.Column(db.Float, nullable=False)  # 系统库存数量
    actual_quantity = db.Column(db.Float, nullable=False)  # 实际库存数量
    unit = db.Column(db.String(20), nullable=False)
    difference = db.Column(db.Float, nullable=False)  # 差异数量
    difference_reason = db.Column(db.String(200), nullable=True)  # 差异原因
    adjustment_type = db.Column(db.String(20), nullable=True)  # 调整类型：盘盈/盘亏
    is_adjusted = db.Column(db.Boolean, nullable=False, default=False)  # 是否已调整
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系
    inventory = db.relationship('Inventory')

    def to_dict(self):
        return {
            'id': self.id,
            'check_id': self.check_id,
            'inventory_id': self.inventory_id,
            'ingredient_name': self.inventory.ingredient.name,
            'batch_number': self.inventory.batch_number,
            'system_quantity': self.system_quantity,
            'actual_quantity': self.actual_quantity,
            'unit': self.unit,
            'difference': self.difference,
            'difference_reason': self.difference_reason,
            'adjustment_type': self.adjustment_type,
            'is_adjusted': self.is_adjusted,
            'notes': self.notes,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S'),
            'updated_at': format_datetime(self.updated_at, '%Y-%m-%d %H:%M:%S')
        }

class Employee(db.Model):
    """员工表"""
    __tablename__ = 'employees'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    name = db.Column(db.String(50), nullable=False)
    gender = db.Column(db.String(10), nullable=False)
    birth_date = db.Column(db.Date, nullable=True)
    phone = db.Column(db.String(20), nullable=False)
    address = db.Column(db.String(200), nullable=True)
    position = db.Column(db.String(50), nullable=False)
    department = db.Column(db.String(50), nullable=False)
    photo = db.Column(db.String(200), nullable=True)
    status = db.Column(db.Integer, default=1, nullable=False)  # 0-离职, 1-在职, 2-休假
    entry_date = db.Column(db.Date, nullable=False)
    leave_date = db.Column(db.Date, nullable=True)
    area_id = db.Column(db.Integer, db.ForeignKey('administrative_areas.id'), nullable=True)  # 所属区域
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 新增字段
    last_health_check_date = db.Column(db.Date, nullable=True)
    health_status = db.Column(db.String(20), nullable=True, default='正常')
    responsible_areas = db.Column(db.Text, nullable=True)  # JSON格式存储负责的区域
    food_safety_certifications = db.Column(db.Text, nullable=True)  # JSON格式存储食品安全相关证书
    safety_violation_count = db.Column(db.Integer, nullable=True, default=0)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)

    # 关系
    area = db.relationship('AdministrativeArea', backref=db.backref('employees', lazy='dynamic'))
    health_certificates = db.relationship('HealthCertificate', backref='employee', lazy='dynamic')
    medical_examinations = db.relationship('MedicalExamination', backref='employee', lazy='dynamic')
    daily_health_checks = db.relationship('DailyHealthCheck', backref='employee', lazy='dynamic')
    training_records = db.relationship('TrainingRecord', backref='employee', lazy='dynamic')
    user = db.relationship('User', foreign_keys=[user_id], backref=db.backref('employee_info', uselist=False))

    def to_dict(self):
        area_info = None
        if self.area:
            area_info = {
                'id': self.area.id,
                'name': self.area.name,
                'level': self.area.level,
                'level_name': self.area.get_level_name()
            }

        user_info = None
        if self.user:
            user_info = {
                'id': self.user.id,
                'username': self.user.username,
                'real_name': self.user.real_name,
                'roles': [role.name for role in self.user.roles]
            }

        return {
            'id': self.id,
            'name': self.name,
            'gender': self.gender,
            'birth_date': format_datetime(self.birth_date, '%Y-%m-%d') if self.birth_date else None,
            'phone': self.phone,
            'address': self.address,
            'position': self.position,
            'department': self.department,
            'photo': self.photo,
            'status': self.status,
            'entry_date': format_datetime(self.entry_date, '%Y-%m-%d') if self.entry_date else None,
            'leave_date': format_datetime(self.leave_date, '%Y-%m-%d') if self.leave_date else None,
            'area_id': self.area_id,
            'area': area_info,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S'),
            # 新增字段
            'last_health_check_date': format_datetime(self.last_health_check_date, '%Y-%m-%d') if self.last_health_check_date else None,
            'health_status': self.health_status,
            'responsible_areas': json.loads(self.responsible_areas) if self.responsible_areas else None,
            'food_safety_certifications': json.loads(self.food_safety_certifications) if self.food_safety_certifications else None,
            'safety_violation_count': self.safety_violation_count,
            'user_id': self.user_id,
            'user': user_info
        }

    def get_latest_health_certificate(self):
        """获取最新的健康证"""
        return self.health_certificates.order_by(HealthCertificate.issue_date.desc()).first()

    def get_latest_medical_examination(self):
        """获取最新的体检记录"""
        return self.medical_examinations.order_by(MedicalExamination.exam_date.desc()).first()

    def get_health_certificate_status(self):
        """获取健康证状态"""
        cert = self.get_latest_health_certificate()
        if not cert:
            return "未办理"
        if cert.expire_date < date.today():
            return "已过期"
        days_left = (cert.expire_date - date.today()).days
        if days_left <= 30:
            return f"即将过期({days_left}天)"
        return "有效"

class HealthCertificate(db.Model):
    """健康证表"""
    __tablename__ = 'health_certificates'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    certificate_no = db.Column(db.String(50), nullable=False)
    issue_authority = db.Column(db.String(100), nullable=False)
    issue_date = db.Column(db.Date, nullable=False)
    expire_date = db.Column(db.Date, nullable=False)
    certificate_img = db.Column(db.String(200), nullable=True)
    status = db.Column(db.Integer, default=1, nullable=False)  # 0-无效, 1-有效
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    def to_dict(self):
        return {
            'id': self.id,
            'employee_id': self.employee_id,
            'certificate_no': self.certificate_no,
            'issue_authority': self.issue_authority,
            'issue_date': format_datetime(self.issue_date, '%Y-%m-%d'),
            'expire_date': format_datetime(self.expire_date, '%Y-%m-%d'),
            'certificate_img': self.certificate_img,
            'status': self.status,
            'notes': self.notes,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S')
        }

class MedicalExamination(db.Model):
    """体检记录表"""
    __tablename__ = 'medical_examinations'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    exam_date = db.Column(db.Date, nullable=False)
    exam_hospital = db.Column(db.String(100), nullable=False)
    result = db.Column(db.String(20), nullable=False)  # 合格/不合格
    report_img = db.Column(db.String(200), nullable=True)
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)

    def to_dict(self):
        return {
            'id': self.id,
            'employee_id': self.employee_id,
            'exam_date': format_datetime(self.exam_date, '%Y-%m-%d'),
            'exam_hospital': self.exam_hospital,
            'result': self.result,
            'report_img': self.report_img,
            'notes': self.notes,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S')
        }

class DailyHealthCheck(db.Model):
    """日常健康检查表"""
    __tablename__ = 'daily_health_checks'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    check_date = db.Column(db.Date, nullable=False)
    temperature = db.Column(db.Float, nullable=False)
    health_status = db.Column(db.String(20), nullable=False)  # 正常/异常
    symptoms = db.Column(db.Text, nullable=True)
    checker_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系
    checker = db.relationship('User', backref='health_checks')

    def to_dict(self):
        return {
            'id': self.id,
            'employee_id': self.employee_id,
            'check_date': format_datetime(self.check_date, '%Y-%m-%d'),
            'temperature': self.temperature,
            'health_status': self.health_status,
            'symptoms': self.symptoms,
            'checker_id': self.checker_id,
            'checker_name': self.checker.real_name or self.checker.username,
            'notes': self.notes,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S')
        }

class TrainingRecord(db.Model):
    """培训记录表"""
    __tablename__ = 'training_records'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    training_name = db.Column(db.String(100), nullable=False)
    training_date = db.Column(db.Date, nullable=False)
    expire_date = db.Column(db.Date, nullable=True)
    certificate_no = db.Column(db.String(50), nullable=True)
    certificate_img = db.Column(db.String(200), nullable=True)
    score = db.Column(db.Float, nullable=True)
    trainer = db.Column(db.String(50), nullable=True)
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)

    def to_dict(self):
        return {
            'id': self.id,
            'employee_id': self.employee_id,
            'training_name': self.training_name,
            'training_date': format_datetime(self.training_date, '%Y-%m-%d'),
            'expire_date': format_datetime(self.expire_date, '%Y-%m-%d') if self.expire_date else None,
            'certificate_no': self.certificate_no,
            'certificate_img': self.certificate_img,
            'score': self.score,
            'trainer': self.trainer,
            'notes': self.notes,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S')
        }

class AdministrativeArea(db.Model):
    """行政区划表"""
    __tablename__ = 'administrative_areas'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    code = db.Column(db.String(50), nullable=False, unique=True)
    level = db.Column(db.Integer, nullable=False)  # 1:县市区, 2:乡镇, 3:学校, 4:食堂
    parent_id = db.Column(db.Integer, db.ForeignKey('administrative_areas.id'), nullable=True)
    description = db.Column(db.Text, nullable=True)
    status = db.Column(db.Integer, default=1, nullable=False)  # 0:禁用, 1:启用
    is_township_school = db.Column(db.Boolean, default=False, nullable=False)  # 是否为乡镇级别直接关联的学校
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系
    parent = db.relationship('AdministrativeArea', remote_side=lambda: [AdministrativeArea.id], backref='children')
    users = db.relationship('User', backref='area', lazy='dynamic')

    def get_ancestors(self):
        """获取所有上级区域"""
        ancestors = []
        current = self.parent
        while current:
            ancestors.append(current)
            current = current.parent
        return ancestors

    def get_path_ids(self):
        """获取区域路径ID列表（从顶级到当前区域）"""
        path_ids = []
        # 先获取所有上级区域
        ancestors = self.get_ancestors()
        # 反转列表，使其从顶级到当前区域
        ancestors.reverse()
        # 添加所有上级区域ID
        for ancestor in ancestors:
            path_ids.append(ancestor.id)
        # 添加当前区域ID
        path_ids.append(self.id)
        return path_ids

    def get_descendants(self):
        """获取所有下级区域"""
        # 使用递归查询获取所有下级区域
        descendants = []

        # 使用广度优先搜索，避免递归过深
        queue = list(self.children)
        while queue:
            child = queue.pop(0)
            descendants.append(child)
            queue.extend(child.children)

        return descendants

    def get_descendants_ids(self):
        """获取所有下级区域ID"""
        return [area.id for area in self.get_descendants()]

    def get_level_name(self):
        """获取级别名称"""
        level_names = {
            1: '县市区',
            2: '乡镇',
            3: '学校',
            4: '食堂'
        }
        return level_names.get(self.level, '未知')

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'code': self.code,
            'level': self.level,
            'level_name': self.get_level_name(),
            'parent_id': self.parent_id,
            'description': self.description,
            'status': self.status,
            'is_township_school': self.is_township_school,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S')
        }

    def delete_area_and_related_data(self, area_id):
        """删除指定区域及其所有相关数据
        
        Args:
            area_id: 要删除的区域ID
            
        Returns:
            bool: 删除是否成功
            
        Raises:
            Exception: 删除过程中的任何错误
        """
        try:
            # 获取区域对象
            area = AdministrativeArea.query.get(area_id)
            if not area:
                raise ValueError(f"区域 {area_id} 不存在")
                
            # 开始事务
            db.session.begin_nested()
            
            # 1. 获取所有子区域
            child_areas = area.get_descendants()
            
            # 2. 按照正确的顺序删除数据
            # 先删除子区域的数据
            for child_area in child_areas:
                self._delete_area_related_data(child_area)
            
            # 再删除当前区域的数据
            self._delete_area_related_data(area)
            
            # 3. 删除子区域
            for child_area in child_areas:
                db.session.delete(child_area)
            
            # 4. 删除当前区域
            db.session.delete(area)
            
            # 提交事务
            db.session.commit()
            return True
            
        except Exception as e:
            # 发生错误时回滚事务
            db.session.rollback()
            raise e

    def _delete_area_related_data(self, area):
        """删除区域相关的所有数据，按照正确的顺序删除
        
        Args:
            area: AdministrativeArea对象
        """
        try:
            # 1. 删除通知相关数据
            Notification.query.filter_by(user_id=User.query.filter_by(area_id=area.id).with_entities(User.id)).delete(synchronize_session=False)
            
            # 2. 删除用户角色关联
            UserRole.query.filter_by(user_id=User.query.filter_by(area_id=area.id).with_entities(User.id)).delete(synchronize_session=False)
            
            # 3. 删除用户
            User.query.filter_by(area_id=area.id).delete()
            
            # 4. 删除员工相关数据
            # 先删除员工的健康证、体检记录等
            employee_ids = Employee.query.filter_by(area_id=area.id).with_entities(Employee.id)
            HealthCertificate.query.filter(HealthCertificate.employee_id.in_(employee_ids)).delete(synchronize_session=False)
            MedicalExamination.query.filter(MedicalExamination.employee_id.in_(employee_ids)).delete(synchronize_session=False)
            DailyHealthCheck.query.filter(DailyHealthCheck.employee_id.in_(employee_ids)).delete(synchronize_session=False)
            TrainingRecord.query.filter(TrainingRecord.employee_id.in_(employee_ids)).delete(synchronize_session=False)
            Employee.query.filter_by(area_id=area.id).delete()
            
            # 5. 删除仓库相关数据
            warehouses = Warehouse.query.filter_by(area_id=area.id).all()
            for warehouse in warehouses:
                # 删除库存
                Inventory.query.filter_by(warehouse_id=warehouse.id).delete()
                # 删除存储位置
                StorageLocation.query.filter_by(warehouse_id=warehouse.id).delete()
                # 删除入库记录
                stock_in_ids = StockIn.query.filter_by(warehouse_id=warehouse.id).with_entities(StockIn.id)
                StockInItem.query.filter(StockInItem.stock_in_id.in_(stock_in_ids)).delete(synchronize_session=False)
                StockIn.query.filter_by(warehouse_id=warehouse.id).delete()
                # 删除出库记录
                stock_out_ids = StockOut.query.filter_by(warehouse_id=warehouse.id).with_entities(StockOut.id)
                StockOutItem.query.filter(StockOutItem.stock_out_id.in_(stock_out_ids)).delete(synchronize_session=False)
                StockOut.query.filter_by(warehouse_id=warehouse.id).delete()
                # 删除盘点记录
                check_ids = InventoryCheck.query.filter_by(warehouse_id=warehouse.id).with_entities(InventoryCheck.id)
                InventoryCheckItem.query.filter(InventoryCheckItem.check_id.in_(check_ids)).delete(synchronize_session=False)
                InventoryCheck.query.filter_by(warehouse_id=warehouse.id).delete()
                # 删除仓库
                db.session.delete(warehouse)
            
            # 6. 删除采购相关数据
            # 删除采购订单
            purchase_orders = PurchaseOrder.query.filter_by(area_id=area.id).all()
            for order in purchase_orders:
                # 删除采购订单项
                PurchaseOrderItem.query.filter_by(order_id=order.id).delete()
                # 删除采购订单
                db.session.delete(order)
            
            # 删除采购申请
            requisitions = PurchaseRequisition.query.filter_by(area_id=area.id).all()
            for req in requisitions:
                # 删除采购申请项
                PurchaseRequisitionItem.query.filter_by(requisition_id=req.id).delete()
                # 删除采购申请
                db.session.delete(req)
            
            # 7. 删除菜单相关数据已移除（MenuPlan/MenuRecipe已删除）
            
            # 删除消耗计划
            consumption_plans = ConsumptionPlan.query.filter_by(area_id=area.id).all()
            for plan in consumption_plans:
                # 删除消耗明细
                ConsumptionDetail.query.filter_by(consumption_plan_id=plan.id).delete()
                # 删除消耗计划
                db.session.delete(plan)
            
            # 8. 删除留样记录
            FoodSample.query.filter_by(area_id=area.id).delete()
            
            # 9. 删除库存预警
            InventoryAlert.query.filter_by(area_id=area.id).delete()
            
            # 10. 删除周菜单
            weekly_menus = WeeklyMenu.query.filter_by(area_id=area.id).all()
            for menu in weekly_menus:
                # 删除周菜单食谱
                WeeklyMenuRecipe.query.filter_by(weekly_menu_id=menu.id).delete()
                # 删除周菜单
                db.session.delete(menu)
            
            # 11. 删除审计日志
            AuditLog.query.filter_by(area_id=area.id).delete()
            
            # 12. 删除区域变更历史
            AreaChangeHistory.query.filter_by(area_id=area.id).delete()
            
            # 13. 删除供应商学校关联
            SupplierSchoolRelation.query.filter_by(area_id=area.id).delete()
            
            db.session.flush()
            
        except Exception as e:
            db.session.rollback()
            raise e

class AreaChangeHistory(db.Model):
    """区域变更历史表"""
    __tablename__ = 'area_change_history'

    id = db.Column(db.Integer, primary_key=True)
    area_id = db.Column(db.Integer, db.ForeignKey('administrative_areas.id'), nullable=False)
    change_type = db.Column(db.String(20), nullable=False)  # create, update, move, delete
    old_parent_id = db.Column(db.Integer, nullable=True)
    new_parent_id = db.Column(db.Integer, nullable=True)
    old_data = db.Column(db.Text, nullable=True)  # JSON格式的旧数据
    new_data = db.Column(db.Text, nullable=True)  # JSON格式的新数据
    changed_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系
    area = db.relationship('AdministrativeArea', backref='change_history')
    user = db.relationship('User', backref='area_changes')

class RecipeProcess(db.Model):
    """食谱工序表"""
    __tablename__ = 'recipe_processes'

    id = db.Column(db.Integer, primary_key=True)
    recipe_id = db.Column(db.Integer, db.ForeignKey('recipes.id'), nullable=False)
    process_name = db.Column(db.String(100), nullable=False)
    process_order = db.Column(db.Integer, nullable=False)
    description = db.Column(db.Text, nullable=True)
    estimated_time = db.Column(db.Integer, nullable=True)  # 预计时间（分钟）
    image = db.Column(db.String(200), nullable=True)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系
    ingredients = db.relationship('RecipeProcessIngredient', backref='process', lazy='dynamic')

    def to_dict(self):
        return {
            'id': self.id,
            'recipe_id': self.recipe_id,
            'process_name': self.process_name,
            'process_order': self.process_order,
            'description': self.description,
            'estimated_time': self.estimated_time,
            'image': self.image,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S'),
            'ingredients': [ing.to_dict() for ing in self.ingredients]
        }

class RecipeProcessIngredient(db.Model):
    """工序食材关联表"""
    __tablename__ = 'recipe_process_ingredients'

    id = db.Column(db.Integer, primary_key=True)
    process_id = db.Column(db.Integer, db.ForeignKey('recipe_processes.id'), nullable=False)
    ingredient_id = db.Column(db.Integer, db.ForeignKey('ingredients.id'), nullable=False)
    quantity = db.Column(db.Float, nullable=False)
    unit = db.Column(db.String(20), nullable=False)
    processing_method = db.Column(db.String(100), nullable=True)  # 处理方法（切片、切丁等）
    notes = db.Column(db.String(200), nullable=True)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系
    ingredient = db.relationship('Ingredient')

    def to_dict(self):
        return {
            'id': self.id,
            'process_id': self.process_id,
            'ingredient_id': self.ingredient_id,
            'ingredient_name': self.ingredient.name,
            'quantity': self.quantity,
            'unit': self.unit,
            'processing_method': self.processing_method,
            'notes': self.notes
        }

class AuditLog(db.Model):
    """审计日志表"""
    __tablename__ = 'audit_logs'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    action = db.Column(db.String(50), nullable=False)  # create, update, delete, view
    resource_type = db.Column(db.String(50), nullable=False)  # 资源类型
    resource_id = db.Column(db.Integer, nullable=True)  # 资源ID
    area_id = db.Column(db.Integer, db.ForeignKey('administrative_areas.id'), nullable=True)
    details = db.Column(db.Text, nullable=True)  # 详细信息(JSON)
    ip_address = db.Column(db.String(50), nullable=True)
    user_agent = db.Column(db.String(200), nullable=True)
    created_at = db.Column(DATETIME2(precision=1), server_default=db.text('GETDATE()'), nullable=False)

    # 关系
    user = db.relationship('User', backref='audit_logs')
    area = db.relationship('AdministrativeArea', backref='audit_logs')

class InventoryAlert(db.Model):
    """库存预警表"""
    __tablename__ = 'inventory_alerts'

    id = db.Column(db.Integer, primary_key=True)
    ingredient_id = db.Column(db.Integer, db.ForeignKey('ingredients.id'), nullable=False)
    area_id = db.Column(db.Integer, db.ForeignKey('administrative_areas.id'), nullable=False)
    min_quantity = db.Column(db.Float, nullable=True)  # 最小库存量
    max_quantity = db.Column(db.Float, nullable=True)  # 最大库存量
    current_quantity = db.Column(db.Float, nullable=False)  # 当前库存量
    unit = db.Column(db.String(20), nullable=False)  # 单位
    alert_type = db.Column(db.String(20), nullable=False)  # 预警类型（库存不足/库存过多/临近过期）
    status = db.Column(db.String(20), nullable=False, default='未处理')  # 状态（未处理/已处理）
    processed_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)  # 处理人
    processed_at = db.Column(DATETIME2(precision=1), nullable=True)  # 处理时间
    notes = db.Column(db.Text, nullable=True)  # 备注
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)

    # 关系
    ingredient = db.relationship('Ingredient', backref='alerts')
    area = db.relationship('AdministrativeArea', backref='inventory_alerts')
    processor = db.relationship('User', foreign_keys=[processed_by], backref='processed_alerts')

    def to_dict(self):
        return {
            'id': self.id,
            'ingredient_id': self.ingredient_id,
            'ingredient_name': self.ingredient.name,
            'area_id': self.area_id,
            'area_name': self.area.name,
            'min_quantity': self.min_quantity,
            'max_quantity': self.max_quantity,
            'current_quantity': self.current_quantity,
            'unit': self.unit,
            'alert_type': self.alert_type,
            'status': self.status,
            'processed_by': self.processed_by,
            'processor_name': self.processor.real_name or self.processor.username if self.processor else None,
            'processed_at': format_datetime(self.processed_at, '%Y-%m-%d %H:%M:%S') if self.processed_at else None,
            'notes': self.notes,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S'),
            'updated_at': format_datetime(self.updated_at, '%Y-%m-%d %H:%M:%S')
        }

class WeeklyMenu(db.Model):
    """周菜单表"""
    __tablename__ = 'weekly_menus'

    id = db.Column(db.Integer, primary_key=True)
    area_id = db.Column(db.Integer, db.ForeignKey('administrative_areas.id'), nullable=False)
    week_start = db.Column(db.Date, nullable=False)  # 周开始日期（周一）
    week_end = db.Column(db.Date, nullable=False)    # 周结束日期（周五或周日）
    status = db.Column(db.String(20), nullable=False, default='计划中')  # 计划中/已发布
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(DATETIME2(precision=1),
                          default=lambda: datetime.now().replace(microsecond=0),
                          nullable=False)
    updated_at = db.Column(DATETIME2(precision=1),
                          default=lambda: datetime.now().replace(microsecond=0),
                          onupdate=lambda: datetime.now().replace(microsecond=0),
                          nullable=False)

    # 关系
    area = db.relationship('AdministrativeArea', backref='weekly_menus')
    creator = db.relationship('User', foreign_keys=[created_by], backref='created_weekly_menus')
    menu_recipes = db.relationship('WeeklyMenuRecipe', backref='weekly_menu', lazy='dynamic')

    def to_dict(self):
        return {
            'id': self.id,
            'area_id': self.area_id,
            'area_name': self.area.name if self.area else None,
            'week_start': format_datetime(self.week_start, '%Y-%m-%d'),
            'week_end': format_datetime(self.week_end, '%Y-%m-%d'),
            'status': self.status,
            'created_by': self.created_by,
            'creator_name': self.creator.real_name or self.creator.username if self.creator else None,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S'),
            'updated_at': format_datetime(self.updated_at, '%Y-%m-%d %H:%M:%S')
        }

class WeeklyMenuRecipe(db.Model):
    """周菜单食谱关联表"""
    __tablename__ = 'weekly_menu_recipes'

    id = db.Column(db.Integer, primary_key=True)
    weekly_menu_id = db.Column(db.Integer, db.ForeignKey('weekly_menus.id'), nullable=False)
    day_of_week = db.Column(db.Integer, nullable=False)  # 1-7 表示周一到周日
    meal_type = db.Column(db.String(20), nullable=False)  # 早餐/午餐/晚餐
    recipe_id = db.Column(db.Integer, db.ForeignKey('recipes.id'), nullable=True)  # 可以为空，表示自定义食谱
    recipe_name = db.Column(db.String(100), nullable=False)  # 食谱名称，可以是系统食谱或自定义
    created_at = db.Column(DATETIME2(precision=1),
                          default=lambda: datetime.now().replace(microsecond=0),
                          nullable=False)
    updated_at = db.Column(DATETIME2(precision=1),
                          default=lambda: datetime.now().replace(microsecond=0),
                          onupdate=lambda: datetime.now().replace(microsecond=0),
                          nullable=False)

    # 关系
    recipe = db.relationship('Recipe', foreign_keys=[recipe_id])

    def to_dict(self):
        return {
            'id': self.id,
            'weekly_menu_id': self.weekly_menu_id,
            'day_of_week': self.day_of_week,
            'meal_type': self.meal_type,
            'recipe_id': self.recipe_id,
            'recipe_name': self.recipe_name,
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S'),
            'updated_at': format_datetime(self.updated_at, '%Y-%m-%d %H:%M:%S')
        }

class Notification(db.Model):
    """通知模型"""
    __tablename__ = 'notifications'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    title = db.Column(db.String(100), nullable=False)
    content = db.Column(db.Text, nullable=False)
    notification_type = db.Column(db.String(50), nullable=False)  # 通知类型
    level = db.Column(db.Integer, default=0)  # 通知级别: 0-普通, 1-重要, 2-紧急
    reference_id = db.Column(db.Integer)  # 引用ID，如健康证ID
    reference_type = db.Column(db.String(50))  # 引用类型
    is_read = db.Column(db.Boolean, default=False)  # 是否已读
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0))

    # 关联用户
    user = db.relationship('User', backref=db.backref('notifications', lazy='dynamic'))

    @property
    def formatted_created_time(self):
        """格式化创建时间"""
        now = datetime.now()

        # 确保 created_at 是 datetime 对象
        if isinstance(self.created_at, str):
            try:
                # 尝试将字符串转换为 datetime 对象
                created_at = datetime.strptime(self.created_at, '%Y-%m-%d %H:%M:%S')
            except ValueError:
                try:
                    # 尝试另一种常见的日期格式
                    created_at = datetime.strptime(self.created_at, '%Y-%m-%d')
                except ValueError:
                    # 如果无法解析，返回原始字符串
                    return self.created_at
        else:
            created_at = self.created_at

        delta = now - created_at

        if delta.days == 0:
            if delta.seconds < 60:
                return "刚刚"
            elif delta.seconds < 3600:
                return f"{delta.seconds // 60}分钟前"
            else:
                return f"{delta.seconds // 3600}小时前"
        elif delta.days == 1:
            return "昨天"
        elif delta.days < 7:
            return f"{delta.days}天前"
        else:
            return format_datetime(created_at, '%Y-%m-%d')

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'title': self.title,
            'content': self.content,
            'notification_type': self.notification_type,
            'level': self.level,
            'is_read': self.is_read,
            'created_at': self.formatted_created_time
        }

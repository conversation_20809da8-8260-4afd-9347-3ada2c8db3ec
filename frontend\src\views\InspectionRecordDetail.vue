<template>
  <div class="page-container">
    <el-card v-loading="loading">
      <template #header>
        <div class="card-header">
          <span>InspectionRecord详情</span>
          <div>
            <el-button type="warning" @click="handleEdit">编辑</el-button>
            <el-button @click="handleBack">返回</el-button>
          </div>
        </div>
      </template>

      <el-descriptions :column="2" border>
        
        <el-descriptions-item label="">
          {{ data.{{ field.name }} }}
        </el-descriptions-item>
        
        <el-descriptions-item label="">
          {{ data.{{ field.name }} }}
        </el-descriptions-item>
        
        <el-descriptions-item label="">
          {{ data.{{ field.name }} }}
        </el-descriptions-item>
        
        <el-descriptions-item label="">
          {{ data.{{ field.name }} }}
        </el-descriptions-item>
        
        <el-descriptions-item label="">
          {{ data.{{ field.name }} }}
        </el-descriptions-item>
        
        <el-descriptions-item label="">
          {{ data.{{ field.name }} }}
        </el-descriptions-item>
        
        <el-descriptions-item label="">
          {{ data.{{ field.name }} }}
        </el-descriptions-item>
        
        <el-descriptions-item label="">
          {{ data.{{ field.name }} }}
        </el-descriptions-item>
        
        <el-descriptions-item label="">
          {{ data.{{ field.name }} }}
        </el-descriptions-item>
        
        <el-descriptions-item label="">
          {{ data.{{ field.name }} }}
        </el-descriptions-item>
        
        <el-descriptions-item label="">
          {{ data.{{ field.name }} }}
        </el-descriptions-item>
        
        <el-descriptions-item label="">
          {{ data.{{ field.name }} }}
        </el-descriptions-item>
        
        <el-descriptions-item label="">
          {{ data.{{ field.name }} }}
        </el-descriptions-item>
        
      </el-descriptions>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useInspectionRecordStore } from '@/stores/inspectionrecord'

const route = useRoute()
const router = useRouter()
const inspectionrecordStore = useInspectionRecordStore()

// 响应式数据
const loading = ref(false)
const data = ref({})

// 方法
const fetchData = async () => {
  loading.value = true
  try {
    data.value = await inspectionrecordStore.fetchById(route.params.id)
  } catch (error) {
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const handleEdit = () => {
  router.push({ name: 'InspectionRecordForm', params: { id: route.params.id } })
}

const handleBack = () => {
  router.push({ name: 'InspectionRecordList' })
}

// 生命周期
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
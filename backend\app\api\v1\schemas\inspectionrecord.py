"""
InspectionRecord 序列化模式
"""

from marshmallow import Schema, fields, validate

class InspectionRecordSchema(Schema):
    """InspectionRecord 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    daily_log_id = fields.Integer(required=True)
    
    
    
    inspection_type = fields.String(required=True)
    
    
    
    inspection_item = fields.String(required=True)
    
    
    
    status = fields.String()
    
    
    
    description = fields.String()
    
    
    
    photo_path = fields.String()
    
    
    
    inspector_id = fields.Integer()
    
    
    
    reference_type = fields.String()
    
    
    
    reference_id = fields.Integer()
    
    
    
    inspection_time = fields.String(required=True)
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
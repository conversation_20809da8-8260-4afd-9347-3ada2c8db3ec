import request from '@/utils/request'

const inspectionrecordAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/inspectionrecord',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/inspectionrecord/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/inspectionrecord',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/inspectionrecord/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/inspectionrecord/${id}`,
      method: 'delete'
    })
  }
}

export default inspectionrecordAPI
import request from '@/utils/request'

const recipeversionAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/recipeversion',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/recipeversion/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/recipeversion',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/recipeversion/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/recipeversion/${id}`,
      method: 'delete'
    })
  }
}

export default recipeversionAPI
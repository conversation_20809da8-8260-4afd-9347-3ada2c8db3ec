"""
在线咨询API
独立的API蓝图，不依赖URL前缀
"""

from flask import Blueprint, request, jsonify, current_app
from app import db
from sqlalchemy import text
import re

# 创建独立的API蓝图
consultation_api_bp = Blueprint('consultation_api', __name__)


@consultation_api_bp.route('/api/consultation/submit', methods=['POST'])
def submit_consultation():
    """提交在线咨询（API接口）"""
    try:
        # 获取表单数据
        data = request.get_json() if request.is_json else request.form

        name = data.get('name', '').strip()
        contact_type = data.get('contact_type', '微信').strip()
        contact_value = data.get('contact_value', '').strip()
        content = data.get('content', '').strip()

        # 基本验证
        if not name:
            return jsonify({'success': False, 'message': '请输入您的姓名'})

        if len(name) < 2 or len(name) > 50:
            return jsonify({'success': False, 'message': '姓名长度应在2-50个字符之间'})

        if not contact_value:
            return jsonify({'success': False, 'message': '请输入您的联系方式'})

        if len(contact_value) < 3 or len(contact_value) > 100:
            return jsonify({'success': False, 'message': '联系方式长度应在3-100个字符之间'})

        if not content:
            return jsonify({'success': False, 'message': '请输入咨询内容'})

        if len(content) < 10 or len(content) > 1000:
            return jsonify({'success': False, 'message': '咨询内容长度应在10-1000个字符之间'})

        # 联系方式格式验证
        if contact_type == '电话':
            phone_pattern = r'^1[3-9]\d{9}$'
            if not re.match(phone_pattern, contact_value):
                return jsonify({'success': False, 'message': '请输入正确的手机号码格式'})
        elif contact_type == '邮箱':
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, contact_value):
                return jsonify({'success': False, 'message': '请输入正确的邮箱格式'})
        elif contact_type == '微信':
            wechat_pattern = r'^[a-zA-Z0-9_-]{6,20}$'
            if not re.match(wechat_pattern, contact_value):
                return jsonify({'success': False, 'message': '微信号应为6-20位字母、数字、下划线或减号组合'})

        # 获取客户端信息
        ip_address = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR'))
        user_agent = request.headers.get('User-Agent', '')[:500]  # 限制长度

        # 检查表是否存在
        try:
            check_table_sql = text("""
                SELECT COUNT(*) as count
                FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_NAME = 'online_consultations'
            """)
            result = db.session.execute(check_table_sql)
            table_exists = result.fetchone()[0] > 0

            if not table_exists:
                return jsonify({
                    'success': False,
                    'message': '系统尚未初始化，请联系管理员'
                })
        except Exception as e:
            current_app.logger.error(f"检查表存在性失败: {str(e)}")
            return jsonify({'success': False, 'message': '系统错误，请稍后重试'})

        # 插入数据
        try:
            # 按照README.md的最佳实践：不手动设置时间字段，让数据库使用默认值
            insert_sql = text("""
                INSERT INTO online_consultations
                (name, contact_type, contact_value, content, status, source, ip_address, user_agent)
                OUTPUT inserted.id
                VALUES
                (:name, :contact_type, :contact_value, :content, :status, :source, :ip_address, :user_agent)
            """)

            result = db.session.execute(insert_sql, {
                'name': name,
                'contact_type': contact_type,
                'contact_value': contact_value,
                'content': content,
                'status': '待处理',
                'source': '官网首页',
                'ip_address': ip_address,
                'user_agent': user_agent
            })

            consultation_id = result.fetchone()[0]
            db.session.commit()

            # 记录日志
            current_app.logger.info(f"新的在线咨询提交: ID={consultation_id}, 姓名={name}, 联系方式={contact_type}:{contact_value}")

            return jsonify({
                'success': True,
                'message': '咨询提交成功！我们会尽快与您联系。',
                'consultation_id': consultation_id
            })

        except Exception as db_error:
            db.session.rollback()
            current_app.logger.error(f"数据库插入失败: {str(db_error)}")
            return jsonify({'success': False, 'message': '数据保存失败，请稍后重试'})

    except Exception as e:
        current_app.logger.error(f"提交在线咨询失败: {str(e)}")
        db.session.rollback()
        return jsonify({'success': False, 'message': '提交失败，请稍后重试'})


@consultation_api_bp.route('/api/consultation/test', methods=['GET'])
def test_api():
    """测试API是否工作"""
    try:
        # 检查数据库连接
        test_sql = text("SELECT 1 as test")
        db.session.execute(test_sql)

        # 检查表是否存在
        check_table_sql = text("""
            SELECT COUNT(*) as count
            FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_NAME = 'online_consultations'
        """)
        result = db.session.execute(check_table_sql)
        table_exists = result.fetchone()[0] > 0

        return jsonify({
            'success': True,
            'message': '在线咨询API正常工作',
            'database_connected': True,
            'table_exists': table_exists,
            'timestamp': str(__import__('datetime').datetime.now())
        })

    except Exception as e:
        current_app.logger.error(f"API测试失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'API测试失败: {str(e)}',
            'database_connected': False,
            'table_exists': False
        })


@consultation_api_bp.route('/api/consultation/stats', methods=['GET'])
def get_stats():
    """获取咨询统计（简化版）"""
    try:
        # 检查表是否存在
        check_table_sql = text("""
            SELECT COUNT(*) as count
            FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_NAME = 'online_consultations'
        """)
        result = db.session.execute(check_table_sql)
        table_exists = result.fetchone()[0] > 0

        if not table_exists:
            return jsonify({
                'success': False,
                'message': '咨询表不存在'
            })

        # 获取基本统计
        stats_sql = text("""
            SELECT
                COUNT(*) as total,
                COUNT(CASE WHEN status = '待处理' THEN 1 END) as pending,
                COUNT(CASE WHEN status = '已回复' THEN 1 END) as replied,
                COUNT(CASE WHEN status = '已关闭' THEN 1 END) as closed
            FROM online_consultations
        """)

        result = db.session.execute(stats_sql)
        stats = result.fetchone()

        return jsonify({
            'success': True,
            'data': {
                'total': stats[0],
                'pending': stats[1],
                'replied': stats[2],
                'closed': stats[3]
            }
        })

    except Exception as e:
        current_app.logger.error(f"获取统计失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取统计失败: {str(e)}'
        })

"""
WeeklyMenu 序列化模式
"""

from marshmallow import Schema, fields, validate

class WeeklyMenuSchema(Schema):
    """WeeklyMenu 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    area_id = fields.Integer(required=True)
    
    
    
    week_start = fields.Date(required=True)
    
    
    
    week_end = fields.Date(required=True)
    
    
    
    status = fields.String(required=True)
    
    
    
    created_by = fields.Integer(required=True)
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
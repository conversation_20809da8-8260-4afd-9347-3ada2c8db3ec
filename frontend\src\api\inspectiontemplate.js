import request from '@/utils/request'

const inspectiontemplateAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/inspectiontemplate',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/inspectiontemplate/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/inspectiontemplate',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/inspectiontemplate/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/inspectiontemplate/${id}`,
      method: 'delete'
    })
  }
}

export default inspectiontemplateAPI
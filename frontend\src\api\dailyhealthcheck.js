import request from '@/utils/request'

const dailyhealthcheckAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/dailyhealthcheck',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/dailyhealthcheck/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/dailyhealthcheck',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/dailyhealthcheck/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/dailyhealthcheck/${id}`,
      method: 'delete'
    })
  }
}

export default dailyhealthcheckAPI
"""
IngredientInspection 序列化模式
"""

from marshmallow import Schema, fields, validate

class IngredientInspectionSchema(Schema):
    """IngredientInspection 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    stock_in_item_id = fields.Integer(required=True)
    
    
    
    inspector_id = fields.Integer(required=True)
    
    
    
    inspection_date = fields.Date(required=True)
    
    
    
    inspection_type = fields.String(required=True)
    
    
    
    result = fields.String(required=True)
    
    
    
    notes = fields.String()
    
    
    
    document_id = fields.Integer()
    
    
    
    created_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
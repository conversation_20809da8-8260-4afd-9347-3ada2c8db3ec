import request from '@/utils/request'

const warehouseAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/warehouse',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/warehouse/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/warehouse',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/warehouse/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/warehouse/${id}`,
      method: 'delete'
    })
  }
}

export default warehouseAPI
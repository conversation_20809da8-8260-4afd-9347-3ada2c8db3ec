"""
InspectionTemplate 序列化模式
"""

from marshmallow import Schema, fields, validate

class InspectionTemplateSchema(Schema):
    """InspectionTemplate 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    name = fields.String(required=True)
    
    
    
    description = fields.String()
    
    
    
    category = fields.String(required=True)
    
    
    
    items = fields.String(required=True)
    
    
    
    is_default = fields.Boolean()
    
    
    
    created_by = fields.Integer()
    
    
    
    area_id = fields.Integer()
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
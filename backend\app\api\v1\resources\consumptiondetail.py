"""
ConsumptionDetail API 资源
"""

from flask import request
from flask_restful import Resource
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.services.consumptiondetail_service import ConsumptionDetailService
from app.api.v1.schemas.consumptiondetail import ConsumptionDetailSchema
from app.utils.response import success_response, error_response

class ConsumptionDetailListAPI(Resource):
    """ConsumptionDetail 列表 API"""

    @jwt_required()
    def get(self):
        """获取ConsumptionDetail列表"""
        try:
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 10, type=int)

            result = ConsumptionDetailService.get_list(page=page, per_page=per_page)

            schema = ConsumptionDetailSchema(many=True)
            return success_response(
                data={
                    'items': schema.dump(result.items),
                    'total': result.total,
                    'page': result.page,
                    'per_page': result.per_page
                }
            )
        except Exception as e:
            return error_response(message=str(e))

    @jwt_required()
    def post(self):
        """创建ConsumptionDetail"""
        try:
            data = request.get_json()
            schema = ConsumptionDetailSchema()

            # 验证数据
            validated_data = schema.load(data)

            # 创建记录
            item = ConsumptionDetailService.create(validated_data)

            return success_response(
                data=schema.dump(item),
                message='创建成功'
            )
        except Exception as e:
            return error_response(message=str(e))

class ConsumptionDetailAPI(Resource):
    """ConsumptionDetail 详情 API"""

    @jwt_required()
    def get(self, id):
        """获取ConsumptionDetail详情"""
        try:
            item = ConsumptionDetailService.get_by_id(id)
            if not item:
                return error_response(message='记录不存在', code=404)

            schema = ConsumptionDetailSchema()
            return success_response(data=schema.dump(item))
        except Exception as e:
            return error_response(message=str(e))

    @jwt_required()
    def put(self, id):
        """更新ConsumptionDetail"""
        try:
            data = request.get_json()
            schema = ConsumptionDetailSchema()

            # 验证数据
            validated_data = schema.load(data, partial=True)

            # 更新记录
            item = ConsumptionDetailService.update(id, validated_data)
            if not item:
                return error_response(message='记录不存在', code=404)

            return success_response(
                data=schema.dump(item),
                message='更新成功'
            )
        except Exception as e:
            return error_response(message=str(e))

    @jwt_required()
    def delete(self, id):
        """删除ConsumptionDetail"""
        try:
            success = ConsumptionDetailService.delete(id)
            if not success:
                return error_response(message='记录不存在', code=404)

            return success_response(message='删除成功')
        except Exception as e:
            return error_response(message=str(e))
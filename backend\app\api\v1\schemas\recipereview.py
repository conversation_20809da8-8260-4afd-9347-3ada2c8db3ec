"""
RecipeReview 序列化模式
"""

from marshmallow import Schema, fields, validate

class RecipeReviewSchema(Schema):
    """RecipeReview 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    recipe_id = fields.Integer(required=True)
    
    
    
    user_id = fields.Integer(required=True)
    
    
    
    area_id = fields.Integer(required=True)
    
    
    
    rating = fields.Integer(required=True)
    
    
    
    comment = fields.String()
    
    
    
    usage_date = fields.Date()
    
    
    
    is_public = fields.Boolean()
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
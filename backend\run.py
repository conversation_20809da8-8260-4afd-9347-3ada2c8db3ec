"""
应用启动文件
"""

import os
from app import create_app

# 获取配置环境
config_name = os.environ.get('FLASK_ENV', 'development')
app = create_app(config_name)

if __name__ == '__main__':
    print(f"🚀 启动 Flask API 服务器 (环境: {config_name})")
    print(f"📊 数据库: SQL Server (**************)")
    print(f"🌐 访问地址: http://localhost:5001")
    print(f"🔍 健康检查: http://localhost:5001/health")
    print(f"🗄️ 数据库测试: http://localhost:5001/api/v1/test-db")

    app.run(
        debug=(config_name == 'development'),
        host='0.0.0.0',
        port=5001
    )
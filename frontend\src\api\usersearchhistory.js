import request from '@/utils/request'

const usersearchhistoryAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/usersearchhistory',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/usersearchhistory/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/usersearchhistory',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/usersearchhistory/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/usersearchhistory/${id}`,
      method: 'delete'
    })
  }
}

export default usersearchhistoryAPI
import request from '@/utils/request'

const homepagecarouselAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/homepagecarousel',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/homepagecarousel/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/homepagecarousel',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/homepagecarousel/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/homepagecarousel/${id}`,
      method: 'delete'
    })
  }
}

export default homepagecarouselAPI
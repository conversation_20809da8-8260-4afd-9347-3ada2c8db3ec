import request from '@/utils/request'

const stockoutAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/stockout',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/stockout/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/stockout',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/stockout/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/stockout/${id}`,
      method: 'delete'
    })
  }
}

export default stockoutAPI
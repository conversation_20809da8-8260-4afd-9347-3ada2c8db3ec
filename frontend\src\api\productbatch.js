import request from '@/utils/request'

const productbatchAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/productbatch',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/productbatch/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/productbatch',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/productbatch/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/productbatch/${id}`,
      method: 'delete'
    })
  }
}

export default productbatchAPI
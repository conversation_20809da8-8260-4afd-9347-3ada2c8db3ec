"""
WeeklyMenuRecipe 序列化模式
"""

from marshmallow import Schema, fields, validate

class WeeklyMenuRecipeSchema(Schema):
    """WeeklyMenuRecipe 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    weekly_menu_id = fields.Integer(required=True)
    
    
    
    day_of_week = fields.Integer(required=True)
    
    
    
    meal_type = fields.String(required=True)
    
    
    
    recipe_id = fields.Integer()
    
    
    
    recipe_name = fields.String(required=True)
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
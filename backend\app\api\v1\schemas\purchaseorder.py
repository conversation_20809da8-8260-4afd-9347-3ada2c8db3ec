"""
PurchaseOrder 序列化模式
"""

from marshmallow import Schema, fields, validate

class PurchaseOrderSchema(Schema):
    """PurchaseOrder 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    order_number = fields.String(required=True)
    
    
    
    supplier_id = fields.Integer()
    
    
    
    requisition_id = fields.Integer()
    
    
    
    area_id = fields.Integer(required=True)
    
    
    
    total_amount = fields.String(required=True)
    
    
    
    order_date = fields.String(required=True)
    
    
    
    expected_delivery_date = fields.Date()
    
    
    
    payment_terms = fields.String()
    
    
    
    delivery_terms = fields.String()
    
    
    
    status = fields.String(required=True)
    
    
    
    delivery_date = fields.String()
    
    
    
    created_by = fields.Integer(required=True)
    
    
    
    approved_by = fields.Integer()
    
    
    
    notes = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
"""
数据库工具类
"""

from contextlib import contextmanager
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from flask import current_app

# 全局变量
_engine = None
_SessionLocal = None

def init_database(app):
    """初始化数据库连接"""
    global _engine, _SessionLocal

    # 获取数据库连接字符串
    database_uri = app.config['SQLALCHEMY_DATABASE_URI']
    engine_options = app.config.get('SQLALCHEMY_ENGINE_OPTIONS', {})

    _engine = create_engine(
        database_uri,
        echo=app.config.get('SQLALCHEMY_ECHO', False),
        **engine_options
    )

    _SessionLocal = sessionmaker(
        autocommit=False,
        autoflush=False,
        bind=_engine
    )

@contextmanager
def get_db_session():
    """获取数据库会话上下文管理器"""
    if _SessionLocal is None:
        raise RuntimeError("Database not initialized. Call init_database() first.")

    session = _SessionLocal()
    try:
        yield session
    except Exception:
        session.rollback()
        raise
    finally:
        session.close()

def get_db():
    """获取数据库会话（用于依赖注入）"""
    if _SessionLocal is None:
        raise RuntimeError("Database not initialized. Call init_database() first.")

    session = _SessionLocal()
    try:
        yield session
    finally:
        session.close()

def test_connection():
    """测试数据库连接"""
    try:
        if _engine is None:
            raise RuntimeError("Database not initialized. Call init_database() first.")

        from sqlalchemy import text
        with _engine.connect() as connection:
            result = connection.execute(text("SELECT 1"))
            return True
    except Exception as e:
        print(f"Database connection test failed: {e}")
        return False

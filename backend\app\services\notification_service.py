"""
通知服务模块
提供便捷的通知发送和管理功能
"""

from app import db
from app.models import Notification, User, AdministrativeArea
from datetime import datetime
from typing import List, Optional, Union
import logging

logger = logging.getLogger(__name__)

class NotificationService:
    """通知服务类"""

    # 通知类型常量
    TYPE_SYSTEM = 'system'
    TYPE_HEALTH_CERT = 'health_cert'
    TYPE_MENU = 'menu'
    TYPE_PURCHASE = 'purchase'
    TYPE_INSPECTION = 'inspection'
    TYPE_TASK = 'task'
    TYPE_INVENTORY = 'inventory'
    TYPE_EXPIRY = 'expiry'
    TYPE_STOCK_IN = 'stock_in'
    TYPE_CONSUMPTION = 'consumption'

    # 通知级别常量
    LEVEL_NORMAL = 0    # 普通
    LEVEL_IMPORTANT = 1 # 重要
    LEVEL_URGENT = 2    # 紧急

    @staticmethod
    def send_to_user(user_id: int, title: str, content: str,
                     notification_type: str = TYPE_SYSTEM,
                     level: int = LEVEL_NORMAL,
                     reference_id: Optional[int] = None,
                     reference_type: Optional[str] = None) -> Optional[Notification]:
        """
        发送通知给指定用户

        Args:
            user_id: 用户ID
            title: 通知标题
            content: 通知内容
            notification_type: 通知类型
            level: 通知级别
            reference_id: 引用ID
            reference_type: 引用类型

        Returns:
            Notification: 创建的通知对象，失败返回None
        """
        try:
            user = User.query.get(user_id)
            if not user:
                logger.error(f"用户不存在: {user_id}")
                return None

            # 使用原始SQL插入，避免SQLAlchemy的时间精度问题
            from sqlalchemy import text
            from datetime import datetime

            sql = text("""
                INSERT INTO notifications
                (user_id, title, content, notification_type, level, reference_id, reference_type, is_read, created_at)
                OUTPUT inserted.id
                VALUES
                (:user_id, :title, :content, :notification_type, :level, :reference_id, :reference_type, :is_read, GETDATE())
            """)

            result = db.session.execute(sql, {
                'user_id': user_id,
                'title': title,
                'content': content,
                'notification_type': notification_type,
                'level': level,
                'reference_id': reference_id,
                'reference_type': reference_type,
                'is_read': 0
            })

            notification_id = result.fetchone()[0]
            db.session.commit()

            # 返回创建的通知对象
            return Notification.query.get(notification_id)

        except Exception as e:
            logger.error(f"发送通知失败: {str(e)}")
            db.session.rollback()
            return None

    @staticmethod
    def send_to_users(user_ids: List[int], title: str, content: str,
                      notification_type: str = TYPE_SYSTEM,
                      level: int = LEVEL_NORMAL,
                      reference_id: Optional[int] = None,
                      reference_type: Optional[str] = None) -> List[Notification]:
        """
        批量发送通知给多个用户

        Args:
            user_ids: 用户ID列表
            title: 通知标题
            content: 通知内容
            notification_type: 通知类型
            level: 通知级别
            reference_id: 引用ID
            reference_type: 引用类型

        Returns:
            List[Notification]: 成功创建的通知列表
        """
        notifications = []
        for user_id in user_ids:
            notification = NotificationService.send_to_user(
                user_id=user_id,
                title=title,
                content=content,
                notification_type=notification_type,
                level=level,
                reference_id=reference_id,
                reference_type=reference_type
            )
            if notification:
                notifications.append(notification)
        return notifications

    @staticmethod
    def send_to_area(area_id: int, title: str, content: str,
                     notification_type: str = TYPE_SYSTEM,
                     level: int = LEVEL_NORMAL,
                     reference_id: Optional[int] = None,
                     reference_type: Optional[str] = None,
                     include_sub_areas: bool = True) -> List[Notification]:
        """
        发送通知给指定区域的所有用户

        Args:
            area_id: 区域ID
            title: 通知标题
            content: 通知内容
            notification_type: 通知类型
            level: 通知级别
            reference_id: 引用ID
            reference_type: 引用类型
            include_sub_areas: 是否包含子区域

        Returns:
            List[Notification]: 成功创建的通知列表
        """
        try:
            # 获取区域用户
            if include_sub_areas:
                # 获取当前区域及其所有子区域的用户
                area = AdministrativeArea.query.get(area_id)
                if not area:
                    logger.error(f"区域不存在: {area_id}")
                    return []

                # 递归获取所有子区域ID
                def get_all_sub_area_ids(area):
                    area_ids = [area.id]
                    for child in area.children:
                        area_ids.extend(get_all_sub_area_ids(child))
                    return area_ids

                area_ids = get_all_sub_area_ids(area)
                users = User.query.filter(User.area_id.in_(area_ids)).all()
            else:
                # 只获取当前区域的用户
                users = User.query.filter_by(area_id=area_id).all()

            # 发送通知
            user_ids = [user.id for user in users]
            return NotificationService.send_to_users(
                user_ids=user_ids,
                title=title,
                content=content,
                notification_type=notification_type,
                level=level,
                reference_id=reference_id,
                reference_type=reference_type
            )
        except Exception as e:
            logger.error(f"发送区域通知失败: {str(e)}")
            return []

    @staticmethod
    def send_to_all(title: str, content: str,
                    notification_type: str = TYPE_SYSTEM,
                    level: int = LEVEL_NORMAL,
                    reference_id: Optional[int] = None,
                    reference_type: Optional[str] = None) -> List[Notification]:
        """
        发送通知给所有用户

        Args:
            title: 通知标题
            content: 通知内容
            notification_type: 通知类型
            level: 通知级别
            reference_id: 引用ID
            reference_type: 引用类型

        Returns:
            List[Notification]: 成功创建的通知列表
        """
        try:
            users = User.query.filter_by(status=1).all()  # 只发送给活跃用户
            user_ids = [user.id for user in users]
            return NotificationService.send_to_users(
                user_ids=user_ids,
                title=title,
                content=content,
                notification_type=notification_type,
                level=level,
                reference_id=reference_id,
                reference_type=reference_type
            )
        except Exception as e:
            logger.error(f"发送全局通知失败: {str(e)}")
            return []

    @staticmethod
    def send_inventory_alert(user_id: int, ingredient_name: str, current_stock: float,
                           min_stock: float, unit: str, warehouse_name: str) -> Optional[Notification]:
        """发送库存不足预警通知"""
        title = f"库存不足预警 - {ingredient_name}"
        content = f"{warehouse_name}的{ingredient_name}库存不足，当前库存：{current_stock}{unit}，最低库存：{min_stock}{unit}，请及时补充。"

        return NotificationService.send_to_user(
            user_id=user_id,
            title=title,
            content=content,
            notification_type=NotificationService.TYPE_INVENTORY,
            level=NotificationService.LEVEL_IMPORTANT,
            reference_type='inventory_alert'
        )

    @staticmethod
    def send_expiry_alert(user_id: int, ingredient_name: str, batch_number: str,
                         expiry_date: str, days_left: int, warehouse_name: str) -> Optional[Notification]:
        """发送食材过期预警通知"""
        if days_left <= 0:
            title = f"食材已过期 - {ingredient_name}"
            content = f"{warehouse_name}的{ingredient_name}（批次：{batch_number}）已于{expiry_date}过期，请立即处理。"
            level = NotificationService.LEVEL_URGENT
        else:
            title = f"食材即将过期 - {ingredient_name}"
            content = f"{warehouse_name}的{ingredient_name}（批次：{batch_number}）将于{expiry_date}过期（还有{days_left}天），请及时使用。"
            level = NotificationService.LEVEL_IMPORTANT

        return NotificationService.send_to_user(
            user_id=user_id,
            title=title,
            content=content,
            notification_type=NotificationService.TYPE_EXPIRY,
            level=level,
            reference_type='expiry_alert'
        )

    @staticmethod
    def send_health_cert_alert(user_id: int, employee_name: str, cert_number: str,
                              expiry_date: str, days_left: int) -> Optional[Notification]:
        """发送健康证过期预警通知"""
        if days_left <= 0:
            title = f"健康证已过期 - {employee_name}"
            content = f"员工{employee_name}的健康证（证书号：{cert_number}）已于{expiry_date}过期，请立即更新。"
            level = NotificationService.LEVEL_URGENT
        else:
            title = f"健康证即将过期 - {employee_name}"
            content = f"员工{employee_name}的健康证（证书号：{cert_number}）将于{expiry_date}过期（还有{days_left}天），请及时更新。"
            level = NotificationService.LEVEL_IMPORTANT

        return NotificationService.send_to_user(
            user_id=user_id,
            title=title,
            content=content,
            notification_type=NotificationService.TYPE_HEALTH_CERT,
            level=level,
            reference_type='health_certificate'
        )

    @staticmethod
    def send_purchase_order_notification(user_id: int, order_number: str,
                                       supplier_name: str, status: str) -> Optional[Notification]:
        """发送采购订单状态通知"""
        title = f"采购订单状态更新 - {order_number}"
        content = f"采购订单{order_number}（供应商：{supplier_name}）状态已更新为：{status}"

        return NotificationService.send_to_user(
            user_id=user_id,
            title=title,
            content=content,
            notification_type=NotificationService.TYPE_PURCHASE,
            level=NotificationService.LEVEL_NORMAL,
            reference_type='purchase_order'
        )

    @staticmethod
    def send_stock_in_notification(user_id: int, stock_in_number: str,
                                 warehouse_name: str, status: str) -> Optional[Notification]:
        """发送入库单状态通知"""
        title = f"入库单状态更新 - {stock_in_number}"
        content = f"入库单{stock_in_number}（仓库：{warehouse_name}）状态已更新为：{status}"

        return NotificationService.send_to_user(
            user_id=user_id,
            title=title,
            content=content,
            notification_type=NotificationService.TYPE_STOCK_IN,
            level=NotificationService.LEVEL_NORMAL,
            reference_type='stock_in'
        )

    @staticmethod
    def send_menu_plan_notification(area_id: int, plan_date: str, meal_type: str,
                                  status: str) -> List[Notification]:
        """发送菜单计划通知"""
        title = f"菜单计划更新 - {plan_date} {meal_type}"
        content = f"{plan_date}的{meal_type}菜单计划状态已更新为：{status}"

        return NotificationService.send_to_area(
            area_id=area_id,
            title=title,
            content=content,
            notification_type=NotificationService.TYPE_MENU,
            level=NotificationService.LEVEL_NORMAL,
            reference_type='menu_plan'
        )

    @staticmethod
    def mark_as_read(notification_id: int, user_id: int) -> bool:
        """标记通知为已读"""
        try:
            notification = Notification.query.filter_by(
                id=notification_id,
                user_id=user_id
            ).first()

            if notification and not notification.is_read:
                notification.is_read = True
                db.session.commit()
                return True
            return False
        except Exception as e:
            logger.error(f"标记通知已读失败: {str(e)}")
            return False

    @staticmethod
    def delete_notification(notification_id: int, user_id: int) -> bool:
        """删除通知"""
        try:
            notification = Notification.query.filter_by(
                id=notification_id,
                user_id=user_id
            ).first()

            if notification:
                db.session.delete(notification)
                db.session.commit()
                return True
            return False
        except Exception as e:
            logger.error(f"删除通知失败: {str(e)}")
            return False

"""
DailyHealthCheck 序列化模式
"""

from marshmallow import Schema, fields, validate

class DailyHealthCheckSchema(Schema):
    """DailyHealthCheck 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    employee_id = fields.Integer(required=True)
    
    
    
    check_date = fields.Date(required=True)
    
    
    
    temperature = fields.Float(required=True)
    
    
    
    health_status = fields.String(required=True)
    
    
    
    symptoms = fields.String()
    
    
    
    checker_id = fields.Integer(required=True)
    
    
    
    notes = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    
    
    created_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
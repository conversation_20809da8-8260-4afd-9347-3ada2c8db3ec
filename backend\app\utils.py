from functools import wraps
from flask import flash, redirect, url_for, request
from flask_login import current_user
from app import db
from app.models import AuditLog
import json
from datetime import datetime

def admin_required(f):
    """
    检查用户是否是管理员的装饰器
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            return redirect(url_for('auth.login', next=request.url))

        if not current_user.is_admin():
            flash('您没有权限访问此页面', 'danger')
            return redirect(url_for('main.index'))

        return f(*args, **kwargs)
    return decorated_function

def area_required(level=None, check_area_param=0, area_id_param='id'):
    """
    检查用户是否有权限访问特定级别的区域
    level: 1:县市区, 2:乡镇, 3:学校, 4:食堂，None表示任意级别
    check_area_param: 是否检查URL参数中的区域ID
    area_id_param: URL参数中区域ID的参数名
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                return redirect(url_for('auth.login', next=request.url))

            # 系统管理员可以访问所有级别
            if current_user.is_admin():
                return f(*args, **kwargs)

            # 检查用户区域级别
            if level and current_user.area_level > level:
                flash('您没有权限访问此功能', 'danger')
                return redirect(url_for('main.index'))

            # 检查用户是否有权限访问指定区域
            if check_area_param and area_id_param in kwargs:
                area_id = kwargs[area_id_param]
                if not current_user.can_access_area_by_id(area_id):
                    flash('您没有权限访问此区域', 'danger')
                    return redirect(url_for('main.index'))

            return f(*args, **kwargs)
        return decorated_function
    return decorator

# log_activity函数已移动到app.utils.log_activity模块

def filter_by_user_area(query, model):
    """
    根据用户权限过滤查询结果
    """
    if not current_user.is_authenticated:
        return query.filter(0)  # 返回空结果

    # 系统管理员可以查看所有数据
    if current_user.is_admin():
        return query

    # 如果用户没有关联区域，则无权查看任何数据
    if not current_user.area:
        return query.filter(0)  # 返回空结果

    # 获取用户可访问的所有区域ID
    area_ids = [current_user.area_id]
    for area in current_user.area.get_descendants():
        area_ids.append(area.id)

    # 根据模型不同，使用不同的过滤条件
    if hasattr(model, 'area_id'):
        return query.filter(model.area_id.in_(area_ids))

    # 特殊处理：用户表
    if model.__name__ == 'User':
        return query.filter(model.area_id.in_(area_ids))

    # 特殊处理：员工表
    if model.__name__ == 'Employee':
        # 假设员工表有关联的区域ID
        if hasattr(model, 'area_id'):
            return query.filter(model.area_id.in_(area_ids))
        # 如果没有，则通过用户关联查询
        return query

    return query

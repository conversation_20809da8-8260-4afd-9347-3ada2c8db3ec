server {
    listen 80;
    server_name localhost;

    # 新系统前端
    location /new/ {
        alias /path/to/frontend/dist/;
        try_files $uri $uri/ /new/index.html;
    }

    # 新系统 API
    location /api/v1/ {
        proxy_pass http://localhost:5001/api/v1/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 现有系统
    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

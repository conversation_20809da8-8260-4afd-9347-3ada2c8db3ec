"""
视频引导表单
"""
from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileAllowed, FileRequired
from wtforms import StringField, TextAreaField, SelectField, HiddenField
from wtforms.validators import DataRequired, Length, Optional

class VideoGuideForm(FlaskForm):
    """视频引导表单"""
    step_name = SelectField('引导步骤', choices=[
        ('daily_management', '日常管理模块'),
        ('suppliers', '供应商管理'),
        ('ingredients_recipes', '食材食谱管理'),
        ('weekly_menu', '周菜单制定'),
        ('purchase_order', '采购订单管理'),
        ('stock_in', '食材入库管理'),
        ('consumption_plan', '消耗量计划'),
        ('stock_out', '食材出库管理'),
        ('traceability', '食材溯源管理'),
        ('food_samples', '留样记录管理')
    ], validators=[DataRequired(message='请选择引导步骤')])
    
    name = StringField('视频名称', validators=[
        DataRequired(message='请输入视频名称'),
        Length(min=1, max=128, message='视频名称长度应在1-128个字符之间')
    ])
    
    description = TextAreaField('视频描述', validators=[
        Optional(),
        Length(max=500, message='视频描述不能超过500个字符')
    ])
    
    video_file = FileField('视频文件', validators=[
        FileRequired(message='请选择视频文件'),
        FileAllowed(['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'], '只支持视频格式文件')
    ])
    
    thumbnail_file = FileField('缩略图', validators=[
        Optional(),
        FileAllowed(['jpg', 'jpeg', 'png', 'gif'], '只支持图片格式文件')
    ])

class VideoGuideEditForm(FlaskForm):
    """视频引导编辑表单"""
    id = HiddenField('ID')
    
    step_name = SelectField('引导步骤', choices=[
        ('daily_management', '日常管理模块'),
        ('suppliers', '供应商管理'),
        ('ingredients_recipes', '食材食谱管理'),
        ('weekly_menu', '周菜单制定'),
        ('purchase_order', '采购订单管理'),
        ('stock_in', '食材入库管理'),
        ('consumption_plan', '消耗量计划'),
        ('stock_out', '食材出库管理'),
        ('traceability', '食材溯源管理'),
        ('food_samples', '留样记录管理')
    ], validators=[DataRequired(message='请选择引导步骤')])
    
    name = StringField('视频名称', validators=[
        DataRequired(message='请输入视频名称'),
        Length(min=1, max=128, message='视频名称长度应在1-128个字符之间')
    ])
    
    description = TextAreaField('视频描述', validators=[
        Optional(),
        Length(max=500, message='视频描述不能超过500个字符')
    ])
    
    video_file = FileField('视频文件', validators=[
        Optional(),
        FileAllowed(['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'], '只支持视频格式文件')
    ])
    
    thumbnail_file = FileField('缩略图', validators=[
        Optional(),
        FileAllowed(['jpg', 'jpeg', 'png', 'gif'], '只支持图片格式文件')
    ])

import request from '@/utils/request'

const stockinAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/stockin',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/stockin/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/stockin',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/stockin/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/stockin/${id}`,
      method: 'delete'
    })
  }
}

export default stockinAPI
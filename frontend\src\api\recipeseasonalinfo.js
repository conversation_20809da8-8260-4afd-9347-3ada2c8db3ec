import request from '@/utils/request'

const recipeseasonalinfoAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/recipeseasonalinfo',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/recipeseasonalinfo/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/recipeseasonalinfo',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/recipeseasonalinfo/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/recipeseasonalinfo/${id}`,
      method: 'delete'
    })
  }
}

export default recipeseasonalinfoAPI
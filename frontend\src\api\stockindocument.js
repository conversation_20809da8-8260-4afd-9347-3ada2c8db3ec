import request from '@/utils/request'

const stockindocumentAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/stockindocument',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/stockindocument/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/stockindocument',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/stockindocument/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/stockindocument/${id}`,
      method: 'delete'
    })
  }
}

export default stockindocumentAPI
"""
DeliveryInspection 序列化模式
"""

from marshmallow import Schema, fields, validate

class DeliveryInspectionSchema(Schema):
    """DeliveryInspection 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    delivery_id = fields.Integer(required=True)
    
    
    
    inspector_id = fields.Integer(required=True)
    
    
    
    inspection_date = fields.String(required=True)
    
    
    
    inspection_result = fields.String(required=True)
    
    
    
    notes = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
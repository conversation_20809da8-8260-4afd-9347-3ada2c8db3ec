import request from '@/utils/request'

const userrecipefavoriteAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/userrecipefavorite',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/userrecipefavorite/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/userrecipefavorite',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/userrecipefavorite/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/userrecipefavorite/${id}`,
      method: 'delete'
    })
  }
}

export default userrecipefavoriteAPI
import request from '@/utils/request'

const inventorycheckAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/inventorycheck',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/inventorycheck/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/inventorycheck',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/inventorycheck/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/inventorycheck/${id}`,
      method: 'delete'
    })
  }
}

export default inventorycheckAPI
"""
数据库管理路由
"""

from flask import Blueprint, jsonify
from flask_login import login_required, current_user
from app import db
from app.utils import admin_required
from sqlalchemy import text, inspect

db_bp = Blueprint('db', __name__)

@db_bp.route('/create-photos-table')
@login_required
@admin_required
def create_photos_table():
    """创建 photos 表"""
    try:
        # 检查表是否已存在
        inspector = inspect(db.engine)
        existing_tables = inspector.get_table_names()
        
        if 'photos' not in existing_tables:
            # 创建 photos 表的 SQL
            sql = text("""
            CREATE TABLE [dbo].[photos](
                [id] [int] IDENTITY(1,1) NOT NULL,
                [reference_id] [int] NOT NULL,
                [reference_type] [nvarchar](20) NOT NULL,
                [file_name] [nvarchar](255) NOT NULL,
                [file_path] [nvarchar](255) NOT NULL,
                [description] [nvarchar](255) NULL,
                [rating] [int] NULL DEFAULT 3,
                [upload_time] [datetime2](1) NOT NULL DEFAULT GETDATE(),
                CONSTRAINT [PK_photos] PRIMARY KEY CLUSTERED 
                (
                    [id] ASC
                )
            )

            -- 创建索引
            CREATE NONCLUSTERED INDEX [idx_photos_reference] ON [dbo].[photos]
            (
                [reference_type] ASC,
                [reference_id] ASC
            )
            """)
            
            # 执行 SQL
            db.session.execute(sql)
            db.session.commit()
            
            return jsonify({
                'success': True,
                'message': 'photos 表创建成功'
            })
        else:
            # 检查是否需要添加 rating 字段
            columns = inspector.get_columns('photos')
            column_names = [col['name'] for col in columns]
            
            if 'rating' not in column_names:
                # 添加 rating 字段
                sql = text("""
                ALTER TABLE [dbo].[photos]
                ADD [rating] [int] NULL DEFAULT 3
                """)
                
                # 执行 SQL
                db.session.execute(sql)
                db.session.commit()
                
                return jsonify({
                    'success': True,
                    'message': 'photos 表添加 rating 字段成功'
                })
            else:
                return jsonify({
                    'success': True,
                    'message': 'photos 表已存在，无需创建'
                })
            
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'创建 photos 表失败: {str(e)}'
        }), 500

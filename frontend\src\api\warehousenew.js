import request from '@/utils/request'

const warehousenewAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/warehousenew',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/warehousenew/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/warehousenew',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/warehousenew/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/warehousenew/${id}`,
      method: 'delete'
    })
  }
}

export default warehousenewAPI
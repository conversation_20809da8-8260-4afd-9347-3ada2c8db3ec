"""
PurchaseOrder 服务层
"""

from typing import Optional, Dict, Any
from sqlalchemy.orm import Session
from app.models.models import PurchaseOrder
from app.utils.database import get_db_session

class PurchaseOrderService:
    """PurchaseOrder 服务"""

    @staticmethod
    def get_list(page: int = 1, per_page: int = 10, **filters):
        """获取PurchaseOrder列表"""
        with get_db_session() as session:
            query = session.query(PurchaseOrder)

            # 应用过滤条件
            for key, value in filters.items():
                if hasattr(PurchaseOrder, key) and value is not None:
                    query = query.filter(getattr(PurchaseOrder, key) == value)

            return query.paginate(
                page=page,
                per_page=per_page,
                error_out=False
            )

    @staticmethod
    def get_by_id(id: int) -> Optional[PurchaseOrder]:
        """根据ID获取PurchaseOrder"""
        with get_db_session() as session:
            return session.query(PurchaseOrder).filter(PurchaseOrder.id == id).first()

    @staticmethod
    def create(data: Dict[str, Any]) -> PurchaseOrder:
        """创建PurchaseOrder"""
        with get_db_session() as session:
            item = PurchaseOrder(**data)
            session.add(item)
            session.commit()
            session.refresh(item)
            return item

    @staticmethod
    def update(id: int, data: Dict[str, Any]) -> Optional[PurchaseOrder]:
        """更新PurchaseOrder"""
        with get_db_session() as session:
            item = session.query(PurchaseOrder).filter(PurchaseOrder.id == id).first()
            if not item:
                return None

            for key, value in data.items():
                if hasattr(item, key):
                    setattr(item, key, value)

            session.commit()
            session.refresh(item)
            return item

    @staticmethod
    def delete(id: int) -> bool:
        """删除PurchaseOrder"""
        with get_db_session() as session:
            item = session.query(PurchaseOrder).filter(PurchaseOrder.id == id).first()
            if not item:
                return False

            session.delete(item)
            session.commit()
            return True
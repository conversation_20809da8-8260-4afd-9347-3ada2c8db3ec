import request from '@/utils/request'

const onlineconsultationAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/onlineconsultation',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/onlineconsultation/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/onlineconsultation',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/onlineconsultation/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/onlineconsultation/${id}`,
      method: 'delete'
    })
  }
}

export default onlineconsultationAPI
import request from '@/utils/request'

const userAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/user',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/user/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/user',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/user/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/user/${id}`,
      method: 'delete'
    })
  }
}

export default userAPI
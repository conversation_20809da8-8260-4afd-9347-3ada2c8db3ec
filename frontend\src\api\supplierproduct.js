import request from '@/utils/request'

const supplierproductAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/supplierproduct',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/supplierproduct/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/supplierproduct',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/supplierproduct/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/supplierproduct/${id}`,
      method: 'delete'
    })
  }
}

export default supplierproductAPI
import request from '@/utils/request'

const batchflowAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/batchflow',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/batchflow/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/batchflow',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/batchflow/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/batchflow/${id}`,
      method: 'delete'
    })
  }
}

export default batchflowAPI
"""
SystemLog 序列化模式
"""

from marshmallow import Schema, fields, validate

class SystemLogSchema(Schema):
    """SystemLog 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    level = fields.String(required=True)
    
    
    
    module = fields.String()
    
    
    
    message = fields.String(required=True)
    
    
    
    details = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    
    
    user_id = fields.Integer()
    
    
    
    ip_address = fields.String()
    
    

    class Meta:
        ordered = True
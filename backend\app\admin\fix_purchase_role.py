"""
修复采购员角色权限和模块可见性的模块
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, current_app
from flask_login import login_required, current_user
from app import db
from app.models import Role
from app.models_visibility import ModuleVisibility
from app.utils.decorators import admin_required
import json

fix_purchase_bp = Blueprint('fix_purchase', __name__)

@fix_purchase_bp.route('/')
@login_required
@admin_required
def index():
    """显示修复采购员权限的页面"""
    return render_template('admin/fix_purchase.html')

@fix_purchase_bp.route('/fix-purchase-role')
@login_required
@admin_required
def fix_purchase_role():
    """修复采购员角色的权限和模块可见性"""
    try:
        # 1. 获取采购员角色
        purchase_role = Role.query.filter_by(name='采购员').first()
        if not purchase_role:
            flash('错误: 未找到采购员角色', 'danger')
            return redirect(url_for('system.roles'))

        role_id = purchase_role.id
        current_app.logger.info(f"找到采购员角色，ID: {role_id}")

        # 2. 添加模块可见性设置
        module_id = 'purchase'

        # 检查是否已存在可见性设置
        existing = ModuleVisibility.query.filter_by(
            module_id=module_id,
            role_id=role_id
        ).first()

        if existing:
            # 更新现有设置
            existing.is_visible = 1
            current_app.logger.info(f"更新现有可见性设置: 模块={module_id}, 角色={role_id}, 可见=1")
        else:
            # 创建新的可见性设置
            ModuleVisibility.set_visibility(
                module_id=module_id,
                role_id=role_id,
                is_visible=1
            )
            current_app.logger.info(f"创建新的可见性设置: 模块={module_id}, 角色={role_id}, 可见=1")

        # 3. 更新角色权限
        current_permissions = json.loads(purchase_role.permissions) if purchase_role.permissions else {}

        # 添加采购模块的查看和创建权限
        if 'purchase' not in current_permissions:
            current_permissions['purchase'] = []

        if 'view' not in current_permissions['purchase']:
            current_permissions['purchase'].append('view')

        if 'create' not in current_permissions['purchase']:
            current_permissions['purchase'].append('create')

        if 'edit' not in current_permissions['purchase']:
            current_permissions['purchase'].append('edit')

        # 更新角色权限
        purchase_role.permissions = json.dumps(current_permissions)

        # 4. 保存更改
        db.session.commit()

        current_app.logger.info(f"采购员角色权限已更新: {purchase_role.permissions}")

        # 5. 检查子模块可见性
        child_modules = ['purchase_order_list', 'purchase_order_create', 'supplier_list', 'supplier_product_list']
        for child_id in child_modules:
            # 检查是否已存在可见性设置
            existing = ModuleVisibility.query.filter_by(
                module_id=child_id,
                role_id=role_id
            ).first()

            if existing:
                # 更新现有设置
                existing.is_visible = 1
                current_app.logger.info(f"更新现有可见性设置: 子模块={child_id}, 角色={role_id}, 可见=1")
            else:
                # 创建新的可见性设置
                ModuleVisibility.set_visibility(
                    module_id=child_id,
                    role_id=role_id,
                    is_visible=1
                )
                current_app.logger.info(f"创建新的可见性设置: 子模块={child_id}, 角色={role_id}, 可见=1")

        # 保存子模块可见性设置
        db.session.commit()

        flash('采购员角色权限和模块可见性已成功修复', 'success')
        return redirect(url_for('system.roles'))

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"修复采购员角色时出错: {str(e)}")
        flash(f'修复采购员角色时出错: {str(e)}', 'danger')
        return redirect(url_for('system.roles'))

import request from '@/utils/request'

const notificationAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/notification',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/notification/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/notification',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/notification/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/notification/${id}`,
      method: 'delete'
    })
  }
}

export default notificationAPI
"""
CanteenTrainingRecord 序列化模式
"""

from marshmallow import Schema, fields, validate

class CanteenTrainingRecordSchema(Schema):
    """CanteenTrainingRecord 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    daily_log_id = fields.Integer(required=True)
    
    
    
    training_topic = fields.String(required=True)
    
    
    
    trainer = fields.String(required=True)
    
    
    
    training_time = fields.String(required=True)
    
    
    
    location = fields.String()
    
    
    
    duration = fields.Integer()
    
    
    
    attendees_count = fields.Integer()
    
    
    
    content_summary = fields.String()
    
    
    
    effectiveness_evaluation = fields.String()
    
    
    
    photo_paths = fields.String()
    
    
    
    created_by = fields.Integer()
    
    
    
    area_id = fields.Integer()
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
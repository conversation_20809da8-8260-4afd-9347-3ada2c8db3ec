import request from '@/utils/request'

const issueAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/issue',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/issue/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/issue',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/issue/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/issue/${id}`,
      method: 'delete'
    })
  }
}

export default issueAPI
"""
TrainingRecord 序列化模式
"""

from marshmallow import Schema, fields, validate

class TrainingRecordSchema(Schema):
    """TrainingRecord 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    employee_id = fields.Integer(required=True)
    
    
    
    training_name = fields.String(required=True)
    
    
    
    training_date = fields.Date(required=True)
    
    
    
    expire_date = fields.Date()
    
    
    
    certificate_no = fields.String()
    
    
    
    certificate_img = fields.String()
    
    
    
    score = fields.Float()
    
    
    
    trainer = fields.String()
    
    
    
    notes = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
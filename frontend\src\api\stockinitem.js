import request from '@/utils/request'

const stockinitemAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/stockinitem',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/stockinitem/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/stockinitem',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/stockinitem/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/stockinitem/${id}`,
      method: 'delete'
    })
  }
}

export default stockinitemAPI
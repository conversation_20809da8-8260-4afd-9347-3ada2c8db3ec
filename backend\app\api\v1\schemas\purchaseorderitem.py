"""
PurchaseOrderItem 序列化模式
"""

from marshmallow import Schema, fields, validate

class PurchaseOrderItemSchema(Schema):
    """PurchaseOrderItem 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    order_id = fields.Integer(required=True)
    
    
    
    product_id = fields.Integer()
    
    
    
    ingredient_id = fields.Integer(required=True)
    
    
    
    quantity = fields.Float(required=True)
    
    
    
    unit = fields.String(required=True)
    
    
    
    unit_price = fields.String(required=True)
    
    
    
    total_price = fields.String(required=True)
    
    
    
    received_quantity = fields.Float()
    
    
    
    notes = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
"""
PurchaseRequisitionItem 序列化模式
"""

from marshmallow import Schema, fields, validate

class PurchaseRequisitionItemSchema(Schema):
    """PurchaseRequisitionItem 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    requisition_id = fields.Integer(required=True)
    
    
    
    ingredient_id = fields.Integer(required=True)
    
    
    
    quantity = fields.Float(required=True)
    
    
    
    unit = fields.String(required=True)
    
    
    
    estimated_price = fields.String()
    
    
    
    total_estimated_price = fields.String()
    
    
    
    purpose = fields.String()
    
    
    
    notes = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
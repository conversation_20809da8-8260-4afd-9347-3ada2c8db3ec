"""
StandardUnit 序列化模式
"""

from marshmallow import Schema, fields, validate

class StandardUnitSchema(Schema):
    """StandardUnit 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    name = fields.String(required=True)
    
    
    
    symbol = fields.String(required=True)
    
    
    
    unit_type = fields.String(required=True)
    
    
    
    is_default = fields.Boolean(required=True)
    
    
    
    created_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
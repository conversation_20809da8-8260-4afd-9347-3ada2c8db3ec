from flask import render_template, redirect, url_for, flash, request, current_app, jsonify
from flask_login import login_required, current_user
from app import db
from app.admin import system_bp
from app.admin.forms import UserForm, RoleForm, PermissionsForm
from app.models import User, Role, UserRole, AdministrativeArea, AuditLog
from app.utils import admin_required
from app.utils.__init__ import log_activity
from app.utils.permissions import get_permission_list, parse_permissions_json, format_permissions_json, get_role_templates
from datetime import datetime
import json

@system_bp.route('/')
@login_required
@admin_required
def index():
    """系统管理首页"""
    return redirect(url_for('system.dashboard'))

@system_bp.route('/quick-access')
@login_required
@admin_required
def quick_access():
    """管理员快速访问页面"""
    return render_template('admin/quick_access.html', title='系统管理快速访问')

@system_bp.route('/users')
@login_required
@admin_required
def users():
    """用户管理"""
    page = request.args.get('page', 1, type=int)
    users = User.query.order_by(User.id.desc()).paginate(
        page=page,
        per_page=current_app.config['ITEMS_PER_PAGE'],
        error_out=0
    )

    # 获取所有角色，用于批量分配角色
    roles = Role.query.all()

    return render_template('admin/users.html', title='用户管理', users=users, roles=roles, now=datetime.now())

@system_bp.route('/users/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_user():
    """添加用户"""
    form = UserForm()

    # 获取所有角色
    roles = Role.query.all()
    form.roles.choices = [(role.id, role.name) for role in roles]

    # 获取所有区域
    areas = AdministrativeArea.query.order_by(AdministrativeArea.level, AdministrativeArea.name).all()
    area_choices = [(0, '无')]
    for area in areas:
        area_choices.append((area.id, f"{area.get_level_name()} - {area.name}"))
    form.area_id.choices = area_choices

    if form.validate_on_submit():
        # 创建用户
        user = User(
            username=form.username.data,
            email=form.email.data,
            real_name=form.real_name.data,
            phone=form.phone.data,
            status=form.status.data,
            area_id=form.area_id.data if form.area_id.data > 0 else None,
            area_level=form.area_level.data if form.area_id.data > 0 else None
        )

        # 设置密码
        if form.password.data:
            user.set_password(form.password.data)
        else:
            # 默认密码为123456
            user.set_password('123456')

        db.session.add(user)
        db.session.commit()

        # 添加角色
        for role_id in form.roles.data:
            user_role = UserRole(user_id=user.id, role_id=role_id)
            db.session.add(user_role)

        db.session.commit()

        # 记录审计日志
        log_activity(
            action='create',
            resource_type='user',
            resource_id=user.id,
            details={
                'user': user.to_dict()
            }
        )

        flash(f'用户 {user.username} 添加成功', 'success')
        return redirect(url_for('system.users'))

    return render_template('admin/user_form.html', title='添加用户', form=form, now=datetime.now())

@system_bp.route('/users/edit/<int:id>', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_user(id):
    """编辑用户"""
    user = User.query.get_or_404(id)
    form = UserForm(obj=user)
    form.user_id = id

    # 获取所有角色
    roles = Role.query.all()
    form.roles.choices = [(role.id, role.name) for role in roles]

    # 获取所有区域
    areas = AdministrativeArea.query.order_by(AdministrativeArea.level, AdministrativeArea.name).all()
    area_choices = [(0, '无')]
    for area in areas:
        area_choices.append((area.id, f"{area.get_level_name()} - {area.name}"))
    form.area_id.choices = area_choices

    if request.method == 'GET':
        # 设置默认值
        form.roles.data = [role.id for role in user.roles]
        form.area_id.data = user.area_id if user.area_id else 0

    if form.validate_on_submit():
        # 保存旧数据用于记录历史
        old_data = user.to_dict()

        # 更新用户信息
        user.username = form.username.data
        user.email = form.email.data
        user.real_name = form.real_name.data
        user.phone = form.phone.data
        user.status = form.status.data
        user.area_id = form.area_id.data if form.area_id.data > 0 else None
        user.area_level = form.area_level.data if form.area_id.data > 0 else None

        # 如果提供了新密码，则更新密码
        if form.password.data:
            user.set_password(form.password.data)

        # 使用事务确保角色更新的原子性
        try:
            # 开始事务
            db.session.begin_nested()

            # 先删除所有现有角色
            UserRole.query.filter_by(user_id=user.id).delete()

            # 添加新角色
            for role_id in form.roles.data:
                user_role = UserRole(user_id=user.id, role_id=role_id)
                db.session.add(user_role)

            # 提交事务
            db.session.commit()
        except Exception as e:
            # 回滚事务
            db.session.rollback()
            flash(f'更新用户角色失败: {str(e)}', 'danger')
            current_app.logger.error(f'更新用户角色失败: {str(e)}')
            return render_template('admin/user_form.html', title=f'编辑用户 - {user.username}', form=form, user=user, now=datetime.now())

        # 记录审计日志
        log_activity(
            action='update',
            resource_type='user',
            resource_id=user.id,
            details={
                'old': old_data,
                'new': user.to_dict()
            }
        )

        flash(f'用户 {user.username} 更新成功', 'success')
        return redirect(url_for('system.users'))

    return render_template('admin/user_form.html', title=f'编辑用户 - {user.username}', form=form, user=user, now=datetime.now())

@system_bp.route('/users/view/<int:id>')
@login_required
@admin_required
def view_user(id):
    """查看用户详情"""
    user = User.query.get_or_404(id)

    # 获取用户的审计日志
    logs = AuditLog.query.filter_by(user_id=user.id).order_by(AuditLog.created_at.desc()).limit(20).all()

    return render_template('admin/view_user.html', title=f'用户详情 - {user.username}', user=user, logs=logs, now=datetime.now())

@system_bp.route('/roles')
@login_required
@admin_required
def roles():
    """角色管理"""
    roles = Role.query.all()
    return render_template('admin/roles.html', title='角色管理', roles=roles, now=datetime.now())

@system_bp.route('/roles/templates')
@login_required
@admin_required
def role_templates():
    """角色模板选择"""
    templates = get_role_templates()
    return render_template('admin/role_templates.html', title='选择角色模板', templates=templates, now=datetime.now())

@system_bp.route('/roles/create_from_template/<template_key>')
@login_required
@admin_required
def create_role_from_template(template_key):
    """从模板创建角色"""
    templates = get_role_templates()

    if template_key not in templates:
        flash('无效的角色模板', 'danger')
        return redirect(url_for('system.role_templates'))

    template = templates[template_key]

    # 创建表单并预填充模板数据
    form = RoleForm()
    form.name.data = template['name']
    form.description.data = template['description']
    form.permissions.data = format_permissions_json(template['permissions'])

    return render_template('admin/role_form.html', title='从模板创建角色', form=form, template=template, now=datetime.now())

@system_bp.route('/roles/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_role():
    """添加角色"""
    form = RoleForm()

    if form.validate_on_submit():
        # 创建角色
        role = Role(
            name=form.name.data,
            description=form.description.data,
            permissions=form.permissions.data if form.permissions.data else '{"*": ["*"]}'  # 默认拥有所有权限
        )
        db.session.add(role)
        db.session.commit()

        flash(f'角色 {role.name} 添加成功', 'success')
        return redirect(url_for('system.roles'))

    return render_template('admin/role_form.html', title='添加角色', form=form, now=datetime.now())

@system_bp.route('/roles/edit/<int:id>', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_role(id):
    """编辑角色"""
    role = Role.query.get_or_404(id)
    form = RoleForm(obj=role)
    form.role_id = id

    if form.validate_on_submit():
        # 更新角色信息
        role.name = form.name.data
        role.description = form.description.data
        if form.permissions.data:
            role.permissions = form.permissions.data

        db.session.commit()

        flash(f'角色 {role.name} 更新成功', 'success')
        return redirect(url_for('system.roles'))

    return render_template('admin/role_form.html', title=f'编辑角色 - {role.name}', form=form, role=role, now=datetime.now())

@system_bp.route('/roles/view/<int:id>')
@login_required
@admin_required
def view_role(id):
    """查看角色详情"""
    role = Role.query.get_or_404(id)

    # 获取关联此角色的用户
    users = User.query.join(UserRole).filter(UserRole.role_id == id).all()

    # 解析权限
    permissions = parse_permissions_json(role.permissions)

    return render_template('admin/view_role.html', title=f'角色详情 - {role.name}', role=role, users=users, permissions=permissions, now=datetime.now())

@system_bp.route('/roles/permissions/help')
@login_required
@admin_required
def permission_help():
    """权限配置帮助页面"""
    return render_template('admin/permission_help.html', title='权限配置帮助', now=datetime.now())

@system_bp.route('/roles/permissions/<int:id>', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_role_permissions(id):
    """编辑角色权限"""
    role = Role.query.get_or_404(id)
    form = PermissionsForm()

    # 获取所有权限
    all_permissions = get_permission_list()

    # 解析当前角色的权限
    current_permissions = parse_permissions_json(role.permissions)

    if request.method == 'POST' and form.validate_on_submit():
        try:
            # 处理表单提交
            permissions_data = {}

            # 获取所有选中的权限
            selected_permissions = request.form.getlist('permissions')

            # 解析权限格式 "module:action"
            for perm in selected_permissions:
                try:
                    module, action = perm.split(':')
                    if module not in permissions_data:
                        permissions_data[module] = []
                    permissions_data[module].append(action)
                except ValueError:
                    current_app.logger.warning(f"无效的权限格式: {perm}")
                    continue

            # 确保采购管理模块的权限存在
            if 'purchase' not in permissions_data:
                permissions_data['purchase'] = []

            # 确保采购管理模块的基本权限
            basic_permissions = ['view', 'create', 'edit']
            for perm in basic_permissions:
                if perm not in permissions_data['purchase']:
                    permissions_data['purchase'].append(perm)

            # 确保采购订单管理模块的权限存在
            if 'purchase_order' not in permissions_data:
                permissions_data['purchase_order'] = []

            # 确保采购订单管理模块的基本权限
            for perm in basic_permissions:
                if perm not in permissions_data['purchase_order']:
                    permissions_data['purchase_order'].append(perm)

            # 更新角色权限
            permissions_json = format_permissions_json(permissions_data)

            # 使用原始SQL更新角色权限，避免精度问题
            from sqlalchemy import text
            sql = text("""
            UPDATE roles
            SET permissions = :permissions
            WHERE id = :id
            """)

            db.session.execute(
                sql,
                {
                    'permissions': permissions_json,
                    'id': role.id
                }
            )
            db.session.commit()

            flash(f'角色 {role.name} 的权限已更新', 'success')
            return redirect(url_for('system.view_role', id=role.id))
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"更新角色权限时出错: {str(e)}")
            flash(f'更新角色权限时出错: {str(e)}', 'danger')
            return redirect(url_for('system.edit_role_permissions', id=id))

    return render_template(
        'admin/role_permissions.html',
        title=f'编辑权限 - {role.name}',
        role=role,
        form=form,
        permissions=all_permissions,
        current_permissions=current_permissions,
        now=datetime.now()
    )

@system_bp.route('/roles/delete/<int:id>', methods=['POST'])
@login_required
@admin_required
def delete_role(id):
    """删除角色"""
    role = Role.query.get_or_404(id)

    # 检查是否有用户关联此角色
    if role.users.count() > 0:
        flash(f'无法删除角色 {role.name}，因为有 {role.users.count()} 个用户关联此角色', 'danger')
        return redirect(url_for('system.roles'))

    # 检查是否是系统预设角色
    if role.name in ['系统管理员', '管理员', '超级管理员']:
        flash(f'无法删除系统预设角色 {role.name}', 'danger')
        return redirect(url_for('system.roles'))

    # 保存角色名称用于消息提示
    role_name = role.name

    try:
        # 删除角色
        db.session.delete(role)
        db.session.commit()

        flash(f'角色 {role_name} 已成功删除', 'success')
        return redirect(url_for('system.roles'))
    except Exception as e:
        db.session.rollback()
        flash(f'删除角色失败: {str(e)}', 'danger')
        return redirect(url_for('system.roles'))

@system_bp.route('/users/permissions/<int:id>', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_user_permissions(id):
    """编辑用户权限"""
    user = User.query.get_or_404(id)

    # 检查当前用户是否有权限编辑此用户
    # 系统管理员可以编辑任何用户
    # 其他用户只能编辑自己区域下级的用户
    if not current_user.is_admin():
        if not user.area or not current_user.area:
            flash('您没有权限编辑此用户的权限', 'danger')
            return redirect(url_for('system.view_user', id=id))

        # 检查用户是否是当前用户区域的下级
        if not current_user.can_access_area(user.area):
            flash('您只能编辑您所在区域及下级区域的用户权限', 'danger')
            return redirect(url_for('system.view_user', id=id))

    # 获取所有角色
    all_roles = Role.query.all()

    # 如果不是系统管理员，过滤掉系统管理员角色
    if not current_user.is_admin():
        all_roles = [role for role in all_roles if role.name != '系统管理员']

    # 获取用户当前的角色
    current_role_ids = [role.id for role in user.roles]

    if request.method == 'POST':
        # 获取选中的角色
        selected_role_ids = [int(role_id) for role_id in request.form.getlist('roles')]

        # 如果不是系统管理员，确保不会添加系统管理员角色
        if not current_user.is_admin():
            admin_role = Role.query.filter_by(name='系统管理员').first()
            if admin_role and admin_role.id in selected_role_ids:
                flash('您没有权限分配系统管理员角色', 'danger')
                return redirect(url_for('system.edit_user_permissions', id=id))

        # 准备更新用户权限

        # 获取旧的角色名称
        old_role_names = [role.name for role in user.roles]
        new_role_names = [Role.query.get(role_id).name for role_id in selected_role_ids]

        try:
            # 开始事务
            db.session.begin_nested()

            # 先删除所有现有角色
            UserRole.query.filter_by(user_id=user.id).delete()

            # 添加新角色
            for role_id in selected_role_ids:
                user_role = UserRole(user_id=user.id, role_id=role_id)
                db.session.add(user_role)

            # 提交事务
            db.session.commit()

            # 记录审计日志
            log_activity(
                action='update_permissions',
                resource_type='user',
                resource_id=user.id,
                details={
                    'old': {'roles': old_role_names},
                    'new': {'roles': new_role_names}
                }
            )

            flash(f'用户 {user.username} 的权限已更新', 'success')
            return redirect(url_for('system.view_user', id=id))

        except Exception as e:
            # 回滚事务
            db.session.rollback()
            flash(f'更新用户权限失败: {str(e)}', 'danger')
            current_app.logger.error(f'更新用户权限失败: {str(e)}')

    return render_template(
        'admin/user_permissions.html',
        title=f'编辑权限 - {user.username}',
        user=user,
        roles=all_roles,
        current_role_ids=current_role_ids,
        now=datetime.now()
    )

@system_bp.route('/users/delete/<int:id>', methods=['POST'])
@login_required
@admin_required
def delete_user(id):
    """删除用户"""
    user = User.query.get_or_404(id)

    # 检查是否是系统管理员
    if user.is_admin() and not current_user.is_admin():
        flash('您没有权限删除系统管理员', 'danger')
        return redirect(url_for('system.view_user', id=id))

    # 检查是否是当前用户
    if user.id == current_user.id:
        flash('您不能删除自己的账户', 'danger')
        return redirect(url_for('system.view_user', id=id))

    # 保存用户名用于消息提示
    username = user.username

    try:
        # 开始事务
        db.session.begin_nested()

        # 删除用户角色关联
        UserRole.query.filter_by(user_id=user.id).delete()

        # 记录审计日志
        log_activity(
            action='delete',
            resource_type='user',
            resource_id=user.id,
            details={
                'user': user.to_dict()
            }
        )

        # 删除用户
        db.session.delete(user)
        db.session.commit()

        flash(f'用户 {username} 已成功删除', 'success')
        return redirect(url_for('system.users'))
    except Exception as e:
        db.session.rollback()
        flash(f'删除用户失败: {str(e)}', 'danger')
        current_app.logger.error(f'删除用户失败: {str(e)}')
        return redirect(url_for('system.view_user', id=id))

@system_bp.route('/users/batch_delete', methods=['POST'])
@login_required
@admin_required
def batch_delete_users():
    """批量删除用户"""
    user_ids_str = request.form.get('user_ids', '')
    if not user_ids_str:
        flash('未选择任何用户', 'danger')
        return redirect(url_for('system.users'))

    user_ids = [int(id) for id in user_ids_str.split(',')]

    # 获取要删除的用户
    users = User.query.filter(User.id.in_(user_ids)).all()

    # 检查是否包含系统管理员
    if not current_user.is_admin() and any(user.is_admin() for user in users):
        flash('您没有权限删除系统管理员', 'danger')
        return redirect(url_for('system.users'))

    # 检查是否包含当前用户
    if current_user.id in user_ids:
        flash('您不能删除自己的账户', 'danger')
        return redirect(url_for('system.users'))

    deleted_count = 0
    error_count = 0

    for user in users:
        try:
            # 开始事务
            db.session.begin_nested()

            # 删除用户角色关联
            UserRole.query.filter_by(user_id=user.id).delete()

            # 记录审计日志
            log_activity(
                action='delete',
                resource_type='user',
                resource_id=user.id,
                details={
                    'user': user.to_dict()
                }
            )

            # 删除用户
            db.session.delete(user)
            db.session.commit()

            deleted_count += 1
        except Exception as e:
            db.session.rollback()
            error_count += 1
            current_app.logger.error(f'删除用户 {user.username} 失败: {str(e)}')

    if deleted_count > 0:
        flash(f'成功删除 {deleted_count} 个用户', 'success')

    if error_count > 0:
        flash(f'有 {error_count} 个用户删除失败，详情请查看日志', 'warning')

    return redirect(url_for('system.users'))

@system_bp.route('/users/batch_assign_roles', methods=['POST'])
@login_required
@admin_required
def batch_assign_roles():
    """批量分配角色"""
    user_ids_str = request.form.get('user_ids', '')
    role_ids_str = request.form.get('role_ids', '')
    role_action = request.form.get('role_action', 'add')

    if not user_ids_str or not role_ids_str:
        flash('未选择用户或角色', 'danger')
        return redirect(url_for('system.users'))

    user_ids = [int(id) for id in user_ids_str.split(',')]
    role_ids = [int(id) for id in role_ids_str.split(',')]

    # 获取要操作的用户
    users = User.query.filter(User.id.in_(user_ids)).all()
    # Verify that all role IDs exist
    Role.query.filter(Role.id.in_(role_ids)).all()

    # 检查是否包含系统管理员角色
    admin_role = Role.query.filter_by(name='系统管理员').first()
    if not current_user.is_admin() and admin_role and admin_role.id in role_ids:
        flash('您没有权限分配系统管理员角色', 'danger')
        return redirect(url_for('system.users'))

    success_count = 0
    error_count = 0

    for user in users:
        try:
            # 开始事务
            db.session.begin_nested()

            # 获取用户当前的角色
            current_role_ids = [role.id for role in user.roles]

            # 根据操作方式处理角色
            if role_action == 'replace':
                # 替换模式：删除所有现有角色，添加新角色
                UserRole.query.filter_by(user_id=user.id).delete()
                new_role_ids = role_ids
            else:
                # 添加模式：保留现有角色，添加新角色
                new_role_ids = list(set(current_role_ids + role_ids))
                # 先删除所有现有角色
                UserRole.query.filter_by(user_id=user.id).delete()

            # 添加角色
            for role_id in new_role_ids:
                user_role = UserRole(user_id=user.id, role_id=role_id)
                db.session.add(user_role)

            # 记录审计日志
            log_activity(
                action='update_roles',
                resource_type='user',
                resource_id=user.id,
                details={
                    'old_role_ids': current_role_ids,
                    'new_role_ids': new_role_ids,
                    'action': role_action
                }
            )

            db.session.commit()
            success_count += 1
        except Exception as e:
            db.session.rollback()
            error_count += 1
            current_app.logger.error(f'为用户 {user.username} 分配角色失败: {str(e)}')

    if success_count > 0:
        flash(f'成功为 {success_count} 个用户分配角色', 'success')

    if error_count > 0:
        flash(f'有 {error_count} 个用户分配角色失败，详情请查看日志', 'warning')

    return redirect(url_for('system.users'))

@system_bp.route('/get_area_info/<int:area_id>')
@login_required
def get_area_info(area_id):
    """获取区域信息（用于AJAX请求）"""
    area = AdministrativeArea.query.get_or_404(area_id)
    return jsonify({
        'id': area.id,
        'name': area.name,
        'level': area.level,
        'level_name': area.get_level_name(),
        'parent_id': area.parent_id
    })

"""
VideoGuide 服务层
"""

from typing import Optional, Dict, Any
from sqlalchemy.orm import Session
from app.models.models import VideoGuide
from app.utils.database import get_db_session

class VideoGuideService:
    """VideoGuide 服务"""

    @staticmethod
    def get_list(page: int = 1, per_page: int = 10, **filters):
        """获取VideoGuide列表"""
        with get_db_session() as session:
            query = session.query(VideoGuide)

            # 应用过滤条件
            for key, value in filters.items():
                if hasattr(VideoGuide, key) and value is not None:
                    query = query.filter(getattr(VideoGuide, key) == value)

            return query.paginate(
                page=page,
                per_page=per_page,
                error_out=False
            )

    @staticmethod
    def get_by_id(id: int) -> Optional[VideoGuide]:
        """根据ID获取VideoGuide"""
        with get_db_session() as session:
            return session.query(VideoGuide).filter(VideoGuide.id == id).first()

    @staticmethod
    def create(data: Dict[str, Any]) -> VideoGuide:
        """创建VideoGuide"""
        with get_db_session() as session:
            item = VideoGuide(**data)
            session.add(item)
            session.commit()
            session.refresh(item)
            return item

    @staticmethod
    def update(id: int, data: Dict[str, Any]) -> Optional[VideoGuide]:
        """更新VideoGuide"""
        with get_db_session() as session:
            item = session.query(VideoGuide).filter(VideoGuide.id == id).first()
            if not item:
                return None

            for key, value in data.items():
                if hasattr(item, key):
                    setattr(item, key, value)

            session.commit()
            session.refresh(item)
            return item

    @staticmethod
    def delete(id: int) -> bool:
        """删除VideoGuide"""
        with get_db_session() as session:
            item = session.query(VideoGuide).filter(VideoGuide.id == id).first()
            if not item:
                return False

            session.delete(item)
            session.commit()
            return True
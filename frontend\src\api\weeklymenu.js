import request from '@/utils/request'

const weeklymenuAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/weeklymenu',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/weeklymenu/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/weeklymenu',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/weeklymenu/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/weeklymenu/${id}`,
      method: 'delete'
    })
  }
}

export default weeklymenuAPI
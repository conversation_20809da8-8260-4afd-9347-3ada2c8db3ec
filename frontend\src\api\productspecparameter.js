import request from '@/utils/request'

const productspecparameterAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/productspecparameter',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/productspecparameter/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/productspecparameter',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/productspecparameter/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/productspecparameter/${id}`,
      method: 'delete'
    })
  }
}

export default productspecparameterAPI
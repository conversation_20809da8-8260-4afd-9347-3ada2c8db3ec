import request from '@/utils/request'

const dailylogAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/dailylog',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/dailylog/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/dailylog',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/dailylog/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/dailylog/${id}`,
      method: 'delete'
    })
  }
}

export default dailylogAPI
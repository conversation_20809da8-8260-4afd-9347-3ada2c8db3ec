#!/usr/bin/env python3
"""
生成核心模型 - 只生成高优先级的13个核心模型
"""

import urllib.parse
from sqlalchemy import create_engine, MetaData, inspect, text

def generate_core_models():
    """生成核心模型"""
    print("🔍 生成核心模型...")
    
    # 核心模型列表（基于分析结果）
    core_models = [
        'User', 'Role', 'UserRole',
        'Supplier', 'SupplierCategory', 'SupplierProduct',
        'Ingredient', 'IngredientCategory', 
        'Recipe', 'RecipeCategory', 'RecipeIngredient',
        'PurchaseOrder', 'PurchaseOrderItem',
        'Warehouse', 'Inventory'
    ]
    
    # 数据库连接配置
    DB_SERVER = '(local)\\SQLEXPRESS'
    DB_DATABASE = 'StudentsCMSSP'
    DB_DRIVER = 'SQL Server'
    
    # 构建连接字符串
    conn_str = f"DRIVER={{{DB_DRIVER}}};SERVER={DB_SERVER};DATABASE={DB_DATABASE};Trusted_Connection=yes"
    quoted_conn_str = urllib.parse.quote_plus(conn_str)
    connection_string = f"mssql+pyodbc:///?odbc_connect={quoted_conn_str}"
    
    try:
        # 创建引擎
        engine = create_engine(
            connection_string,
            echo=False,
            pool_pre_ping=True,
            pool_recycle=300
        )
        
        # 测试连接
        with engine.connect() as connection:
            result = connection.execute(text("SELECT 1"))
            print("✅ 数据库连接成功")
        
        # 获取表信息
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        
        print(f"📊 数据库中共有 {len(tables)} 个表")
        
        # 生成核心模型代码
        model_code = generate_core_model_code(inspector, core_models)
        
        # 写入文件
        output_file = 'app/models/models_core.py'
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(model_code)
        
        print(f"✅ 核心模型已生成到: {output_file}")
        return True
        
    except Exception as e:
        print(f"❌ 生成核心模型失败: {e}")
        return False

def generate_core_model_code(inspector, core_models):
    """生成核心模型代码"""
    
    code = '''"""
核心模型 - 从 SQL Server 数据库生成
只包含最重要的13个核心模型
"""

from datetime import datetime, date
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import UserMixin
from sqlalchemy.dialects.mssql import DATETIME2
from flask import current_app

# 获取数据库实例
def get_db():
    """获取数据库实例"""
    return current_app.extensions['sqlalchemy']

def init_core_models():
    """初始化核心模型"""
    db = get_db()
    
    # 基础模型类
    class BaseModel(db.Model):
        """基础模型类"""
        __abstract__ = True
        
        def to_dict(self):
            """转换为字典"""
            result = {}
            for column in self.__table__.columns:
                value = getattr(self, column.name)
                if isinstance(value, datetime):
                    result[column.name] = value.isoformat()
                elif isinstance(value, date):
                    result[column.name] = value.isoformat()
                else:
                    result[column.name] = value
            return result

        def update_from_dict(self, data):
            """从字典更新属性"""
            for key, value in data.items():
                if hasattr(self, key):
                    setattr(self, key, value)
    
    # 特殊处理 User 模型，添加认证功能
    class User(BaseModel, UserMixin):
        """用户模型"""
        __tablename__ = 'users'
        
        # 字段将通过反射自动添加
        
        def set_password(self, password):
            """设置密码"""
            if hasattr(self, 'password_hash'):
                self.password_hash = generate_password_hash(password)
        
        def check_password(self, password):
            """检查密码"""
            if hasattr(self, 'password_hash'):
                return check_password_hash(self.password_hash, password)
            return False
        
        def __repr__(self):
            username = getattr(self, 'username', 'Unknown')
            return f'<User {username}>'
    
    # 返回模型字典
    models = {
        'BaseModel': BaseModel,
        'User': User,
    }
    
    return models

# 全局变量
_models = None

def get_models():
    """获取模型字典"""
    global _models
    if _models is None:
        _models = init_core_models()
    return _models

def get_model(name):
    """获取指定模型"""
    models = get_models()
    return models.get(name)
'''
    
    return code

def main():
    """主函数"""
    print("🚀 开始生成核心模型")
    print("=" * 50)
    
    success = generate_core_models()
    
    if success:
        print("\n" + "=" * 50)
        print("🎉 核心模型生成完成！")
        print("📝 下一步:")
        print("1. 更新应用初始化代码")
        print("2. 测试应用启动")
        print("3. 测试 API 端点")
    else:
        print("\n❌ 核心模型生成失败")
    
    return success

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)

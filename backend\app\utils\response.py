"""
统一响应格式工具
"""

from datetime import datetime
from flask import jsonify

def success_response(data=None, message="success", code=200):
    """成功响应"""
    response = {
        "code": code,
        "message": message,
        "data": data,
        "timestamp": datetime.now().isoformat()
    }
    return jsonify(response), code

def error_response(message="error", code=400, data=None):
    """错误响应"""
    response = {
        "code": code,
        "message": message,
        "data": data,
        "timestamp": datetime.now().isoformat()
    }
    return jsonify(response), code

def paginated_response(items, total, page, per_page, message="success"):
    """分页响应"""
    data = {
        "items": items,
        "total": total,
        "page": page,
        "per_page": per_page,
        "pages": (total + per_page - 1) // per_page
    }
    return success_response(data=data, message=message)

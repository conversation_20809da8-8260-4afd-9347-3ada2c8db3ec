"""
SupplierProduct 序列化模式
"""

from marshmallow import Schema, fields, validate

class SupplierProductSchema(Schema):
    """SupplierProduct 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    supplier_id = fields.Integer(required=True)
    
    
    
    ingredient_id = fields.Integer(required=True)
    
    
    
    product_code = fields.String()
    
    
    
    product_name = fields.String()
    
    
    
    model_number = fields.String()
    
    
    
    specification = fields.String()
    
    
    
    price = fields.String(required=True)
    
    
    
    quality_cert = fields.String(required=True)
    
    
    
    quality_standard = fields.String()
    
    
    
    product_image = fields.String()
    
    
    
    lead_time = fields.Integer()
    
    
    
    min_order_quantity = fields.Float()
    
    
    
    is_available = fields.Integer(required=True)
    
    
    
    shelf_status = fields.Integer(required=True)
    
    
    
    shelf_time = fields.String()
    
    
    
    shelf_operator_id = fields.Integer()
    
    
    
    description = fields.String()
    
    
    
    batch_id = fields.Integer()
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
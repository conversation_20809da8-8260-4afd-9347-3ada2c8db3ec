#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户引导服务
为新用户提供分步骤的系统使用引导
"""

from flask import session, url_for
from datetime import datetime, date
import json
# from .video_guide_service import VideoGuideService  # 暂时注释掉
from .scenario_guide_service import ScenarioGuideService

class UserGuideService:
    """用户引导服务类"""

    # 引导步骤定义
    GUIDE_STEPS = {
        'welcome': {
            'title': '欢迎使用校园餐智慧食堂平台',
            'description': '让我们一起了解食堂管理的完整流程',
            'order': 0
        },
        'daily_management': {
            'title': '食堂日常管理模块',
            'description': '了解6个日常管理子功能，体验二维码检查和PDF打印',
            'order': 1
        },
        'suppliers': {
            'title': '供应商管理',
            'description': '建立供应商档案，为采购做准备',
            'order': 2
        },
        'ingredients_recipes': {
            'title': '食材食谱管理',
            'description': '添加食材和食谱，建立菜品数据库',
            'order': 3
        },
        'weekly_menu': {
            'title': '周菜单计划',
            'description': '制定周菜单，安排每日餐食',
            'order': 4
        },
        'purchase_order': {
            'title': '采购订单',
            'description': '从周菜单生成采购单，规范采购流程',
            'order': 5
        },
        'stock_in': {
            'title': '食材入库',
            'description': '记录入库信息，建立溯源档案',
            'order': 6
        },
        'consumption_plan': {
            'title': '消耗量计划',
            'description': '制定食材使用计划，精确控制用量',
            'order': 7
        },
        'stock_out': {
            'title': '食材出库',
            'description': '记录出库信息，完善溯源链条',
            'order': 8
        },
        'traceability': {
            'title': '食材溯源',
            'description': '实现从采购到餐桌的全程追溯',
            'order': 9
        },
        'food_samples': {
            'title': '留样记录',
            'description': '一键生成留样记录，确保食品安全',
            'order': 10
        },
        'completed': {
            'title': '引导完成',
            'description': '恭喜您已掌握完整的食堂管理流程',
            'order': 11
        }
    }

    @staticmethod
    def get_user_guide_status(user_id):
        """获取用户引导状态"""
        guide_key = f'user_guide_{user_id}'
        guide_data = session.get(guide_key, {})

        return {
            'current_step': guide_data.get('current_step', 'welcome'),
            'completed_steps': guide_data.get('completed_steps', []),
            'started_at': guide_data.get('started_at'),
            'is_active': guide_data.get('is_active', True)
        }

    @staticmethod
    def start_guide(user_id, school_type=None, user_preferences=None):
        """开始用户引导"""
        guide_key = f'user_guide_{user_id}'

        # 创建个性化引导计划
        if school_type:
            guide_plan = ScenarioGuideService.create_personalized_guide_plan(school_type, user_preferences)
        else:
            guide_plan = None

        session[guide_key] = {
            'current_step': 'welcome',
            'completed_steps': [],
            'started_at': datetime.now().isoformat(),
            'is_active': True,
            'school_type': school_type,
            'guide_plan': guide_plan,
            'user_preferences': user_preferences or {}
        }
        return True

    @staticmethod
    def complete_step(user_id, step_name):
        """完成一个引导步骤"""
        guide_key = f'user_guide_{user_id}'
        guide_data = session.get(guide_key, {})

        if step_name not in guide_data.get('completed_steps', []):
            guide_data.setdefault('completed_steps', []).append(step_name)

        # 获取下一步
        next_step = UserGuideService._get_next_step(step_name)
        guide_data['current_step'] = next_step

        session[guide_key] = guide_data
        return next_step

    @staticmethod
    def _get_next_step(current_step):
        """获取下一个步骤"""
        current_order = UserGuideService.GUIDE_STEPS.get(current_step, {}).get('order', 0)

        for step_name, step_info in UserGuideService.GUIDE_STEPS.items():
            if step_info['order'] == current_order + 1:
                return step_name

        return 'completed'

    @staticmethod
    def skip_guide(user_id):
        """跳过引导"""
        guide_key = f'user_guide_{user_id}'
        guide_data = session.get(guide_key, {})
        guide_data['is_active'] = False
        session[guide_key] = guide_data
        return True

    @staticmethod
    def reset_guide(user_id):
        """重置用户引导状态"""
        try:
            guide_key = f'user_guide_{user_id}'
            # 清除session中的引导数据
            if guide_key in session:
                del session[guide_key]
            return True
        except RuntimeError:
            # 如果没有请求上下文，直接返回成功
            # 因为引导状态存储在session中，没有session就相当于已经重置了
            return True

    @staticmethod
    def get_step_info(step_name):
        """获取步骤信息"""
        return UserGuideService.GUIDE_STEPS.get(step_name, {})

    @staticmethod
    def get_step_content(step_name, user_id=None):
        """获取步骤详细内容"""
        # 获取用户的引导状态
        guide_status = UserGuideService.get_user_guide_status(user_id) if user_id else {}
        school_type = guide_status.get('school_type')

        # 获取场景化定制内容
        customized_content = {}
        if school_type:
            customized_content = ScenarioGuideService.get_customized_step_content(school_type, step_name)

        # 获取视频资源
        video_resources = []  # 暂时返回空列表

        content_map = {
            'welcome': {
                'features': [
                    '完整的食堂管理工作流程',
                    '从采购到餐桌的全程追溯',
                    '专业的PDF报表生成',
                    '移动端二维码检查',
                    '智能化数据分析'
                ],
                'next_action': '开始了解日常管理',
                'next_url': 'daily_management.index'
            },
            'daily_management': {
                'modules': [
                    {'name': '检查记录', 'icon': 'fa-search', 'desc': '食品安全检查，支持二维码扫描上传'},
                    {'name': '陪餐记录', 'icon': 'fa-users', 'desc': '陪餐人员记录，生成陪餐报告'},
                    {'name': '培训记录', 'icon': 'fa-graduation-cap', 'desc': '员工培训档案管理'},
                    {'name': '特殊事件', 'icon': 'fa-exclamation-triangle', 'desc': '突发事件记录处理'},
                    {'name': '问题记录', 'icon': 'fa-bug', 'desc': '问题发现与整改跟踪'},
                    {'name': '工作日志', 'icon': 'fa-calendar-check', 'desc': '日常工作记录，生成工作报告'}
                ],
                'highlights': [
                    '生成学校专属二维码，员工扫码上传检查数据',
                    '一键生成PDF报告，方便部门检查',
                    '整合相关资料，形成完整档案'
                ],
                'next_action': '了解供应商管理',
                'next_url': 'supplier.index'
            },
            'suppliers': {
                'importance': [
                    '建立合格供应商档案',
                    '确保食材来源可靠',
                    '规范采购流程',
                    '建立供应商评价体系'
                ],
                'demo_data': {
                    'name': '绿色农场有限公司',
                    'contact': '张经理',
                    'phone': '***********',
                    'products': ['新鲜蔬菜', '有机水果', '绿色大米']
                },
                'next_action': '添加食材和食谱',
                'next_url': 'ingredient.index'
            }
            # 其他步骤内容...
        }

        return content_map.get(step_name, {})

    @staticmethod
    def generate_demo_data(step_name, user_area_id):
        """为指定步骤生成演示数据"""
        if step_name == 'suppliers':
            return UserGuideService._create_demo_supplier(user_area_id)
        elif step_name == 'ingredients_recipes':
            return UserGuideService._create_demo_ingredients(user_area_id)
        elif step_name == 'stock_in':
            return UserGuideService._create_demo_stock_in(user_area_id)
        # 其他演示数据生成...

        return None

    @staticmethod
    def _create_demo_supplier(area_id):
        """创建演示供应商数据"""
        from app.models import Supplier, SupplierCategory
        from app import db
        from sqlalchemy import text
        from datetime import datetime, date

        try:
            # 检查是否已存在演示供应商分类
            existing_category = db.session.execute(text('''
                SELECT id FROM supplier_categories WHERE name = :name
            '''), {'name': '蔬菜供应商'}).scalar()

            if existing_category:
                category_id = existing_category
            else:
                # 创建供应商分类
                sql = text('''
                INSERT INTO supplier_categories (name, description, created_at)
                OUTPUT inserted.id
                VALUES (:name, :description, GETDATE())
                ''')

                result = db.session.execute(sql, {
                    'name': '蔬菜供应商',
                    'description': '提供新鲜蔬菜的供应商'
                })
                category_id = result.scalar()

            # 检查是否已存在演示供应商
            existing_supplier = db.session.execute(text('''
                SELECT id FROM suppliers WHERE name = :name
            '''), {'name': '绿色农场有限公司'}).scalar()

            if existing_supplier:
                supplier_id = existing_supplier
            else:
                # 创建演示供应商
                sql = text('''
                INSERT INTO suppliers
                (name, contact_person, phone, email, address, business_license,
                 status, category_id, created_at, updated_at)
                OUTPUT inserted.id
                VALUES
                (:name, :contact_person, :phone, :email, :address, :business_license,
                 :status, :category_id, GETDATE(), GETDATE())
                ''')

                result = db.session.execute(sql, {
                    'name': '绿色农场有限公司',
                    'contact_person': '张经理',
                    'phone': '***********',
                    'email': '<EMAIL>',
                    'address': '北京市昌平区绿色农业园区',
                    'business_license': '91110000123456789X',
                    'status': 1,
                    'category_id': category_id
                })

                supplier_id = result.scalar()

            # 检查是否已存在供应商-学校关联关系
            existing_relation = db.session.execute(text('''
                SELECT id FROM supplier_school_relations
                WHERE supplier_id = :supplier_id AND area_id = :area_id
            '''), {'supplier_id': supplier_id, 'area_id': area_id}).scalar()

            if not existing_relation:
                # 创建供应商-学校关联关系
                # 使用字符串格式化避免date类型参数绑定问题
                contract_number = f"DEMO-{datetime.now().strftime('%Y%m%d')}-{supplier_id:04d}"
                start_date_str = date.today().strftime('%Y-%m-%d')

                relation_sql = text(f'''
                INSERT INTO supplier_school_relations
                (supplier_id, area_id, contract_number, start_date, status, created_at, updated_at)
                OUTPUT inserted.id
                VALUES
                ({supplier_id}, {area_id}, '{contract_number}', '{start_date_str}', 1, GETDATE(), GETDATE())
                ''')

                relation_result = db.session.execute(relation_sql)
                relation_id = relation_result.scalar()
            else:
                relation_id = existing_relation

            db.session.commit()

            return {
                'supplier_id': supplier_id,
                'category_id': category_id,
                'relation_id': relation_id,
                'message': '演示供应商创建成功'
            }

        except Exception as e:
            db.session.rollback()
            return {'error': str(e)}

    @staticmethod
    def _create_demo_ingredients(area_id):
        """创建演示食材数据"""
        from app.models import Ingredient, IngredientCategory
        from app import db
        from sqlalchemy import text
        from datetime import datetime

        try:
            # 创建食材分类
            demo_categories = [
                {'name': '蔬菜类', 'description': '新鲜蔬菜'},
                {'name': '肉类', 'description': '各种肉类食材'},
                {'name': '调料', 'description': '调味料和香料'},
                {'name': '主食', 'description': '米面等主食类'}
            ]

            created_categories = []
            for cat_data in demo_categories:
                # 检查是否已存在
                existing_cat = db.session.execute(text('''
                    SELECT id FROM ingredient_categories WHERE name = :name
                '''), {'name': cat_data['name']}).scalar()

                if existing_cat:
                    created_categories.append(existing_cat)
                else:
                    # 创建新分类
                    result = db.session.execute(text('''
                        INSERT INTO ingredient_categories (name, description, created_at, updated_at)
                        OUTPUT inserted.id
                        VALUES (:name, :description, GETDATE(), GETDATE())
                    '''), cat_data)
                    cat_id = result.scalar()
                    created_categories.append(cat_id)

            # 创建演示食材
            demo_ingredients = [
                {
                    'name': '白萝卜',
                    'category': '蔬菜类',
                    'category_id': created_categories[0],
                    'unit': '公斤',
                    'standard_unit': '公斤',
                    'storage_temp': '0-4°C',
                    'storage_condition': '冷藏保存',
                    'shelf_life': 7,
                    'specification': '新鲜白萝卜，无损伤',
                    'nutrition_info': '{"维生素C": "高", "纤维": "丰富"}'
                },
                {
                    'name': '猪肉',
                    'category': '肉类',
                    'category_id': created_categories[1],
                    'unit': '公斤',
                    'standard_unit': '公斤',
                    'storage_temp': '-18°C',
                    'storage_condition': '冷冻保存',
                    'shelf_life': 90,
                    'specification': '新鲜猪肉，无异味',
                    'nutrition_info': '{"蛋白质": "高", "脂肪": "适中"}'
                },
                {
                    'name': '生抽',
                    'category': '调料',
                    'category_id': created_categories[2],
                    'unit': '瓶',
                    'standard_unit': '毫升',
                    'storage_temp': '常温',
                    'storage_condition': '阴凉干燥处',
                    'shelf_life': 365,
                    'specification': '500ml装生抽',
                    'nutrition_info': '{"钠": "高", "氨基酸": "丰富"}'
                },
                {
                    'name': '大米',
                    'category': '主食',
                    'category_id': created_categories[3],
                    'unit': '公斤',
                    'standard_unit': '公斤',
                    'storage_temp': '常温',
                    'storage_condition': '干燥通风处',
                    'shelf_life': 180,
                    'specification': '优质大米，无杂质',
                    'nutrition_info': '{"碳水化合物": "高", "蛋白质": "适中"}'
                }
            ]

            created_ingredients = []
            for ing_data in demo_ingredients:
                # 检查是否已存在
                existing_ing = db.session.execute(text('''
                    SELECT id FROM ingredients
                    WHERE name = :name AND area_id = :area_id
                '''), {'name': ing_data['name'], 'area_id': area_id}).scalar()

                if existing_ing:
                    created_ingredients.append(existing_ing)
                else:
                    # 创建新食材
                    result = db.session.execute(text(f'''
                        INSERT INTO ingredients
                        (name, category, category_id, area_id, unit, standard_unit,
                         storage_temp, storage_condition, shelf_life, specification,
                         nutrition_info, status, is_global, created_at, updated_at)
                        OUTPUT inserted.id
                        VALUES
                        ('{ing_data['name']}', '{ing_data['category']}', {ing_data['category_id']}, {area_id},
                         '{ing_data['unit']}', '{ing_data['standard_unit']}', '{ing_data['storage_temp']}',
                         '{ing_data['storage_condition']}', {ing_data['shelf_life']}, '{ing_data['specification']}',
                         '{ing_data['nutrition_info']}', 1, 0, GETDATE(), GETDATE())
                    '''))
                    ing_id = result.scalar()
                    created_ingredients.append(ing_id)

            db.session.commit()

            return {
                'categories': created_categories,
                'ingredients': created_ingredients,
                'message': '演示食材创建成功'
            }

        except Exception as e:
            db.session.rollback()
            return {'error': str(e)}

    @staticmethod
    def _create_demo_stock_in(area_id):
        """创建演示入库数据"""
        from app.models import Warehouse, StorageLocation, StockIn, StockInItem, Supplier, Ingredient
        from app import db
        from sqlalchemy import text
        from datetime import datetime, date

        try:
            # 检查是否已有仓库
            existing_warehouse = db.session.execute(text('''
                SELECT id FROM warehouses WHERE area_id = :area_id
            '''), {'area_id': area_id}).scalar()

            if not existing_warehouse:
                return {'error': '请先创建仓库才能进行入库操作'}

            warehouse_id = existing_warehouse

            # 检查是否有存储位置
            existing_location = db.session.execute(text('''
                SELECT id FROM storage_locations WHERE warehouse_id = :warehouse_id
            '''), {'warehouse_id': warehouse_id}).scalar()

            if not existing_location:
                return {'error': '请先创建存储位置才能进行入库操作'}

            storage_location_id = existing_location

            # 检查是否有供应商
            existing_supplier = db.session.execute(text('''
                SELECT s.id FROM suppliers s
                JOIN supplier_school_relations ssr ON s.id = ssr.supplier_id
                WHERE ssr.area_id = :area_id AND ssr.status = 1
            '''), {'area_id': area_id}).scalar()

            if not existing_supplier:
                return {'error': '请先创建供应商才能进行入库操作'}

            supplier_id = existing_supplier

            # 检查是否有食材
            existing_ingredients = db.session.execute(text('''
                SELECT id, name, unit FROM ingredients
                WHERE area_id = :area_id OR is_global = 1
                ORDER BY id
            '''), {'area_id': area_id}).fetchall()

            if not existing_ingredients:
                return {'error': '请先创建食材才能进行入库操作'}

            # 生成入库单号
            today = datetime.now()
            stock_in_number = f"RK{today.strftime('%Y%m%d')}{today.strftime('%H%M%S')}"

            # 检查是否已存在相同的入库单
            existing_stock_in = db.session.execute(text('''
                SELECT id FROM stock_ins WHERE stock_in_number = :number
            '''), {'number': stock_in_number}).scalar()

            if existing_stock_in:
                return {'error': '演示入库单已存在'}

            # 创建入库单
            stock_in_date_str = date.today().strftime('%Y-%m-%d')

            stock_in_sql = text(f'''
                INSERT INTO stock_ins
                (stock_in_number, warehouse_id, stock_in_date, stock_in_type,
                 operator_id, status, notes, created_at, updated_at)
                OUTPUT inserted.id
                VALUES
                ('{stock_in_number}', {warehouse_id}, '{stock_in_date_str}', '采购入库',
                 1, '已入库', '演示入库数据', GETDATE(), GETDATE())
            ''')

            result = db.session.execute(stock_in_sql)
            stock_in_id = result.scalar()

            # 创建入库明细（取前3个食材）
            created_items = []
            for i, ingredient in enumerate(existing_ingredients[:3]):
                ingredient_id, ingredient_name, unit = ingredient

                # 生成批次号
                batch_number = f"DEMO{today.strftime('%Y%m%d')}{i+1:02d}"

                # 计算生产日期和过期日期
                production_date = date.today()
                expiry_date = date(2025, 12, 31)  # 固定过期日期

                quantity = 10.0 + i * 5  # 不同数量
                unit_price = 5.0 + i * 2  # 不同单价

                item_sql = text(f'''
                    INSERT INTO stock_in_items
                    (stock_in_id, ingredient_id, batch_number, quantity, unit,
                     production_date, expiry_date, storage_location_id, supplier_id,
                     quality_check_result, unit_price, created_at, updated_at)
                    OUTPUT inserted.id
                    VALUES
                    ({stock_in_id}, {ingredient_id}, '{batch_number}', {quantity}, '{unit}',
                     '{production_date.strftime('%Y-%m-%d')}', '{expiry_date.strftime('%Y-%m-%d')}',
                     {storage_location_id}, {supplier_id}, '合格', {unit_price}, GETDATE(), GETDATE())
                ''')

                item_result = db.session.execute(item_sql)
                item_id = item_result.scalar()
                created_items.append({
                    'id': item_id,
                    'ingredient_name': ingredient_name,
                    'quantity': quantity,
                    'unit': unit
                })

            db.session.commit()

            return {
                'stock_in_id': stock_in_id,
                'stock_in_number': stock_in_number,
                'items': created_items,
                'message': '演示入库数据创建成功'
            }

        except Exception as e:
            db.session.rollback()
            return {'error': str(e)}

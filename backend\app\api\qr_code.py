from flask import Blueprint, jsonify, request, current_app, url_for
from flask_login import login_required, current_user
import qrcode
import io
import base64
from datetime import datetime

qr_code_bp = Blueprint('qr_code', __name__)

@qr_code_bp.route('/api/v2/qr-code/dining-companion', methods=['GET'])
@login_required
def generate_dining_companion_qr_code():
    """生成陪餐记录二维码"""
    try:
        # 获取日期参数
        date_str = request.args.get('date', datetime.now().strftime('%Y-%m-%d'))
        area_id = request.args.get('area_id', type=int)
        
        # 构建陪餐记录URL
        url = url_for('daily_management.dining_companions_scan', _external=True)
        if area_id:
            url += f'?date={date_str}&area_id={area_id}'
        else:
            url += f'?date={date_str}'
        
        # 生成二维码
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(url)
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        
        # 将图片转换为base64
        buffered = io.BytesIO()
        img.save(buffered)
        img_str = base64.b64encode(buffered.getvalue()).decode()
        
        return jsonify({
            'success': True,
            'qr_code': f'data:image/png;base64,{img_str}',
            'url': url
        })
    except Exception as e:
        current_app.logger.error(f"生成二维码失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

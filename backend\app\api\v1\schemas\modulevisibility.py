"""
ModuleVisibility 序列化模式
"""

from marshmallow import Schema, fields, validate

class ModuleVisibilitySchema(Schema):
    """ModuleVisibility 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    module_id = fields.String(required=True)
    
    
    
    role_id = fields.Integer(required=True)
    
    
    
    is_visible = fields.Integer(required=True)
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
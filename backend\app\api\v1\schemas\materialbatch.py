"""
MaterialBatch 序列化模式
"""

from marshmallow import Schema, fields, validate

class MaterialBatchSchema(Schema):
    """MaterialBatch 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    batch_number = fields.String(required=True)
    
    
    
    ingredient_id = fields.Integer(required=True)
    
    
    
    supplier_id = fields.Integer(required=True)
    
    
    
    production_date = fields.Date(required=True)
    
    
    
    expiry_date = fields.Date(required=True)
    
    
    
    production_batch_no = fields.String()
    
    
    
    origin_place = fields.String()
    
    
    
    inspection_no = fields.String()
    
    
    
    certificate_no = fields.String()
    
    
    
    initial_quantity = fields.Float(required=True)
    
    
    
    current_quantity = fields.Float(required=True)
    
    
    
    unit = fields.String(required=True)
    
    
    
    unit_price = fields.String()
    
    
    
    status = fields.String(required=True)
    
    
    
    area_id = fields.Integer(required=True)
    
    
    
    remark = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
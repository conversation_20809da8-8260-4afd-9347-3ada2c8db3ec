import request from '@/utils/request'

const purchaseorderAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/purchaseorder',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/purchaseorder/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/purchaseorder',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/purchaseorder/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/purchaseorder/${id}`,
      method: 'delete'
    })
  }
}

export default purchaseorderAPI
# 数据库配置完成总结

## ✅ 配置完成

已成功将后端 API 项目配置为连接您的 SQL Server 数据库。

## 📊 数据库连接信息

- **服务器地址**: **************
- **数据库名称**: StudentsCMSSP
- **用户名**: StudentsCMSSP
- **密码**: Xg2LS44Cyz5Zt8.
- **驱动**: ODBC Driver 17 for SQL Server

## 🔧 修改的文件

### 1. backend/config.py
- 更新为 SQL Server 连接配置
- 添加了连接池和超时设置
- 支持环境变量覆盖

### 2. backend/requirements.txt
- 添加了 pyodbc 和 SQLAlchemy 依赖
- 确保支持 SQL Server 连接

### 3. backend/app/__init__.py
- 集成数据库连接初始化
- 添加连接测试功能
- 增加错误处理和健康检查端点

### 4. backend/app/utils/database.py
- 创建数据库工具类
- 提供连接池管理
- 支持连接测试

### 5. backend/run.py
- 更新启动脚本
- 添加启动信息显示
- 支持环境配置

### 6. backend/test_connection.py
- 创建数据库连接测试脚本
- 提供详细的连接状态信息

## 🚀 使用方法

### 测试数据库连接
```bash
cd backend
python test_connection.py
```

### 启动后端服务
```bash
cd backend
pip install -r requirements.txt
python run.py
```

### 访问端点
- **健康检查**: http://localhost:5001/health
- **数据库测试**: http://localhost:5001/api/v1/test-db
- **API 文档**: http://localhost:5001/api/v1/

## 🔍 连接字符串格式

```python
mssql+pyodbc://StudentsCMSSP:Xg2LS44Cyz5Zt8.@**************/StudentsCMSSP?driver=ODBC+Driver+17+for+SQL+Server&TrustServerCertificate=yes
```

## ⚙️ 环境变量支持

可以通过环境变量覆盖默认配置：

```bash
export DB_SERVER="your_server"
export DB_DATABASE="your_database"
export DB_USERNAME="your_username"
export DB_PASSWORD="your_password"
export FLASK_ENV="production"
```

## 🛠️ 故障排除

### 常见问题

1. **连接超时**
   - 检查服务器地址和端口
   - 确认防火墙设置

2. **认证失败**
   - 验证用户名和密码
   - 检查用户权限

3. **驱动问题**
   - 确保安装了 ODBC Driver 17 for SQL Server
   - 在 Windows 上可能需要额外安装

### 安装 ODBC 驱动

**Windows:**
下载并安装 Microsoft ODBC Driver 17 for SQL Server

**Linux:**
```bash
# Ubuntu/Debian
sudo apt-get install unixodbc-dev

# CentOS/RHEL
sudo yum install unixODBC-devel
```

## 📝 下一步

1. 测试数据库连接
2. 启动后端服务
3. 验证 API 端点
4. 开始前端开发
5. 进行功能迁移

配置已完成，您现在可以开始使用新的 API 后端了！

"""
VoucherDetail 序列化模式
"""

from marshmallow import Schema, fields, validate

class VoucherDetailSchema(Schema):
    """VoucherDetail 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    voucher_id = fields.Integer(required=True)
    
    
    
    line_number = fields.Integer(required=True)
    
    
    
    subject_id = fields.Integer(required=True)
    
    
    
    summary = fields.String(required=True)
    
    
    
    debit_amount = fields.String(required=True)
    
    
    
    credit_amount = fields.String(required=True)
    
    
    
    auxiliary_info = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
"""
OnlineConsultation 序列化模式
"""

from marshmallow import Schema, fields, validate

class OnlineConsultationSchema(Schema):
    """OnlineConsultation 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    name = fields.String(required=True)
    
    
    
    contact_type = fields.String(required=True)
    
    
    
    contact_value = fields.String(required=True)
    
    
    
    content = fields.String(required=True)
    
    
    
    status = fields.String(required=True)
    
    
    
    reply_content = fields.String()
    
    
    
    reply_time = fields.String()
    
    
    
    reply_user_id = fields.Integer()
    
    
    
    source = fields.String(required=True)
    
    
    
    ip_address = fields.String()
    
    
    
    user_agent = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
"""
Photo 序列化模式
"""

from marshmallow import Schema, fields, validate

class PhotoSchema(Schema):
    """Photo 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    reference_id = fields.Integer(required=True)
    
    
    
    reference_type = fields.String(required=True)
    
    
    
    file_name = fields.String(required=True)
    
    
    
    file_path = fields.String(required=True)
    
    
    
    description = fields.String()
    
    
    
    rating = fields.Integer()
    
    
    
    upload_time = fields.String(required=True)
    
    

    class Meta:
        ordered = True
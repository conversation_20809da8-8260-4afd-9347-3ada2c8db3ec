import request from '@/utils/request'

const materialbatchAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/materialbatch',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/materialbatch/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/materialbatch',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/materialbatch/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/materialbatch/${id}`,
      method: 'delete'
    })
  }
}

export default materialbatchAPI
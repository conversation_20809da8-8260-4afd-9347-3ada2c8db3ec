import request from '@/utils/request'

const recipeAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/recipe',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/recipe/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/recipe',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/recipe/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/recipe/${id}`,
      method: 'delete'
    })
  }
}

export default recipeAPI
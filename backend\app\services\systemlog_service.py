"""
SystemLog 服务层
"""

from typing import Optional, Dict, Any
from sqlalchemy.orm import Session
from app.models.models_system import SystemLog
from app.utils.database import get_db_session

class SystemLogService:
    """SystemLog 服务"""

    @staticmethod
    def get_list(page: int = 1, per_page: int = 10, **filters):
        """获取SystemLog列表"""
        with get_db_session() as session:
            query = session.query(SystemLog)

            # 应用过滤条件
            for key, value in filters.items():
                if hasattr(SystemLog, key) and value is not None:
                    query = query.filter(getattr(SystemLog, key) == value)

            return query.paginate(
                page=page,
                per_page=per_page,
                error_out=False
            )

    @staticmethod
    def get_by_id(id: int) -> Optional[SystemLog]:
        """根据ID获取SystemLog"""
        with get_db_session() as session:
            return session.query(SystemLog).filter(SystemLog.id == id).first()

    @staticmethod
    def create(data: Dict[str, Any]) -> SystemLog:
        """创建SystemLog"""
        with get_db_session() as session:
            item = SystemLog(**data)
            session.add(item)
            session.commit()
            session.refresh(item)
            return item

    @staticmethod
    def update(id: int, data: Dict[str, Any]) -> Optional[SystemLog]:
        """更新SystemLog"""
        with get_db_session() as session:
            item = session.query(SystemLog).filter(SystemLog.id == id).first()
            if not item:
                return None

            for key, value in data.items():
                if hasattr(item, key):
                    setattr(item, key, value)

            session.commit()
            session.refresh(item)
            return item

    @staticmethod
    def delete(id: int) -> bool:
        """删除SystemLog"""
        with get_db_session() as session:
            item = session.query(SystemLog).filter(SystemLog.id == id).first()
            if not item:
                return False

            session.delete(item)
            session.commit()
            return True
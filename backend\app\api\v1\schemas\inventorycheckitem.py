"""
InventoryCheckItem 序列化模式
"""

from marshmallow import Schema, fields, validate

class InventoryCheckItemSchema(Schema):
    """InventoryCheckItem 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    check_id = fields.Integer(required=True)
    
    
    
    inventory_id = fields.Integer(required=True)
    
    
    
    system_quantity = fields.Float(required=True)
    
    
    
    actual_quantity = fields.Float(required=True)
    
    
    
    unit = fields.String(required=True)
    
    
    
    difference = fields.Float(required=True)
    
    
    
    difference_reason = fields.String()
    
    
    
    adjustment_type = fields.String()
    
    
    
    is_adjusted = fields.Boolean(required=True)
    
    
    
    notes = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
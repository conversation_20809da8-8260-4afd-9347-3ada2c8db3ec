"""
MedicalExamination 序列化模式
"""

from marshmallow import Schema, fields, validate

class MedicalExaminationSchema(Schema):
    """MedicalExamination 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    employee_id = fields.Integer(required=True)
    
    
    
    exam_date = fields.Date(required=True)
    
    
    
    exam_hospital = fields.String(required=True)
    
    
    
    result = fields.String(required=True)
    
    
    
    report_img = fields.String()
    
    
    
    notes = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
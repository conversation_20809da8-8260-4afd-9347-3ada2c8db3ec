"""
SupplierCertificate 序列化模式
"""

from marshmallow import Schema, fields, validate

class SupplierCertificateSchema(Schema):
    """SupplierCertificate 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    supplier_id = fields.Integer(required=True)
    
    
    
    certificate_type = fields.String(required=True)
    
    
    
    certificate_number = fields.String(required=True)
    
    
    
    issue_date = fields.Date(required=True)
    
    
    
    expiry_date = fields.Date(required=True)
    
    
    
    issuing_authority = fields.String(required=True)
    
    
    
    certificate_image = fields.String()
    
    
    
    status = fields.String(required=True)
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
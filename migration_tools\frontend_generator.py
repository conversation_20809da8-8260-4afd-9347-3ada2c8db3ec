#!/usr/bin/env python3
"""
前端组件生成器
基于分析结果生成 Vue 3 + Element Plus 前端组件
"""

import os
import json
from pathlib import Path
from typing import Dict, List, Any
from jinja2 import Template

class FrontendGenerator:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.models = []
        self.templates = {
            'list_view': self._get_list_view_template(),
            'form_view': self._get_form_view_template(),
            'detail_view': self._get_detail_view_template(),
            'api_service': self._get_api_service_template(),
            'store': self._get_store_template(),
            'router': self._get_router_template()
        }

    def generate_frontend(self, analysis_file: str = 'migration_analysis_report.json', output_dir: str = 'frontend'):
        """生成前端项目"""
        print("🎨 开始生成前端项目...")

        # 读取分析报告
        self._load_analysis_report(analysis_file)

        # 创建输出目录
        output_path = self.project_root / output_dir
        self._create_directory_structure(output_path)

        # 生成组件文件
        for model in self.models:
            self._generate_model_components(model, output_path)

        # 生成主要配置文件
        self._generate_main_files(output_path)

        print(f"✅ 前端项目生成完成，输出目录: {output_path}")

    def _load_analysis_report(self, analysis_file: str):
        """加载分析报告"""
        report_path = self.project_root / analysis_file
        if not report_path.exists():
            print(f"⚠️ 分析报告文件不存在: {report_path}")
            return

        with open(report_path, 'r', encoding='utf-8') as f:
            analysis_data = json.load(f)

        # 提取模型信息
        self.models = analysis_data.get('models', [])

        # 过滤掉不需要的模型
        self.models = [model for model in self.models if model['name'] != 'StandardModel']

        print(f"📋 加载了 {len(self.models)} 个模型")

    def _create_directory_structure(self, output_path: Path):
        """创建目录结构"""
        directories = [
            'src',
            'src/api',
            'src/components',
            'src/components/common',
            'src/views',
            'src/stores',
            'src/router',
            'src/utils',
            'src/assets',
            'src/assets/css',
            'src/assets/images',
            'public'
        ]

        for directory in directories:
            (output_path / directory).mkdir(parents=True, exist_ok=True)

    def _generate_model_components(self, model: Dict[str, Any], output_path: Path):
        """为单个模型生成组件"""
        print(f"🎨 生成 {model['name']} 组件...")

        model_name = model['name']
        api_name = model_name.lower()

        # 生成列表视图
        list_content = self.templates['list_view'].render(model=model, api_name=api_name)
        list_file = output_path / 'src' / 'views' / f"{model_name}List.vue"
        with open(list_file, 'w', encoding='utf-8') as f:
            f.write(list_content)

        # 生成表单视图
        form_content = self.templates['form_view'].render(model=model, api_name=api_name)
        form_file = output_path / 'src' / 'views' / f"{model_name}Form.vue"
        with open(form_file, 'w', encoding='utf-8') as f:
            f.write(form_content)

        # 生成详情视图
        detail_content = self.templates['detail_view'].render(model=model, api_name=api_name)
        detail_file = output_path / 'src' / 'views' / f"{model_name}Detail.vue"
        with open(detail_file, 'w', encoding='utf-8') as f:
            f.write(detail_content)

        # 生成 API 服务
        api_content = self.templates['api_service'].render(model=model, api_name=api_name)
        api_file = output_path / 'src' / 'api' / f"{api_name}.js"
        with open(api_file, 'w', encoding='utf-8') as f:
            f.write(api_content)

        # 生成 Pinia Store
        store_content = self.templates['store'].render(model=model, api_name=api_name)
        store_file = output_path / 'src' / 'stores' / f"{api_name}.js"
        with open(store_file, 'w', encoding='utf-8') as f:
            f.write(store_content)

    def _generate_main_files(self, output_path: Path):
        """生成主要配置文件"""
        print("📄 生成主要配置文件...")

        # 生成路由配置
        router_content = self.templates['router'].render(models=self.models)
        router_file = output_path / 'src' / 'router' / 'index.js'
        with open(router_file, 'w', encoding='utf-8') as f:
            f.write(router_content)

        # 生成主应用文件
        app_content = self._get_app_template().render(models=self.models)
        app_file = output_path / 'src' / 'App.vue'
        with open(app_file, 'w', encoding='utf-8') as f:
            f.write(app_content)

        # 生成主入口文件
        main_content = self._get_main_template().render()
        main_file = output_path / 'src' / 'main.js'
        with open(main_file, 'w', encoding='utf-8') as f:
            f.write(main_content)

        # 生成 package.json
        package_content = self._get_package_template().render()
        package_file = output_path / 'package.json'
        with open(package_file, 'w', encoding='utf-8') as f:
            f.write(package_content)

        # 生成 vite.config.js
        vite_content = self._get_vite_config_template().render()
        vite_file = output_path / 'vite.config.js'
        with open(vite_file, 'w', encoding='utf-8') as f:
            f.write(vite_content)

        # 生成 index.html
        html_content = self._get_html_template().render()
        html_file = output_path / 'index.html'
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html_content)

    def _get_list_view_template(self) -> Template:
        """获取列表视图模板"""
        template_str = '''<template>
  <div class="page-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>{{ model.name }}管理</span>
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增
          </el-button>
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form :model="searchForm" inline class="search-form">
        {% for field in model.fields %}
        {% if field.name not in ['id', 'created_at', 'updated_at'] %}
        <el-form-item label="{{ field.name }}">
          <el-input v-model="searchForm.{{ field.name }}" placeholder="请输入{{ field.name }}" clearable />
        </el-form-item>
        {% endif %}
        {% endfor %}
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table :data="tableData" v-loading="loading" stripe>
        {% for field in model.fields %}
        <el-table-column prop="{{ field.name }}" label="{{ field.name }}" />
        {% endfor %}
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)">查看</el-button>
            <el-button type="warning" size="small" @click="handleEdit(row)">编辑</el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.pageSize"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { use{{ model.name }}Store } from '@/stores/{{ api_name }}'

const router = useRouter()
const {{ api_name }}Store = use{{ model.name }}Store()

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const searchForm = reactive({
  {% for field in model.fields %}
  {% if field.name not in ['id', 'created_at', 'updated_at'] %}
  {{ field.name }}: '',
  {% endif %}
  {% endfor %}
})
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 方法
const fetchData = async () => {
  loading.value = true
  try {
    const result = await {{ api_name }}Store.fetchList({
      page: pagination.page,
      per_page: pagination.pageSize,
      ...searchForm
    })
    tableData.value = result.items
    pagination.total = result.total
  } catch (error) {
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  fetchData()
}

const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  handleSearch()
}

const handleAdd = () => {
  router.push({ name: '{{ model.name }}Form' })
}

const handleView = (row) => {
  router.push({ name: '{{ model.name }}Detail', params: { id: row.id } })
}

const handleEdit = (row) => {
  router.push({ name: '{{ model.name }}Form', params: { id: row.id } })
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除这条记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await {{ api_name }}Store.delete(row.id)
    ElMessage.success('删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  fetchData()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  fetchData()
}

// 生命周期
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  margin-bottom: 20px;
}
</style>
'''
        return Template(template_str)

    def _get_form_view_template(self) -> Template:
        """获取表单视图模板"""
        template_str = '''<template>
  <div class="page-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>{{ '编辑' if isEdit else '新增' }}{{ model.name }}</span>
          <el-button @click="handleBack">返回</el-button>
        </div>
      </template>

      <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
        {% for field in model.fields %}
        {% if field.name not in ['id', 'created_at', 'updated_at'] %}
        <el-form-item label="{{ field.name }}" prop="{{ field.name }}">
          {% if field.type in ['String', 'Text'] %}
          <el-input v-model="form.{{ field.name }}" placeholder="请输入{{ field.name }}" />
          {% elif field.type == 'Integer' %}
          <el-input-number v-model="form.{{ field.name }}" placeholder="请输入{{ field.name }}" />
          {% elif field.type == 'Boolean' %}
          <el-switch v-model="form.{{ field.name }}" />
          {% elif field.type in ['DateTime', 'Date'] %}
          <el-date-picker v-model="form.{{ field.name }}" type="datetime" placeholder="请选择{{ field.name }}" />
          {% else %}
          <el-input v-model="form.{{ field.name }}" placeholder="请输入{{ field.name }}" />
          {% endif %}
        </el-form-item>
        {% endif %}
        {% endfor %}

        <el-form-item>
          <el-button type="primary" @click="handleSubmit" :loading="loading">
            {{ '更新' if isEdit else '创建' }}
          </el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { use{{ model.name }}Store } from '@/stores/{{ api_name }}'

const route = useRoute()
const router = useRouter()
const {{ api_name }}Store = use{{ model.name }}Store()

// 响应式数据
const loading = ref(false)
const formRef = ref()
const isEdit = computed(() => !!route.params.id)

const form = reactive({
  {% for field in model.fields %}
  {% if field.name not in ['id', 'created_at', 'updated_at'] %}
  {{ field.name }}: {% if field.type == 'Boolean' %}false{% elif field.type == 'Integer' %}0{% else %}''{% endif %},
  {% endif %}
  {% endfor %}
})

const rules = reactive({
  {% for field in model.fields %}
  {% if field.name not in ['id', 'created_at', 'updated_at'] and not field.nullable %}
  {{ field.name }}: [{ required: true, message: '请输入{{ field.name }}', trigger: 'blur' }],
  {% endif %}
  {% endfor %}
})

// 方法
const fetchData = async () => {
  if (!isEdit.value) return

  try {
    const data = await {{ api_name }}Store.fetchById(route.params.id)
    Object.assign(form, data)
  } catch (error) {
    ElMessage.error('获取数据失败')
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    loading.value = true

    if (isEdit.value) {
      await {{ api_name }}Store.update(route.params.id, form)
      ElMessage.success('更新成功')
    } else {
      await {{ api_name }}Store.create(form)
      ElMessage.success('创建成功')
    }

    handleBack()
  } catch (error) {
    ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
  } finally {
    loading.value = false
  }
}

const handleReset = () => {
  formRef.value.resetFields()
}

const handleBack = () => {
  router.push({ name: '{{ model.name }}List' })
}

// 生命周期
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
'''
        return Template(template_str)

    def _get_detail_view_template(self) -> Template:
        """获取详情视图模板"""
        template_str = '''<template>
  <div class="page-container">
    <el-card v-loading="loading">
      <template #header>
        <div class="card-header">
          <span>{{ model.name }}详情</span>
          <div>
            <el-button type="warning" @click="handleEdit">编辑</el-button>
            <el-button @click="handleBack">返回</el-button>
          </div>
        </div>
      </template>

      <el-descriptions :column="2" border>
        {% for field in model.fields %}
        <el-descriptions-item label="{{ field.name }}">
          {{ '{{ data.{{ field.name }} }}' }}
        </el-descriptions-item>
        {% endfor %}
      </el-descriptions>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { use{{ model.name }}Store } from '@/stores/{{ api_name }}'

const route = useRoute()
const router = useRouter()
const {{ api_name }}Store = use{{ model.name }}Store()

// 响应式数据
const loading = ref(false)
const data = ref({})

// 方法
const fetchData = async () => {
  loading.value = true
  try {
    data.value = await {{ api_name }}Store.fetchById(route.params.id)
  } catch (error) {
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const handleEdit = () => {
  router.push({ name: '{{ model.name }}Form', params: { id: route.params.id } })
}

const handleBack = () => {
  router.push({ name: '{{ model.name }}List' })
}

// 生命周期
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
'''
        return Template(template_str)

    def _get_api_service_template(self) -> Template:
        """获取 API 服务模板"""
        template_str = '''import request from '@/utils/request'

const {{ api_name }}API = {
  // 获取列表
  getList(params) {
    return request({
      url: '/{{ api_name }}',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/{{ api_name }}/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/{{ api_name }}',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/{{ api_name }}/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/{{ api_name }}/${id}`,
      method: 'delete'
    })
  }
}

export default {{ api_name }}API
'''
        return Template(template_str)

    def _get_store_template(self) -> Template:
        """获取 Pinia Store 模板"""
        template_str = '''import { defineStore } from 'pinia'
import {{ api_name }}API from '@/api/{{ api_name }}'

export const use{{ model.name }}Store = defineStore('{{ api_name }}', {
  state: () => ({
    list: [],
    total: 0,
    loading: false,
    current: null
  }),

  actions: {
    // 获取列表
    async fetchList(params = {}) {
      this.loading = true
      try {
        const response = await {{ api_name }}API.getList(params)
        this.list = response.data.items
        this.total = response.data.total
        return response.data
      } catch (error) {
        throw error
      } finally {
        this.loading = false
      }
    },

    // 获取详情
    async fetchById(id) {
      try {
        const response = await {{ api_name }}API.getById(id)
        this.current = response.data
        return response.data
      } catch (error) {
        throw error
      }
    },

    // 创建
    async create(data) {
      try {
        const response = await {{ api_name }}API.create(data)
        return response.data
      } catch (error) {
        throw error
      }
    },

    // 更新
    async update(id, data) {
      try {
        const response = await {{ api_name }}API.update(id, data)
        return response.data
      } catch (error) {
        throw error
      }
    },

    // 删除
    async delete(id) {
      try {
        await {{ api_name }}API.delete(id)
        // 从列表中移除
        const index = this.list.findIndex(item => item.id === id)
        if (index > -1) {
          this.list.splice(index, 1)
          this.total--
        }
        return true
      } catch (error) {
        throw error
      }
    }
  }
})
'''
        return Template(template_str)

    def _get_router_template(self) -> Template:
        """获取路由模板"""
        template_str = '''import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    name: 'Home',
    redirect: '/dashboard'
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/Dashboard.vue')
  },
  {% for model in models %}
  // {{ model.name }} 路由
  {
    path: '/{{ model.name.lower() }}',
    name: '{{ model.name }}List',
    component: () => import('@/views/{{ model.name }}List.vue')
  },
  {
    path: '/{{ model.name.lower() }}/create',
    name: '{{ model.name }}Form',
    component: () => import('@/views/{{ model.name }}Form.vue')
  },
  {
    path: '/{{ model.name.lower() }}/:id/edit',
    name: '{{ model.name }}Edit',
    component: () => import('@/views/{{ model.name }}Form.vue')
  },
  {
    path: '/{{ model.name.lower() }}/:id',
    name: '{{ model.name }}Detail',
    component: () => import('@/views/{{ model.name }}Detail.vue')
  },
  {% endfor %}
]

const router = createRouter({
  history: createWebHistory('/new/'),
  routes
})

export default router
'''
        return Template(template_str)

    def _get_app_template(self) -> Template:
        """获取主应用模板"""
        template_str = '''<template>
  <el-config-provider :locale="zhCn">
    <div id="app">
      <el-container>
        <el-aside width="200px">
          <el-menu
            :default-active="$route.path"
            router
            background-color="#545c64"
            text-color="#fff"
            active-text-color="#ffd04b"
          >
            <el-menu-item index="/dashboard">
              <el-icon><House /></el-icon>
              <span>仪表盘</span>
            </el-menu-item>
            {% for model in models %}
            <el-menu-item index="/{{ model.name.lower() }}">
              <el-icon><Document /></el-icon>
              <span>{{ model.name }}管理</span>
            </el-menu-item>
            {% endfor %}
          </el-menu>
        </el-aside>

        <el-container>
          <el-header>
            <div class="header-content">
              <h2>学校食堂管理系统</h2>
              <div class="user-info">
                <el-dropdown>
                  <span class="el-dropdown-link">
                    用户名
                    <el-icon class="el-icon--right"><arrow-down /></el-icon>
                  </span>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item>个人设置</el-dropdown-item>
                      <el-dropdown-item>退出登录</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
          </el-header>

          <el-main>
            <router-view />
          </el-main>
        </el-container>
      </el-container>
    </div>
  </el-config-provider>
</template>

<script setup>
import { House, Document, ArrowDown } from '@element-plus/icons-vue'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
</script>

<style>
#app {
  height: 100vh;
}

.el-container {
  height: 100%;
}

.el-header {
  background-color: #409eff;
  color: white;
  display: flex;
  align-items: center;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.user-info {
  color: white;
}

.el-dropdown-link {
  color: white;
  cursor: pointer;
}
</style>
'''
        return Template(template_str)

    def _get_main_template(self) -> Template:
        """获取主入口模板"""
        template_str = '''import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

import App from './App.vue'
import router from './router'

const app = createApp(App)
const pinia = createPinia()

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(pinia)
app.use(router)
app.use(ElementPlus)

app.mount('#app')
'''
        return Template(template_str)

    def _get_package_template(self) -> Template:
        """获取 package.json 模板"""
        template_str = '''{
  "name": "school-canteen-frontend",
  "version": "1.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview"
  },
  "dependencies": {
    "vue": "^3.4.0",
    "vue-router": "^4.2.0",
    "pinia": "^2.1.0",
    "element-plus": "^2.4.0",
    "@element-plus/icons-vue": "^2.3.0",
    "axios": "^1.6.0"
  },
  "devDependencies": {
    "@vitejs/plugin-vue": "^5.0.0",
    "vite": "^5.0.0"
  }
}
'''
        return Template(template_str)

    def _get_vite_config_template(self) -> Template:
        """获取 Vite 配置模板"""
        template_str = '''import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  base: '/new/',
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:5001',
        changeOrigin: true
      }
    }
  }
})
'''
        return Template(template_str)

    def _get_html_template(self) -> Template:
        """获取 HTML 模板"""
        template_str = '''<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>学校食堂管理系统</title>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
'''
        return Template(template_str)

if __name__ == '__main__':
    # 生成前端项目
    generator = FrontendGenerator('.')
    generator.generate_frontend()

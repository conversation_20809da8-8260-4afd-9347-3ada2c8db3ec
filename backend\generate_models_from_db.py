#!/usr/bin/env python3
"""
从 SQL Server 数据库自动生成 SQLAlchemy 模型
"""

import urllib.parse
from sqlalchemy import create_engine, MetaData, inspect, text
from sqlalchemy.ext.automap import automap_base
import os

def generate_models_from_database():
    """从数据库自动生成模型"""
    print("🔍 从 SQL Server 数据库生成模型...")
    
    # 数据库连接配置
    DB_SERVER = '(local)\\SQLEXPRESS'
    DB_DATABASE = 'StudentsCMSSP'
    DB_DRIVER = 'SQL Server'
    
    # 构建连接字符串
    conn_str = f"DRIVER={{{DB_DRIVER}}};SERVER={DB_SERVER};DATABASE={DB_DATABASE};Trusted_Connection=yes"
    quoted_conn_str = urllib.parse.quote_plus(conn_str)
    connection_string = f"mssql+pyodbc:///?odbc_connect={quoted_conn_str}"
    
    print(f"📊 连接到数据库: {DB_DATABASE}")
    
    try:
        # 创建引擎
        engine = create_engine(
            connection_string,
            echo=False,
            pool_pre_ping=True,
            pool_recycle=300
        )
        
        # 测试连接
        with engine.connect() as connection:
            result = connection.execute(text("SELECT 1"))
            print("✅ 数据库连接成功")
        
        # 反射数据库结构
        print("🔍 反射数据库结构...")
        metadata = MetaData()
        metadata.reflect(bind=engine)
        
        print(f"📊 发现 {len(metadata.tables)} 个表")
        
        # 生成模型代码
        model_code = generate_model_code(metadata, engine)
        
        # 写入文件
        output_file = 'app/models/models_auto.py'
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(model_code)
        
        print(f"✅ 模型已生成到: {output_file}")
        return True
        
    except Exception as e:
        print(f"❌ 生成模型失败: {e}")
        return False

def generate_model_code(metadata, engine):
    """生成模型代码"""
    
    code = '''"""
自动生成的 SQLAlchemy 模型
从 SQL Server 数据库反射生成
"""

from datetime import datetime, date
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import UserMixin
from sqlalchemy.dialects.mssql import DATETIME2
from flask import current_app
from sqlalchemy.ext.automap import automap_base

# 获取数据库实例
def get_db():
    """获取数据库实例"""
    return current_app.extensions['sqlalchemy']

# 动态模型基类
class DynamicModel:
    """动态模型基类"""
    
    def to_dict(self):
        """转换为字典"""
        result = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            if isinstance(value, datetime):
                result[column.name] = value.isoformat()
            elif isinstance(value, date):
                result[column.name] = value.isoformat()
            else:
                result[column.name] = value
        return result

    def update_from_dict(self, data):
        """从字典更新属性"""
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)

def create_models():
    """创建所有模型类"""
    db = get_db()
    
    # 使用 automap 自动映射数据库表
    Base = automap_base()
    Base.prepare(db.engine, reflect=True)
    
    # 创建模型字典
    models = {}
    
    # 为每个表创建模型类
    for table_name, table_class in Base.classes.items():
        class_name = table_name_to_class_name(table_name)
        
        # 动态创建模型类
        model_class = type(class_name, (table_class, DynamicModel), {
            '__doc__': f'自动生成的 {table_name} 模型'
        })
        
        models[class_name] = model_class
    
    # 特殊处理 User 模型，添加认证功能
    if hasattr(Base.classes, 'users'):
        class User(Base.classes.users, DynamicModel, UserMixin):
            """用户模型 - 添加认证功能"""
            
            def set_password(self, password):
                """设置密码"""
                if hasattr(self, 'password_hash'):
                    self.password_hash = generate_password_hash(password)
            
            def check_password(self, password):
                """检查密码"""
                if hasattr(self, 'password_hash'):
                    return check_password_hash(self.password_hash, password)
                return False
            
            def __repr__(self):
                username = getattr(self, 'username', getattr(self, 'name', 'Unknown'))
                return f'<User {username}>'
        
        models['User'] = User
    
    return models

def table_name_to_class_name(table_name):
    """将表名转换为类名"""
    # 移除复数形式
    if table_name.endswith('ies'):
        table_name = table_name[:-3] + 'y'
    elif table_name.endswith('s') and not table_name.endswith('ss'):
        table_name = table_name[:-1]
    
    # 转换为驼峰命名
    parts = table_name.split('_')
    return ''.join(word.capitalize() for word in parts)

# 全局变量，将在初始化时设置
_models = None

def get_models():
    """获取模型字典"""
    global _models
    if _models is None:
        _models = create_models()
    return _models

def get_model(name):
    """获取指定名称的模型"""
    models = get_models()
    return models.get(name)

# 为了兼容性，提供常用模型的快捷访问
def get_user_model():
    return get_model('User')

def get_supplier_model():
    return get_model('Supplier')

def get_ingredient_model():
    return get_model('Ingredient')
'''
    
    return code

def main():
    """主函数"""
    print("🚀 开始从数据库生成模型")
    print("=" * 50)
    
    success = generate_models_from_database()
    
    if success:
        print("\n" + "=" * 50)
        print("🎉 模型生成完成！")
        print("📝 下一步:")
        print("1. 检查生成的模型文件")
        print("2. 更新应用初始化代码")
        print("3. 测试应用启动")
    else:
        print("\n❌ 模型生成失败")
    
    return success

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)

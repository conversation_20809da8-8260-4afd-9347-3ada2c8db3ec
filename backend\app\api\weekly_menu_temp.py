"""
周菜单临时菜品API
用于处理周菜单临时菜品数据
"""

import json
from flask import Blueprint, request, jsonify
from flask_login import login_required, current_user
from app import db
from app.models import WeeklyMenu, WeeklyMenuRecipe
from app.models_weekly_menu_temp import WeeklyMenuRecipesTemp
from app.utils.permissions import check_permission

weekly_menu_temp_bp = Blueprint('weekly_menu_temp', __name__)


@weekly_menu_temp_bp.route('/api/weekly-menu/<int:weekly_menu_id>/temp-recipes', methods=['GET'])
@login_required
def get_temp_recipes(weekly_menu_id):
    """获取周菜单临时菜品数据"""
    try:
        # 检查权限
        weekly_menu = WeeklyMenu.query.get_or_404(weekly_menu_id)
        if not current_user.has_permission('周菜单管理', 'view'):
            return jsonify({
                'success': False,
                'message': '没有权限访问该周菜单'
            }), 403

        # 获取临时菜品数据
        temp_recipes = WeeklyMenuRecipesTemp.get_by_weekly_menu(weekly_menu_id)

        # 转换为字典列表
        data = [recipe.to_dict() for recipe in temp_recipes]

        return jsonify({
            'success': True,
            'data': data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500


@weekly_menu_temp_bp.route('/api/weekly-menu/<int:weekly_menu_id>/temp-recipes', methods=['POST'])
@login_required
def update_temp_recipes(weekly_menu_id):
    """更新周菜单临时菜品数据"""
    try:
        # 检查权限
        weekly_menu = WeeklyMenu.query.get_or_404(weekly_menu_id)
        if not current_user.has_permission('周菜单管理', 'edit'):
            return jsonify({
                'success': False,
                'message': '没有权限访问该周菜单'
            }), 403

        # 获取请求数据
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'message': '请求数据为空'
            }), 400

        # 验证数据
        day_of_week = data.get('day_of_week')
        meal_type = data.get('meal_type')
        recipes = data.get('recipes')

        if not day_of_week or not meal_type or not isinstance(recipes, list):
            return jsonify({
                'success': False,
                'message': '请求数据格式错误'
            }), 400

        # 删除原有的临时菜品
        WeeklyMenuRecipesTemp.delete_by_day_meal(weekly_menu_id, day_of_week, meal_type)

        # 添加新的临时菜品
        for recipe_data in recipes:
            recipe_id = recipe_data.get('recipe_id')
            recipe_name = recipe_data.get('recipe_name')
            is_custom = recipe_data.get('is_custom', False)

            if not recipe_name:
                continue

            # 创建临时菜品记录
            temp_recipe = WeeklyMenuRecipesTemp(
                weekly_menu_id=weekly_menu_id,
                day_of_week=day_of_week,
                meal_type=meal_type,
                recipe_id=recipe_id,
                recipe_name=recipe_name,
                is_custom=is_custom,
                temp_data=json.dumps(recipe_data)
            )

            db.session.add(temp_recipe)

        # 提交事务
        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'已更新 {len(recipes)} 个临时菜品'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500


@weekly_menu_temp_bp.route('/api/weekly-menu/<int:weekly_menu_id>/sync-temp-data', methods=['POST'])
@login_required
def sync_temp_data(weekly_menu_id):
    """同步临时菜品数据到周菜单菜品表"""
    try:
        # 检查权限
        weekly_menu = WeeklyMenu.query.get_or_404(weekly_menu_id)
        if not current_user.has_permission('周菜单管理', 'edit'):
            return jsonify({
                'success': False,
                'message': '没有权限访问该周菜单'
            }), 403

        # 同步数据
        count = WeeklyMenuRecipesTemp.sync_to_weekly_menu_recipes(weekly_menu_id)

        return jsonify({
            'success': True,
            'message': f'已同步 {count} 个菜品数据'
        })
    except Exception as e:
        import traceback
        error_msg = traceback.format_exc()
        print(f"同步临时菜品数据错误: {error_msg}")
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

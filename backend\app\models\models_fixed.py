"""
修复后的模型文件 - 正确导入 db
"""
from datetime import datetime, date
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import UserMixin
from sqlalchemy.dialects.mssql import DATETIME2
import json
from flask import session

def format_datetime(dt, fmt):
    """简单的日期时间格式化函数"""
    if dt and hasattr(dt, "strftime"):
        return dt.strftime(fmt)
    return str(dt) if dt else None

# 延迟导入 db，在函数中获取
def get_db():
    """获取数据库实例"""
    from flask import current_app
    return current_app.extensions['sqlalchemy']

# 创建模型的工厂函数
def create_models():
    """创建所有模型类"""
    db = get_db()
    
    class StandardModel(db.Model):
        """标准模型基类，包含通用字段和方法"""
        __abstract__ = True

        id = db.Column(db.Integer, primary_key=True, autoincrement=True)
        created_at = db.Column(DATETIME2(precision=1),
                              default=lambda: datetime.now().replace(microsecond=0),
                              nullable=False)
        updated_at = db.Column(DATETIME2(precision=1),
                              default=lambda: datetime.now().replace(microsecond=0),
                              onupdate=lambda: datetime.now().replace(microsecond=0),
                              nullable=False)

        def to_dict(self):
            """转换为字典"""
            result = {}
            for column in self.__table__.columns:
                value = getattr(self, column.name)
                if isinstance(value, datetime):
                    result[column.name] = value.isoformat()
                elif isinstance(value, date):
                    result[column.name] = value.isoformat()
                else:
                    result[column.name] = value
            return result

        def update_from_dict(self, data):
            """从字典更新属性"""
            for key, value in data.items():
                if hasattr(self, key):
                    setattr(self, key, value)

    class User(StandardModel, UserMixin):
        """用户模型"""
        __tablename__ = 'users'

        username = db.Column(db.String(50), unique=True, nullable=False)
        email = db.Column(db.String(100), unique=True, nullable=False)
        password_hash = db.Column(db.String(255), nullable=False)
        real_name = db.Column(db.String(50))
        phone = db.Column(db.String(20))
        is_active = db.Column(db.Boolean, default=True)
        last_login = db.Column(DATETIME2(precision=1))

        def set_password(self, password):
            """设置密码"""
            self.password_hash = generate_password_hash(password)

        def check_password(self, password):
            """检查密码"""
            return check_password_hash(self.password_hash, password)

        def __repr__(self):
            return f'<User {self.username}>'

    # 返回所有模型类
    return {
        'StandardModel': StandardModel,
        'User': User,
        'db': db
    }

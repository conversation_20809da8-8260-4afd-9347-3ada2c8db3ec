"""
RecipeSeasonalInfo 序列化模式
"""

from marshmallow import Schema, fields, validate

class RecipeSeasonalInfoSchema(Schema):
    """RecipeSeasonalInfo 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    recipe_id = fields.Integer(required=True)
    
    
    
    season = fields.String(required=True)
    
    
    
    suitability_score = fields.Integer()
    
    
    
    notes = fields.String()
    
    

    class Meta:
        ordered = True
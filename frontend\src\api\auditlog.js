import request from '@/utils/request'

const auditlogAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/auditlog',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/auditlog/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/auditlog',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/auditlog/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/auditlog/${id}`,
      method: 'delete'
    })
  }
}

export default auditlogAPI
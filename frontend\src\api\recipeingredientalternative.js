import request from '@/utils/request'

const recipeingredientalternativeAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/recipeingredientalternative',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/recipeingredientalternative/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/recipeingredientalternative',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/recipeingredientalternative/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/recipeingredientalternative/${id}`,
      method: 'delete'
    })
  }
}

export default recipeingredientalternativeAPI
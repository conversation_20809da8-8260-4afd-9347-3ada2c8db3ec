"""
BatchFlow 序列化模式
"""

from marshmallow import Schema, fields, validate

class BatchFlowSchema(Schema):
    """BatchFlow 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    batch_id = fields.Integer(required=True)
    
    
    
    flow_type = fields.String(required=True)
    
    
    
    flow_direction = fields.String(required=True)
    
    
    
    quantity = fields.Float(required=True)
    
    
    
    unit = fields.String(required=True)
    
    
    
    related_id = fields.Integer()
    
    
    
    related_type = fields.String()
    
    
    
    operator_id = fields.Integer(required=True)
    
    
    
    flow_date = fields.String(required=True)
    
    
    
    remark = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
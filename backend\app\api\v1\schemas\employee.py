"""
Employee 序列化模式
"""

from marshmallow import Schema, fields, validate

class EmployeeSchema(Schema):
    """Employee 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    name = fields.String(required=True)
    
    
    
    gender = fields.String(required=True)
    
    
    
    birth_date = fields.Date()
    
    
    
    phone = fields.String(required=True)
    
    
    
    address = fields.String()
    
    
    
    position = fields.String(required=True)
    
    
    
    department = fields.String(required=True)
    
    
    
    photo = fields.String()
    
    
    
    status = fields.Integer(required=True)
    
    
    
    entry_date = fields.Date(required=True)
    
    
    
    leave_date = fields.Date()
    
    
    
    area_id = fields.Integer()
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    
    
    last_health_check_date = fields.Date()
    
    
    
    health_status = fields.String()
    
    
    
    responsible_areas = fields.String()
    
    
    
    food_safety_certifications = fields.String()
    
    
    
    safety_violation_count = fields.Integer()
    
    
    
    user_id = fields.Integer()
    
    

    class Meta:
        ordered = True
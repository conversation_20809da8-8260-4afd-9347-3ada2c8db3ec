<template>
  <div class="page-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>TrainingRecord管理</span>
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增
          </el-button>
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form :model="searchForm" inline class="search-form">
        
        
        <el-form-item label="">
          <el-input v-model="searchForm." placeholder="请输入" clearable />
        </el-form-item>
        
        
        
        <el-form-item label="">
          <el-input v-model="searchForm." placeholder="请输入" clearable />
        </el-form-item>
        
        
        
        <el-form-item label="">
          <el-input v-model="searchForm." placeholder="请输入" clearable />
        </el-form-item>
        
        
        
        <el-form-item label="">
          <el-input v-model="searchForm." placeholder="请输入" clearable />
        </el-form-item>
        
        
        
        <el-form-item label="">
          <el-input v-model="searchForm." placeholder="请输入" clearable />
        </el-form-item>
        
        
        
        <el-form-item label="">
          <el-input v-model="searchForm." placeholder="请输入" clearable />
        </el-form-item>
        
        
        
        <el-form-item label="">
          <el-input v-model="searchForm." placeholder="请输入" clearable />
        </el-form-item>
        
        
        
        <el-form-item label="">
          <el-input v-model="searchForm." placeholder="请输入" clearable />
        </el-form-item>
        
        
        
        <el-form-item label="">
          <el-input v-model="searchForm." placeholder="请输入" clearable />
        </el-form-item>
        
        
        
        <el-form-item label="">
          <el-input v-model="searchForm." placeholder="请输入" clearable />
        </el-form-item>
        
        
        
        <el-form-item label="">
          <el-input v-model="searchForm." placeholder="请输入" clearable />
        </el-form-item>
        
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table :data="tableData" v-loading="loading" stripe>
        
        <el-table-column prop="" label="" />
        
        <el-table-column prop="" label="" />
        
        <el-table-column prop="" label="" />
        
        <el-table-column prop="" label="" />
        
        <el-table-column prop="" label="" />
        
        <el-table-column prop="" label="" />
        
        <el-table-column prop="" label="" />
        
        <el-table-column prop="" label="" />
        
        <el-table-column prop="" label="" />
        
        <el-table-column prop="" label="" />
        
        <el-table-column prop="" label="" />
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)">查看</el-button>
            <el-button type="warning" size="small" @click="handleEdit(row)">编辑</el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.pageSize"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { useTrainingRecordStore } from '@/stores/trainingrecord'

const router = useRouter()
const trainingrecordStore = useTrainingRecordStore()

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const searchForm = reactive({
  
  
  : '',
  
  
  
  : '',
  
  
  
  : '',
  
  
  
  : '',
  
  
  
  : '',
  
  
  
  : '',
  
  
  
  : '',
  
  
  
  : '',
  
  
  
  : '',
  
  
  
  : '',
  
  
  
  : '',
  
  
})
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 方法
const fetchData = async () => {
  loading.value = true
  try {
    const result = await trainingrecordStore.fetchList({
      page: pagination.page,
      per_page: pagination.pageSize,
      ...searchForm
    })
    tableData.value = result.items
    pagination.total = result.total
  } catch (error) {
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  fetchData()
}

const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  handleSearch()
}

const handleAdd = () => {
  router.push({ name: 'TrainingRecordForm' })
}

const handleView = (row) => {
  router.push({ name: 'TrainingRecordDetail', params: { id: row.id } })
}

const handleEdit = (row) => {
  router.push({ name: 'TrainingRecordForm', params: { id: row.id } })
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除这条记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await trainingrecordStore.delete(row.id)
    ElMessage.success('删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  fetchData()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  fetchData()
}

// 生命周期
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  margin-bottom: 20px;
}
</style>
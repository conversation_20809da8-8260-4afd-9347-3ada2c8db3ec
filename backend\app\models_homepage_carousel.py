"""
首页轮播图模型
用于管理首页的图片轮播展示
"""

from datetime import datetime
from app import db
from sqlalchemy.dialects.mssql import DATETIME2


class HomepageCarousel(db.Model):
    """首页轮播图模型"""
    __tablename__ = 'homepage_carousel'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    title = db.Column(db.String(100), nullable=False, comment='图片标题')
    description = db.Column(db.String(500), nullable=True, comment='图片描述')
    image_path = db.Column(db.String(255), nullable=False, comment='图片路径')
    link_url = db.Column(db.String(255), nullable=True, comment='点击链接')
    sort_order = db.Column(db.Integer, nullable=False, default=0, comment='排序顺序')
    is_active = db.Column(db.<PERSON>, nullable=False, default=True, comment='是否启用')
    created_by = db.Column(db.Integer, db.<PERSON>ey('users.id'), nullable=True, comment='创建人ID')
    created_at = db.Column(DATETIME2(1), nullable=False, default=lambda: datetime.now().replace(microsecond=0), comment='创建时间')
    updated_at = db.Column(DATETIME2(1), nullable=False, default=lambda: datetime.now().replace(microsecond=0),
                          onupdate=lambda: datetime.now().replace(microsecond=0), comment='更新时间')

    def __repr__(self):
        return f'<HomepageCarousel {self.title}>'

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'image_path': self.image_path,
            'link_url': self.link_url,
            'sort_order': self.sort_order,
            'is_active': self.is_active,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M') if self.created_at else '',
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M') if self.updated_at else ''
        }

    @classmethod
    def get_active_images(cls, limit=None):
        """获取启用的轮播图片，按排序顺序"""
        query = cls.query.filter_by(is_active=True).order_by(cls.sort_order.asc(), cls.created_at.desc())
        if limit:
            query = query.limit(limit)
        return query.all()

    @classmethod
    def get_next_sort_order(cls):
        """获取下一个排序号"""
        max_order = db.session.query(db.func.max(cls.sort_order)).scalar()
        return (max_order or 0) + 1

    @staticmethod
    def allowed_file(filename):
        """检查文件类型是否允许"""
        ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
        return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

#!/usr/bin/env python3
"""
复制现有项目的模型文件到后端项目
"""

import os
import shutil
from pathlib import Path

def copy_models():
    """复制模型文件"""
    print("📋 复制现有项目的模型文件...")

    # 源目录和目标目录
    source_dir = Path('../app')
    target_dir = Path('app/models')

    # 检查源目录是否存在
    if not source_dir.exists():
        print(f"❌ 源目录不存在: {source_dir.absolute()}")
        return []

    # 确保目标目录存在
    target_dir.mkdir(parents=True, exist_ok=True)

    # 需要复制的模型文件
    model_files = [
        'models.py'
    ]

    copied_files = []

    for model_file in model_files:
        source_file = source_dir / model_file
        target_file = target_dir / model_file

        if source_file.exists():
            try:
                shutil.copy2(source_file, target_file)
                copied_files.append(model_file)
                print(f"✅ 复制: {model_file}")
            except Exception as e:
                print(f"❌ 复制失败 {model_file}: {e}")
        else:
            print(f"⚠️ 文件不存在: {model_file}")

    # 创建 __init__.py 文件
    init_file = target_dir / '__init__.py'
    with open(init_file, 'w', encoding='utf-8') as f:
        f.write('"""模型包"""\n')

    print(f"\n📊 总计复制了 {len(copied_files)} 个模型文件")
    return copied_files

def main():
    """主函数"""
    print("🚀 开始复制模型文件")
    print("=" * 50)

    try:
        # 复制模型文件
        copied_files = copy_models()

        print("\n" + "=" * 50)
        print("🎉 模型文件复制完成！")
        print(f"✅ 复制了 {len(copied_files)} 个模型文件")

        return True

    except Exception as e:
        print(f"❌ 复制过程中出错: {e}")
        return False

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)

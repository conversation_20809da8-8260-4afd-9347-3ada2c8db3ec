import request from '@/utils/request'

const databasebackupAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/databasebackup',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/databasebackup/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/databasebackup',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/databasebackup/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/databasebackup/${id}`,
      method: 'delete'
    })
  }
}

export default databasebackupAPI
#!/usr/bin/env python3
"""
API 生成器
基于现有的 SQLAlchemy 模型自动生成 Flask-RESTful API
"""

import os
import ast
import re
from pathlib import Path
from typing import Dict, List, Any
from jinja2 import Template

class APIGenerator:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.models = []
        self.api_templates = {
            'resource': self._get_resource_template(),
            'schema': self._get_schema_template(),
            'service': self._get_service_template(),
            'init': self._get_init_template()
        }

    def generate_apis(self, output_dir: str = 'backend'):
        """生成所有 API"""
        print("🚀 开始生成 API...")

        # 解析模型
        self._parse_models()

        # 创建输出目录
        output_path = self.project_root / output_dir
        self._create_directory_structure(output_path)

        # 生成 API 文件
        for model in self.models:
            self._generate_model_api(model, output_path)

        # 生成主要配置文件
        self._generate_main_files(output_path)

        print(f"✅ API 生成完成，输出目录: {output_path}")

    def _parse_models(self):
        """解析现有模型"""
        print("📋 解析现有模型...")

        model_files = list(self.project_root.glob('app/models*.py'))

        for model_file in model_files:
            try:
                with open(model_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                tree = ast.parse(content)

                for node in ast.walk(tree):
                    if isinstance(node, ast.ClassDef):
                        # 检查是否继承自 db.Model
                        for base in node.bases:
                            if (isinstance(base, ast.Attribute) and
                                isinstance(base.value, ast.Name) and
                                base.value.id == 'db' and base.attr == 'Model'):

                                model_info = {
                                    'name': node.name,
                                    'file': model_file.name,
                                    'fields': self._extract_model_fields(node),
                                    'table_name': self._get_table_name(node.name),
                                    'api_name': self._get_api_name(node.name)
                                }

                                # 跳过抽象模型
                                if model_info['name'] != 'StandardModel':
                                    self.models.append(model_info)
                                break
            except Exception as e:
                print(f"⚠️ 解析模型文件 {model_file} 时出错: {e}")

    def _extract_model_fields(self, class_node) -> List[Dict[str, Any]]:
        """提取模型字段详细信息"""
        fields = []
        for node in class_node.body:
            if isinstance(node, ast.Assign):
                for target in node.targets:
                    if isinstance(target, ast.Name):
                        # 检查是否是 db.Column
                        if (isinstance(node.value, ast.Call) and
                            isinstance(node.value.func, ast.Attribute) and
                            isinstance(node.value.func.value, ast.Name) and
                            node.value.func.value.id == 'db' and
                            node.value.func.attr == 'Column'):

                            field_info = {
                                'name': target.id,
                                'type': self._get_field_type(node.value),
                                'nullable': self._is_field_nullable(node.value),
                                'primary_key': self._is_primary_key(node.value)
                            }
                            fields.append(field_info)
        return fields

    def _get_field_type(self, column_call) -> str:
        """获取字段类型"""
        if column_call.args:
            arg = column_call.args[0]
            if isinstance(arg, ast.Attribute):
                return arg.attr
            elif isinstance(arg, ast.Name):
                return arg.id
        return 'String'

    def _is_field_nullable(self, column_call) -> bool:
        """检查字段是否可为空"""
        for keyword in column_call.keywords:
            if keyword.arg == 'nullable':
                if isinstance(keyword.value, ast.Constant):
                    return keyword.value.value
        return True

    def _is_primary_key(self, column_call) -> bool:
        """检查是否为主键"""
        for keyword in column_call.keywords:
            if keyword.arg == 'primary_key':
                if isinstance(keyword.value, ast.Constant):
                    return keyword.value.value
        return False

    def _get_table_name(self, model_name: str) -> str:
        """获取表名"""
        # 将驼峰命名转换为下划线命名
        return re.sub(r'(?<!^)(?=[A-Z])', '_', model_name).lower()

    def _get_api_name(self, model_name: str) -> str:
        """获取 API 名称"""
        return model_name.lower()

    def _create_directory_structure(self, output_path: Path):
        """创建目录结构"""
        directories = [
            'app',
            'app/api',
            'app/api/v1',
            'app/api/v1/resources',
            'app/api/v1/schemas',
            'app/services',
            'app/models',
            'app/utils'
        ]

        for directory in directories:
            (output_path / directory).mkdir(parents=True, exist_ok=True)

    def _generate_model_api(self, model: Dict[str, Any], output_path: Path):
        """为单个模型生成 API"""
        print(f"📝 生成 {model['name']} API...")

        # 生成资源文件
        resource_content = self.api_templates['resource'].render(model=model)
        resource_file = output_path / 'app' / 'api' / 'v1' / 'resources' / f"{model['api_name']}.py"
        with open(resource_file, 'w', encoding='utf-8') as f:
            f.write(resource_content)

        # 生成序列化模式文件
        schema_content = self.api_templates['schema'].render(model=model)
        schema_file = output_path / 'app' / 'api' / 'v1' / 'schemas' / f"{model['api_name']}.py"
        with open(schema_file, 'w', encoding='utf-8') as f:
            f.write(schema_content)

        # 生成服务文件
        service_content = self.api_templates['service'].render(model=model)
        service_file = output_path / 'app' / 'services' / f"{model['api_name']}_service.py"
        with open(service_file, 'w', encoding='utf-8') as f:
            f.write(service_content)

    def _generate_main_files(self, output_path: Path):
        """生成主要配置文件"""
        print("📄 生成主要配置文件...")

        # 生成 API 初始化文件
        init_content = self.api_templates['init'].render(models=self.models)
        init_file = output_path / 'app' / 'api' / 'v1' / '__init__.py'
        with open(init_file, 'w', encoding='utf-8') as f:
            f.write(init_content)

        # 生成主应用文件
        app_content = self._get_app_template().render(models=self.models)
        app_file = output_path / 'app' / '__init__.py'
        with open(app_file, 'w', encoding='utf-8') as f:
            f.write(app_content)

        # 生成配置文件
        config_content = self._get_config_template().render()
        config_file = output_path / 'config.py'
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(config_content)

        # 生成运行文件
        run_content = self._get_run_template().render()
        run_file = output_path / 'run.py'
        with open(run_file, 'w', encoding='utf-8') as f:
            f.write(run_content)

        # 生成依赖文件
        requirements_content = self._get_requirements_template().render()
        requirements_file = output_path / 'requirements.txt'
        with open(requirements_file, 'w', encoding='utf-8') as f:
            f.write(requirements_content)

    def _get_resource_template(self) -> Template:
        """获取资源模板"""
        template_str = '''"""
{{ model.name }} API 资源
"""

from flask import request
from flask_restful import Resource
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.services.{{ model.api_name }}_service import {{ model.name }}Service
from app.api.v1.schemas.{{ model.api_name }} import {{ model.name }}Schema
from app.utils.response import success_response, error_response

class {{ model.name }}ListAPI(Resource):
    """{{ model.name }} 列表 API"""

    @jwt_required()
    def get(self):
        """获取{{ model.name }}列表"""
        try:
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 10, type=int)

            result = {{ model.name }}Service.get_list(page=page, per_page=per_page)

            schema = {{ model.name }}Schema(many=True)
            return success_response(
                data={
                    'items': schema.dump(result.items),
                    'total': result.total,
                    'page': result.page,
                    'per_page': result.per_page
                }
            )
        except Exception as e:
            return error_response(message=str(e))

    @jwt_required()
    def post(self):
        """创建{{ model.name }}"""
        try:
            data = request.get_json()
            schema = {{ model.name }}Schema()

            # 验证数据
            validated_data = schema.load(data)

            # 创建记录
            item = {{ model.name }}Service.create(validated_data)

            return success_response(
                data=schema.dump(item),
                message='创建成功'
            )
        except Exception as e:
            return error_response(message=str(e))

class {{ model.name }}API(Resource):
    """{{ model.name }} 详情 API"""

    @jwt_required()
    def get(self, id):
        """获取{{ model.name }}详情"""
        try:
            item = {{ model.name }}Service.get_by_id(id)
            if not item:
                return error_response(message='记录不存在', code=404)

            schema = {{ model.name }}Schema()
            return success_response(data=schema.dump(item))
        except Exception as e:
            return error_response(message=str(e))

    @jwt_required()
    def put(self, id):
        """更新{{ model.name }}"""
        try:
            data = request.get_json()
            schema = {{ model.name }}Schema()

            # 验证数据
            validated_data = schema.load(data, partial=True)

            # 更新记录
            item = {{ model.name }}Service.update(id, validated_data)
            if not item:
                return error_response(message='记录不存在', code=404)

            return success_response(
                data=schema.dump(item),
                message='更新成功'
            )
        except Exception as e:
            return error_response(message=str(e))

    @jwt_required()
    def delete(self, id):
        """删除{{ model.name }}"""
        try:
            success = {{ model.name }}Service.delete(id)
            if not success:
                return error_response(message='记录不存在', code=404)

            return success_response(message='删除成功')
        except Exception as e:
            return error_response(message=str(e))
'''
        return Template(template_str)

    def _get_schema_template(self) -> Template:
        """获取序列化模式模板"""
        template_str = '''"""
{{ model.name }} 序列化模式
"""

from marshmallow import Schema, fields, validate

class {{ model.name }}Schema(Schema):
    """{{ model.name }} 序列化模式"""

    {% for field in model.fields %}
    {% if field.type == 'Integer' %}
    {{ field.name }} = fields.Integer({% if not field.nullable %}required=True{% endif %})
    {% elif field.type == 'String' %}
    {{ field.name }} = fields.String({% if not field.nullable %}required=True{% endif %})
    {% elif field.type == 'Text' %}
    {{ field.name }} = fields.String({% if not field.nullable %}required=True{% endif %})
    {% elif field.type == 'DateTime' %}
    {{ field.name }} = fields.DateTime({% if not field.nullable %}required=True{% endif %})
    {% elif field.type == 'Date' %}
    {{ field.name }} = fields.Date({% if not field.nullable %}required=True{% endif %})
    {% elif field.type == 'Boolean' %}
    {{ field.name }} = fields.Boolean({% if not field.nullable %}required=True{% endif %})
    {% elif field.type == 'Float' %}
    {{ field.name }} = fields.Float({% if not field.nullable %}required=True{% endif %})
    {% elif field.type == 'Numeric' %}
    {{ field.name }} = fields.Decimal({% if not field.nullable %}required=True{% endif %})
    {% else %}
    {{ field.name }} = fields.Raw({% if not field.nullable %}required=True{% endif %})
    {% endif %}
    {% endfor %}

    class Meta:
        ordered = True
'''
        return Template(template_str)

    def _get_service_template(self) -> Template:
        """获取服务模板"""
        template_str = '''"""
{{ model.name }} 服务层
"""

from typing import Optional, Dict, Any
from sqlalchemy.orm import Session
from app.models.{{ model.file.replace('.py', '') }} import {{ model.name }}
from app.utils.database import get_db_session

class {{ model.name }}Service:
    """{{ model.name }} 服务"""

    @staticmethod
    def get_list(page: int = 1, per_page: int = 10, **filters):
        """获取{{ model.name }}列表"""
        with get_db_session() as session:
            query = session.query({{ model.name }})

            # 应用过滤条件
            for key, value in filters.items():
                if hasattr({{ model.name }}, key) and value is not None:
                    query = query.filter(getattr({{ model.name }}, key) == value)

            return query.paginate(
                page=page,
                per_page=per_page,
                error_out=False
            )

    @staticmethod
    def get_by_id(id: int) -> Optional[{{ model.name }}]:
        """根据ID获取{{ model.name }}"""
        with get_db_session() as session:
            return session.query({{ model.name }}).filter({{ model.name }}.id == id).first()

    @staticmethod
    def create(data: Dict[str, Any]) -> {{ model.name }}:
        """创建{{ model.name }}"""
        with get_db_session() as session:
            item = {{ model.name }}(**data)
            session.add(item)
            session.commit()
            session.refresh(item)
            return item

    @staticmethod
    def update(id: int, data: Dict[str, Any]) -> Optional[{{ model.name }}]:
        """更新{{ model.name }}"""
        with get_db_session() as session:
            item = session.query({{ model.name }}).filter({{ model.name }}.id == id).first()
            if not item:
                return None

            for key, value in data.items():
                if hasattr(item, key):
                    setattr(item, key, value)

            session.commit()
            session.refresh(item)
            return item

    @staticmethod
    def delete(id: int) -> bool:
        """删除{{ model.name }}"""
        with get_db_session() as session:
            item = session.query({{ model.name }}).filter({{ model.name }}.id == id).first()
            if not item:
                return False

            session.delete(item)
            session.commit()
            return True
'''
        return Template(template_str)

    def _get_init_template(self) -> Template:
        """获取初始化模板"""
        template_str = '''"""
API v1 初始化
"""

from flask import Blueprint
from flask_restful import Api

# 创建蓝图
api_v1_bp = Blueprint('api_v1', __name__)
api = Api(api_v1_bp)

# 导入资源
{% for model in models %}
from app.api.v1.resources.{{ model.api_name }} import {{ model.name }}ListAPI, {{ model.name }}API
{% endfor %}

# 注册路由
{% for model in models %}
api.add_resource({{ model.name }}ListAPI, '/{{ model.api_name }}')
api.add_resource({{ model.name }}API, '/{{ model.api_name }}/<int:id>')
{% endfor %}
'''
        return Template(template_str)

    def _get_app_template(self) -> Template:
        """获取应用模板"""
        template_str = '''"""
Flask 应用工厂
"""

from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_jwt_extended import JWTManager
from flask_cors import CORS
from config import Config

# 初始化扩展
db = SQLAlchemy()
jwt = JWTManager()

def create_app(config_class=Config):
    """创建 Flask 应用"""
    app = Flask(__name__)
    app.config.from_object(config_class)

    # 初始化扩展
    db.init_app(app)
    jwt.init_app(app)
    CORS(app)

    # 注册蓝图
    from app.api.v1 import api_v1_bp
    app.register_blueprint(api_v1_bp, url_prefix='/api/v1')

    # 导入模型
    {% for model in models %}
    from app.models.{{ model.file.replace('.py', '') }} import {{ model.name }}
    {% endfor %}

    return app
'''
        return Template(template_str)

    def _get_config_template(self) -> Template:
        """获取配置模板"""
        template_str = '''"""
应用配置
"""

import os
from datetime import timedelta

class Config:
    """基础配置"""
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key'

    # 数据库配置
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///app.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # JWT 配置
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY') or 'jwt-secret-key'
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=24)
    JWT_REFRESH_TOKEN_EXPIRES = timedelta(days=30)

    # CORS 配置
    CORS_ORIGINS = ['http://localhost:3000', 'http://localhost:8080']

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False

config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
'''
        return Template(template_str)

    def _get_run_template(self) -> Template:
        """获取运行模板"""
        template_str = '''"""
应用启动文件
"""

from app import create_app

app = create_app()

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5001)
'''
        return Template(template_str)

    def _get_requirements_template(self) -> Template:
        """获取依赖模板"""
        template_str = '''Flask==3.0.3
Flask-RESTful==0.3.10
Flask-SQLAlchemy==3.1.1
Flask-JWT-Extended==4.6.0
Flask-CORS==5.0.0
marshmallow==3.21.3
python-dotenv==1.0.1
pyodbc==5.1.0
'''
        return Template(template_str)

if __name__ == '__main__':
    # 生成 API
    generator = APIGenerator('.')
    generator.generate_apis()

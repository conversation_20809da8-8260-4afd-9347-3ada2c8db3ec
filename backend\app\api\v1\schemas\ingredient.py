"""
Ingredient 序列化模式
"""

from marshmallow import Schema, fields, validate

class IngredientSchema(Schema):
    """Ingredient 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    name = fields.String(required=True)
    
    
    
    category = fields.String(required=True)
    
    
    
    category_id = fields.Integer()
    
    
    
    area_id = fields.Integer()
    
    
    
    unit = fields.String(required=True)
    
    
    
    standard_unit = fields.String()
    
    
    
    base_image = fields.String()
    
    
    
    storage_temp = fields.String()
    
    
    
    storage_condition = fields.String()
    
    
    
    shelf_life = fields.Integer()
    
    
    
    specification = fields.String()
    
    
    
    nutrition_info = fields.String()
    
    
    
    is_condiment = fields.Boolean(required=True)
    
    
    
    is_global = fields.Boolean(required=True)
    
    
    
    status = fields.Integer(required=True)
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
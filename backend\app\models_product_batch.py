"""
产品批量上架模块的数据库模型
"""
from datetime import datetime
from app import db
from sqlalchemy.dialects.mssql import DATETIME2
from app.models import User, IngredientCategory, Supplier, SupplierProduct

class StandardUnit(db.Model):
    """标准单位表"""
    __tablename__ = 'standard_units'

    id = db.Column(db.Integer, primary_key=1)
    name = db.Column(db.String(50), nullable=0)
    symbol = db.Column(db.String(10), nullable=0)
    unit_type = db.Column(db.String(20), nullable=0)  # 'weight', 'volume', 'package', 'count'
    is_default = db.Column(db.<PERSON>, default=0, nullable=0)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=0)

    # 关系
    category_mappings = db.relationship('CategoryUnitMapping', backref='unit', lazy='dynamic')

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'symbol': self.symbol,
            'unit_type': self.unit_type,
            'is_default': self.is_default
        }

class CategoryUnitMapping(db.Model):
    """分类-单位映射表"""
    __tablename__ = 'category_unit_mappings'

    id = db.Column(db.Integer, primary_key=1)
    category_id = db.Column(db.Integer, db.ForeignKey('ingredient_categories.id'), nullable=0)
    unit_id = db.Column(db.Integer, db.ForeignKey('standard_units.id'), nullable=0)
    is_primary = db.Column(db.Boolean, default=0, nullable=0)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=0)

    # 关系
    category = db.relationship('IngredientCategory', backref='unit_mappings')

    def to_dict(self):
        return {
            'id': self.id,
            'category_id': self.category_id,
            'category_name': self.category.name if self.category else None,
            'unit_id': self.unit_id,
            'unit_name': self.unit.name if self.unit else None,
            'is_primary': self.is_primary
        }

class ProductBatch(db.Model):
    """产品批次表"""
    __tablename__ = 'product_batches'

    id = db.Column(db.Integer, primary_key=1)
    name = db.Column(db.String(100), nullable=0)  # 批次号/名称，自动生成
    category_id = db.Column(db.Integer, db.ForeignKey('ingredient_categories.id'), nullable=0)
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'), nullable=0)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=0)
    status = db.Column(db.String(20), default='pending', nullable=0)  # 'pending', 'approved', 'shelved', 'rejected'
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=0)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=0)

    # 关系
    category = db.relationship('IngredientCategory', backref='product_batches')
    supplier = db.relationship('Supplier', backref='product_batches')
    creator = db.relationship('User', backref='product_batches')
    # 明确指定外键关系
    products = db.relationship('SupplierProduct',
                              foreign_keys='SupplierProduct.batch_id',
                              backref='batch',
                              lazy='dynamic')

    def to_dict(self):
        # 安全地格式化日期时间
        def safe_format_datetime(dt):
            if dt and hasattr(dt, 'strftime'):
                return dt.strftime('%Y-%m-%d %H:%M:%S')
            return str(dt) if dt else None

        return {
            'id': self.id,
            'name': self.name,
            'category_id': self.category_id,
            'category_name': self.category.name if self.category else None,
            'supplier_id': self.supplier_id,
            'supplier_name': self.supplier.name if self.supplier else None,
            'created_by': self.created_by,
            'creator_name': self.creator.real_name or self.creator.username if self.creator else None,
            'status': self.status,
            'product_count': self.products.count(),
            'created_at': safe_format_datetime(self.created_at),
            'updated_at': safe_format_datetime(self.updated_at)
        }

"""
AuditLog 序列化模式
"""

from marshmallow import Schema, fields, validate

class AuditLogSchema(Schema):
    """AuditLog 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    user_id = fields.Integer(required=True)
    
    
    
    action = fields.String(required=True)
    
    
    
    resource_type = fields.String(required=True)
    
    
    
    resource_id = fields.Integer()
    
    
    
    area_id = fields.Integer()
    
    
    
    details = fields.String()
    
    
    
    ip_address = fields.String()
    
    
    
    user_agent = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
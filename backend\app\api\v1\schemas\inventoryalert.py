"""
InventoryAlert 序列化模式
"""

from marshmallow import Schema, fields, validate

class InventoryAlertSchema(Schema):
    """InventoryAlert 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    ingredient_id = fields.Integer(required=True)
    
    
    
    area_id = fields.Integer(required=True)
    
    
    
    min_quantity = fields.Float()
    
    
    
    max_quantity = fields.Float()
    
    
    
    current_quantity = fields.Float(required=True)
    
    
    
    unit = fields.String(required=True)
    
    
    
    alert_type = fields.String(required=True)
    
    
    
    status = fields.String(required=True)
    
    
    
    processed_by = fields.Integer()
    
    
    
    processed_at = fields.String()
    
    
    
    notes = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
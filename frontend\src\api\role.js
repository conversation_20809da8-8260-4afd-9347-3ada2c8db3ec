import request from '@/utils/request'

const roleAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/role',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/role/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/role',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/role/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/role/${id}`,
      method: 'delete'
    })
  }
}

export default roleAPI
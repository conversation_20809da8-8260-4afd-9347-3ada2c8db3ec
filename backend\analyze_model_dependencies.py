#!/usr/bin/env python3
"""
分析项目中的模型依赖关系
扫描所有 API、服务、资源文件，找出实际使用的模型
"""

import os
import re
from pathlib import Path
from collections import defaultdict

def analyze_model_dependencies():
    """分析模型依赖关系"""
    print("🔍 分析项目中的模型依赖关系...")
    
    # 需要扫描的目录
    scan_dirs = [
        'app/api/v1/resources',
        'app/api/v1/schemas', 
        'app/services'
    ]
    
    model_usage = defaultdict(set)  # 模型名 -> 使用它的文件集合
    import_patterns = []
    
    for scan_dir in scan_dirs:
        if os.path.exists(scan_dir):
            print(f"📂 扫描目录: {scan_dir}")
            analyze_directory(scan_dir, model_usage, import_patterns)
    
    # 分析结果
    print("\n📊 模型使用情况分析:")
    print("=" * 60)
    
    used_models = sorted(model_usage.keys())
    print(f"🎯 发现 {len(used_models)} 个被使用的模型:")
    
    for model in used_models:
        files = model_usage[model]
        print(f"  📋 {model}: 被 {len(files)} 个文件使用")
        for file in sorted(files):
            print(f"    - {file}")
    
    # 分析导入模式
    print(f"\n🔍 发现的导入模式:")
    unique_patterns = set(import_patterns)
    for pattern in sorted(unique_patterns):
        print(f"  - {pattern}")
    
    return used_models, model_usage

def analyze_directory(directory, model_usage, import_patterns):
    """分析目录中的文件"""
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.py') and not file.startswith('__'):
                file_path = os.path.join(root, file)
                analyze_file(file_path, model_usage, import_patterns)

def analyze_file(file_path, model_usage, import_patterns):
    """分析单个文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找模型导入语句
        import_patterns_regex = [
            r'from app\.models\.models import (.+)',
            r'from app\.models\.models_\w+ import (.+)',
            r'from \.\.\.models\.models import (.+)',
            r'from \.\.\.models\.models_\w+ import (.+)',
        ]
        
        relative_path = file_path.replace('\\', '/')
        
        for pattern in import_patterns_regex:
            matches = re.findall(pattern, content)
            for match in matches:
                import_patterns.append(f"{pattern} -> {match}")
                
                # 解析导入的模型名
                models = parse_import_models(match)
                for model in models:
                    model_usage[model].add(relative_path)
        
        # 查找直接使用的模型类（可能没有显式导入）
        model_usage_patterns = [
            r'(\w+)\.query\.',
            r'(\w+)Service',
            r'class (\w+)API',
            r'class (\w+)Schema',
        ]
        
        for pattern in model_usage_patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                if is_likely_model_name(match):
                    model_usage[match].add(relative_path)
    
    except Exception as e:
        print(f"⚠️ 分析文件 {file_path} 时出错: {e}")

def parse_import_models(import_string):
    """解析导入字符串中的模型名"""
    models = []
    
    # 处理不同的导入格式
    if ',' in import_string:
        # from models import Model1, Model2, Model3
        parts = [part.strip() for part in import_string.split(',')]
    else:
        # from models import Model1
        parts = [import_string.strip()]
    
    for part in parts:
        # 移除 'as' 别名
        if ' as ' in part:
            part = part.split(' as ')[0].strip()
        
        # 移除括号
        part = part.strip('()')
        
        if part and not part.startswith('_'):
            models.append(part)
    
    return models

def is_likely_model_name(name):
    """判断是否可能是模型名"""
    # 排除常见的非模型名
    exclude_patterns = [
        'API', 'Schema', 'Service', 'List', 'Resource',
        'app', 'db', 'request', 'response', 'current_app',
        'Blueprint', 'Api', 'Resource', 'Marshmallow',
        'get', 'post', 'put', 'delete', 'patch',
        'str', 'int', 'float', 'bool', 'dict', 'list'
    ]
    
    if name in exclude_patterns:
        return False
    
    # 模型名通常是首字母大写的名词
    if name[0].isupper() and len(name) > 2:
        return True
    
    return False

def generate_priority_models_list(used_models):
    """生成优先级模型列表"""
    print("\n🎯 生成优先级模型列表:")
    print("=" * 60)
    
    # 核心模型（通常最重要）
    core_models = [
        'User', 'Role', 'UserRole',
        'Supplier', 'SupplierCategory',
        'Ingredient', 'IngredientCategory',
        'Recipe', 'RecipeCategory',
        'PurchaseOrder', 'PurchaseOrderItem',
        'Warehouse', 'Inventory'
    ]
    
    # 按优先级分组
    priority_groups = {
        'high': [],    # 核心模型且被使用
        'medium': [],  # 核心模型但未被使用
        'low': []      # 其他被使用的模型
    }
    
    for model in used_models:
        if model in core_models:
            priority_groups['high'].append(model)
        else:
            priority_groups['low'].append(model)
    
    for model in core_models:
        if model not in used_models:
            priority_groups['medium'].append(model)
    
    print("🔥 高优先级 (核心模型且被使用):")
    for model in sorted(priority_groups['high']):
        print(f"  ✅ {model}")
    
    print("\n🔶 中优先级 (核心模型但未被使用):")
    for model in sorted(priority_groups['medium']):
        print(f"  ⚠️ {model}")
    
    print("\n🔸 低优先级 (其他被使用的模型):")
    for model in sorted(priority_groups['low']):
        print(f"  📋 {model}")
    
    return priority_groups

def main():
    """主函数"""
    print("🚀 开始分析模型依赖关系")
    print("=" * 60)
    
    try:
        used_models, model_usage = analyze_model_dependencies()
        priority_groups = generate_priority_models_list(used_models)
        
        print("\n" + "=" * 60)
        print("🎉 分析完成！")
        print(f"📊 总计发现 {len(used_models)} 个被使用的模型")
        print(f"🔥 高优先级模型: {len(priority_groups['high'])} 个")
        print(f"🔶 中优先级模型: {len(priority_groups['medium'])} 个") 
        print(f"🔸 低优先级模型: {len(priority_groups['low'])} 个")
        
        print("\n📝 建议:")
        print("1. 优先生成高优先级模型")
        print("2. 根据需要添加中优先级模型")
        print("3. 最后处理低优先级模型")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)

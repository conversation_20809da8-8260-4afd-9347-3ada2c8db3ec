import request from '@/utils/request'

const ingredientcategoryAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/ingredientcategory',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/ingredientcategory/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/ingredientcategory',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/ingredientcategory/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/ingredientcategory/${id}`,
      method: 'delete'
    })
  }
}

export default ingredientcategoryAPI
"""
ProductSpecParameter 服务层
"""

from typing import Optional, Dict, Any
from sqlalchemy.orm import Session
from app.models.models_supplier import ProductSpecParameter
from app.utils.database import get_db_session

class ProductSpecParameterService:
    """ProductSpecParameter 服务"""

    @staticmethod
    def get_list(page: int = 1, per_page: int = 10, **filters):
        """获取ProductSpecParameter列表"""
        with get_db_session() as session:
            query = session.query(ProductSpecParameter)

            # 应用过滤条件
            for key, value in filters.items():
                if hasattr(ProductSpecParameter, key) and value is not None:
                    query = query.filter(getattr(ProductSpecParameter, key) == value)

            return query.paginate(
                page=page,
                per_page=per_page,
                error_out=False
            )

    @staticmethod
    def get_by_id(id: int) -> Optional[ProductSpecParameter]:
        """根据ID获取ProductSpecParameter"""
        with get_db_session() as session:
            return session.query(ProductSpecParameter).filter(ProductSpecParameter.id == id).first()

    @staticmethod
    def create(data: Dict[str, Any]) -> ProductSpecParameter:
        """创建ProductSpecParameter"""
        with get_db_session() as session:
            item = ProductSpecParameter(**data)
            session.add(item)
            session.commit()
            session.refresh(item)
            return item

    @staticmethod
    def update(id: int, data: Dict[str, Any]) -> Optional[ProductSpecParameter]:
        """更新ProductSpecParameter"""
        with get_db_session() as session:
            item = session.query(ProductSpecParameter).filter(ProductSpecParameter.id == id).first()
            if not item:
                return None

            for key, value in data.items():
                if hasattr(item, key):
                    setattr(item, key, value)

            session.commit()
            session.refresh(item)
            return item

    @staticmethod
    def delete(id: int) -> bool:
        """删除ProductSpecParameter"""
        with get_db_session() as session:
            item = session.query(ProductSpecParameter).filter(ProductSpecParameter.id == id).first()
            if not item:
                return False

            session.delete(item)
            session.commit()
            return True
-- 清理 MenuPlan 和 MenuRecipe 相关表的 SQL 脚本
-- 执行前请确保已备份数据库

-- 1. 删除 menu_recipes 表（子表，包含外键）
IF OBJECT_ID('menu_recipes', 'U') IS NOT NULL
BEGIN
    PRINT '正在删除 menu_recipes 表...'
    DROP TABLE menu_recipes;
    PRINT 'menu_recipes 表已删除'
END
ELSE
BEGIN
    PRINT 'menu_recipes 表不存在，跳过删除'
END

-- 2. 删除 menu_plans 表（主表）
IF OBJECT_ID('menu_plans', 'U') IS NOT NULL
BEGIN
    PRINT '正在删除 menu_plans 表...'
    DROP TABLE menu_plans;
    PRINT 'menu_plans 表已删除'
END
ELSE
BEGIN
    PRINT 'menu_plans 表不存在，跳过删除'
END

-- 3. 检查是否还有相关的索引或约束需要清理
-- 查询可能残留的相关对象
SELECT 
    'INDEX' as object_type,
    i.name as object_name,
    t.name as table_name
FROM sys.indexes i
INNER JOIN sys.tables t ON i.object_id = t.object_id
WHERE t.name IN ('menu_plans', 'menu_recipes')
    AND i.name IS NOT NULL

UNION ALL

SELECT 
    'CONSTRAINT' as object_type,
    c.name as object_name,
    t.name as table_name
FROM sys.check_constraints c
INNER JOIN sys.tables t ON c.parent_object_id = t.object_id
WHERE t.name IN ('menu_plans', 'menu_recipes')

UNION ALL

SELECT 
    'FOREIGN_KEY' as object_type,
    fk.name as object_name,
    t.name as table_name
FROM sys.foreign_keys fk
INNER JOIN sys.tables t ON fk.parent_object_id = t.object_id
WHERE t.name IN ('menu_plans', 'menu_recipes')
    OR fk.name LIKE '%menu_plan%' 
    OR fk.name LIKE '%menu_recipe%';

PRINT '数据库清理完成！'
PRINT '请检查上述查询结果，如果有残留对象请手动清理'

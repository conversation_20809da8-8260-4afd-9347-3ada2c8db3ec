import request from '@/utils/request'

const employeeAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/employee',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/employee/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/employee',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/employee/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/employee/${id}`,
      method: 'delete'
    })
  }
}

export default employeeAPI
"""
RecipeIngredient 服务层
"""

from typing import Optional, Dict, Any
from sqlalchemy.orm import Session
from app.models.models import RecipeIngredient
from app.utils.database import get_db_session

class RecipeIngredientService:
    """RecipeIngredient 服务"""

    @staticmethod
    def get_list(page: int = 1, per_page: int = 10, **filters):
        """获取RecipeIngredient列表"""
        with get_db_session() as session:
            query = session.query(RecipeIngredient)

            # 应用过滤条件
            for key, value in filters.items():
                if hasattr(RecipeIngredient, key) and value is not None:
                    query = query.filter(getattr(RecipeIngredient, key) == value)

            return query.paginate(
                page=page,
                per_page=per_page,
                error_out=False
            )

    @staticmethod
    def get_by_id(id: int) -> Optional[RecipeIngredient]:
        """根据ID获取RecipeIngredient"""
        with get_db_session() as session:
            return session.query(RecipeIngredient).filter(RecipeIngredient.id == id).first()

    @staticmethod
    def create(data: Dict[str, Any]) -> RecipeIngredient:
        """创建RecipeIngredient"""
        with get_db_session() as session:
            item = RecipeIngredient(**data)
            session.add(item)
            session.commit()
            session.refresh(item)
            return item

    @staticmethod
    def update(id: int, data: Dict[str, Any]) -> Optional[RecipeIngredient]:
        """更新RecipeIngredient"""
        with get_db_session() as session:
            item = session.query(RecipeIngredient).filter(RecipeIngredient.id == id).first()
            if not item:
                return None

            for key, value in data.items():
                if hasattr(item, key):
                    setattr(item, key, value)

            session.commit()
            session.refresh(item)
            return item

    @staticmethod
    def delete(id: int) -> bool:
        """删除RecipeIngredient"""
        with get_db_session() as session:
            item = session.query(RecipeIngredient).filter(RecipeIngredient.id == id).first()
            if not item:
                return False

            session.delete(item)
            session.commit()
            return True
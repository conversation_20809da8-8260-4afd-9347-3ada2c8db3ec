"""
RecipeReviewImage 序列化模式
"""

from marshmallow import Schema, fields, validate

class RecipeReviewImageSchema(Schema):
    """RecipeReviewImage 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    review_id = fields.Integer(required=True)
    
    
    
    image_path = fields.String(required=True)
    
    
    
    created_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
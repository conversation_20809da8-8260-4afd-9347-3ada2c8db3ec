# 学校食堂管理系统迁移工具

这是一套完整的自动化迁移工具，用于将现有的 Flask + Bootstrap 单体应用迁移到 Vue 3 + Element Plus 前后端分离架构。

## 工具概述

### 🔍 项目分析器 (project_analyzer.py)
- 分析现有项目结构、依赖关系、数据库设计
- 统计代码规模和复杂度
- 生成详细的分析报告

### 🔧 API 生成器 (api_generator.py)
- 基于现有 SQLAlchemy 模型自动生成 Flask-RESTful API
- 生成资源类、序列化模式、服务层代码
- 支持标准 CRUD 操作

### 🎨 前端生成器 (frontend_generator.py)
- 生成 Vue 3 + Element Plus 前端项目
- 自动创建列表、表单、详情页面组件
- 生成 Pinia 状态管理和路由配置

### 🚀 主迁移脚本 (migrate.py)
- 整合所有迁移工具
- 提供统一的迁移入口
- 支持分步骤执行

## 快速开始

### 1. 安装依赖

```bash
pip install jinja2
```

### 2. 执行完整迁移

```bash
cd migration_tools
python migrate.py --mode full
```

### 3. 分步骤执行

```bash
# 仅执行项目分析
python migrate.py --mode analysis

# 仅生成后端 API
python migrate.py --mode backend

# 仅生成前端项目
python migrate.py --mode frontend
```

## 生成的文件结构

```
├── migration_analysis_report.json  # 项目分析报告
├── backend/                        # 后端 API 项目
│   ├── app/
│   │   ├── api/v1/
│   │   │   ├── resources/          # API 资源
│   │   │   └── schemas/            # 序列化模式
│   │   ├── services/               # 业务逻辑层
│   │   └── models/                 # 数据模型
│   ├── config.py                   # 配置文件
│   ├── run.py                      # 启动文件
│   └── requirements.txt            # 依赖文件
├── frontend/                       # 前端 Vue 项目
│   ├── src/
│   │   ├── api/                    # API 服务
│   │   ├── components/             # 组件
│   │   ├── views/                  # 页面
│   │   ├── stores/                 # 状态管理
│   │   └── router/                 # 路由配置
│   ├── package.json                # 依赖配置
│   └── vite.config.js              # 构建配置
├── deployment/                     # 部署配置
│   ├── nginx.conf                  # Nginx 配置
│   └── docker-compose.yml          # Docker 配置
└── docs/                          # 迁移文档
    └── quick-start.md              # 快速开始指南
```

## 迁移策略

### 1. 并存渐进式迁移
- 新旧系统同时运行
- 通过 Nginx 反向代理分流
- 逐步迁移功能模块

### 2. 技术栈对比

| 组件 | 现有系统 | 新系统 |
|------|----------|--------|
| 前端框架 | Bootstrap 5.3.6 + jQuery | Vue 3 + Element Plus |
| 后端框架 | Flask (单体) | Flask (API) |
| 数据库 | SQL Server | SQL Server (保持不变) |
| 缓存 | Redis | Redis (保持不变) |
| 部署 | 单体部署 | 前后端分离 |

### 3. 迁移优势
- **更好的用户体验**: 现代化的前端界面
- **更高的开发效率**: 前后端分离开发
- **更强的可扩展性**: 微服务架构
- **更好的维护性**: 代码结构清晰

## 使用说明

### 项目分析

```bash
python project_analyzer.py
```

生成的分析报告包含：
- 项目基本信息
- 依赖分析
- 模型统计
- 路由统计
- 迁移复杂度评估

### API 生成

```bash
python api_generator.py
```

为每个数据模型生成：
- RESTful API 资源类
- Marshmallow 序列化模式
- 业务逻辑服务层
- 统一的响应格式

### 前端生成

```bash
python frontend_generator.py
```

为每个数据模型生成：
- 列表页面 (支持搜索、分页、排序)
- 表单页面 (新增/编辑)
- 详情页面
- API 服务封装
- Pinia 状态管理

## 自定义配置

### 修改模板

所有生成的代码都基于 Jinja2 模板，可以根据需要修改模板：

1. 在对应的生成器中找到模板方法
2. 修改模板内容
3. 重新运行生成器

### 过滤模型

如果不需要为某些模型生成代码，可以在生成器中添加过滤逻辑：

```python
# 跳过特定模型
if model['name'] in ['StandardModel', 'LogModel']:
    continue
```

## 注意事项

1. **数据备份**: 迁移前请备份所有数据
2. **充分测试**: 在测试环境充分验证新系统
3. **用户培训**: 为用户提供新系统使用培训
4. **监控系统**: 部署后持续监控系统性能
5. **回滚准备**: 准备回滚方案以应对紧急情况

## 故障排除

### 常见问题

1. **模型解析失败**
   - 检查模型文件语法是否正确
   - 确保继承自 db.Model

2. **API 生成失败**
   - 检查 Jinja2 模板语法
   - 确保模型字段信息完整

3. **前端生成失败**
   - 检查分析报告是否存在
   - 确保模型信息格式正确

### 获取帮助

如果遇到问题，请：
1. 查看生成的日志信息
2. 检查分析报告内容
3. 验证项目结构是否符合预期

## 贡献指南

欢迎提交 Issue 和 Pull Request 来改进这些迁移工具。

## 许可证

MIT License

from flask_wtf import FlaskForm
from wtforms import StringField, SelectField, TextAreaField, HiddenField, SubmitField, BooleanField
from wtforms.validators import DataRequired, Length, Optional, ValidationError
from app.models import AdministrativeArea

class AreaForm(FlaskForm):
    """区域表单"""
    name = StringField('区域名称', validators=[DataRequired(message='请输入区域名称'), Length(min=2, max=100, message='区域名称长度必须在2-100个字符之间')])
    code = StringField('区域代码', validators=[DataRequired(message='请输入区域代码'), Length(min=2, max=50, message='区域代码长度必须在2-50个字符之间')])
    level = SelectField('区域级别', choices=[(1, '县市区'), (2, '乡镇'), (3, '学校'), (4, '食堂')], coerce=int, validators=[DataRequired(message='请选择区域级别')])
    parent_id = SelectField('上级区域', coerce=int, validators=[Optional()])
    description = TextAreaField('描述', validators=[Optional(), Length(max=500, message='描述长度不能超过500个字符')])
    is_township_school = BooleanField('是否为乡镇级别直接关联的学校')
    submit = SubmitField('提交')

    # 用于存储区域ID，用于编辑时验证唯一性
    area_id = None

    def validate_code(self, field):
        """验证区域代码唯一性"""
        area = AdministrativeArea.query.filter_by(code=field.data).first()
        if area and (not hasattr(self, 'area_id') or area.id != self.area_id):
            raise ValidationError('该区域代码已存在，请更换一个')

class MoveAreaForm(FlaskForm):
    """移动区域表单"""
    area_id = HiddenField('区域ID', validators=[DataRequired()])
    parent_id = SelectField('新的上级区域', coerce=int, validators=[DataRequired(message='请选择新的上级区域')])
    submit = SubmitField('移动')

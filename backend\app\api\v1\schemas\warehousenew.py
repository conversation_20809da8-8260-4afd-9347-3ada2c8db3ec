"""
WarehouseNew 序列化模式
"""

from marshmallow import Schema, fields, validate

class WarehouseNewSchema(Schema):
    """WarehouseNew 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    name = fields.String(required=True)
    
    
    
    area_id = fields.Integer(required=True)
    
    
    
    location = fields.String(required=True)
    
    
    
    manager_id = fields.Integer(required=True)
    
    
    
    capacity = fields.Float()
    
    
    
    capacity_unit = fields.String()
    
    
    
    temperature_range = fields.String()
    
    
    
    humidity_range = fields.String()
    
    
    
    status = fields.String(required=True)
    
    
    
    notes = fields.String()
    
    
    
    created_at = fields.DateTime(required=True)
    
    
    
    updated_at = fields.DateTime(required=True)
    
    

    class Meta:
        ordered = True
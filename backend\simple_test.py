#!/usr/bin/env python3
"""
简化的数据库连接测试脚本
不依赖生成的 API 代码
"""

import sys
import os
import urllib.parse
from sqlalchemy import create_engine, text

def test_connection():
    """直接测试数据库连接"""
    print("🔍 测试数据库连接...")
    print("=" * 50)

    # 数据库配置
    DB_SERVER = '14.103.246.164'
    DB_PORT = '1433'
    DB_DATABASE = 'StudentsCMSSP'
    DB_USERNAME = 'StudentsCMSSP'
    DB_PASSWORD = 'Xg2LS44Cyz5Zt8.'
    DB_DRIVER = 'ODBC Driver 17 for SQL Server'

    # 构建连接字符串
    password_encoded = urllib.parse.quote_plus(DB_PASSWORD)
    driver_encoded = urllib.parse.quote_plus(DB_DRIVER)

    connection_string = f"mssql+pyodbc://{DB_USERNAME}:{password_encoded}@{DB_SERVER}:{DB_PORT}/{DB_DATABASE}?driver={driver_encoded}&TrustServerCertificate=yes"

    print(f"📊 服务器: {DB_SERVER}:{DB_PORT}")
    print(f"🗄️ 数据库: {DB_DATABASE}")
    print(f"👤 用户: {DB_USERNAME}")
    print(f"🔑 密码: {'*' * len(DB_PASSWORD)}")
    print(f"🚗 驱动: {DB_DRIVER}")
    print()

    try:
        # 创建引擎
        engine = create_engine(
            connection_string,
            echo=False,
            pool_pre_ping=True,
            pool_recycle=300,
            connect_args={
                'timeout': 20,
                'check_same_thread': False
            }
        )

        # 测试连接
        with engine.connect() as connection:
            result = connection.execute(text("SELECT 1 as test"))
            row = result.fetchone()

            if row and row[0] == 1:
                print("✅ 数据库连接成功！")

                # 测试查询数据库信息
                result = connection.execute(text("SELECT @@VERSION as version"))
                version = result.fetchone()
                if version:
                    print(f"📋 SQL Server 版本: {version[0][:50]}...")

                # 测试查询表数量
                result = connection.execute(text("""
                    SELECT COUNT(*) as table_count
                    FROM INFORMATION_SCHEMA.TABLES
                    WHERE TABLE_TYPE = 'BASE TABLE'
                """))
                table_count = result.fetchone()
                if table_count:
                    print(f"📊 数据库表数量: {table_count[0]}")

                return True
            else:
                print("❌ 连接测试失败")
                return False

    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        print()
        print("🛠️ 可能的解决方案:")
        print("1. 检查服务器地址和端口是否正确")
        print("2. 确认用户名和密码是否正确")
        print("3. 检查防火墙设置")
        print("4. 确认 SQL Server 允许远程连接")
        print("5. 检查 ODBC Driver 17 for SQL Server 是否已安装")
        return False

if __name__ == '__main__':
    success = test_connection()
    sys.exit(0 if success else 1)

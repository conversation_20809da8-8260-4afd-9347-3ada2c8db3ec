import request from '@/utils/request'

const storagelocationAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/storagelocation',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/storagelocation/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/storagelocation',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/storagelocation/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/storagelocation/${id}`,
      method: 'delete'
    })
  }
}

export default storagelocationAPI
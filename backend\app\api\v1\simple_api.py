"""
简化的 API 端点 - 用于测试基本功能
"""

from flask import Blueprint, jsonify, request
from flask_restful import Api, Resource
from app.utils.response import success_response, error_response

# 创建简化的 API 蓝图
simple_api_bp = Blueprint('simple_api', __name__)
api = Api(simple_api_bp)

class HealthAPI(Resource):
    """健康检查 API"""
    
    def get(self):
        """获取服务状态"""
        return success_response(
            data={"status": "healthy", "version": "1.0.0"},
            message="API 服务运行正常"
        )

class DatabaseAPI(Resource):
    """数据库测试 API"""
    
    def get(self):
        """测试数据库连接"""
        from app.utils.database import test_connection
        
        if test_connection():
            return success_response(message="数据库连接正常")
        else:
            return error_response(message="数据库连接失败", code=500)

class UserAPI(Resource):
    """用户 API - 简化版本"""
    
    def get(self, user_id=None):
        """获取用户信息"""
        if user_id:
            # 模拟获取单个用户
            return success_response(
                data={
                    "id": user_id,
                    "username": f"user_{user_id}",
                    "email": f"user_{user_id}@example.com",
                    "created_at": "2025-06-17T21:54:21.364504"
                },
                message="获取用户信息成功"
            )
        else:
            # 模拟获取用户列表
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 10, type=int)
            
            users = []
            for i in range(1, per_page + 1):
                users.append({
                    "id": (page - 1) * per_page + i,
                    "username": f"user_{(page - 1) * per_page + i}",
                    "email": f"user_{(page - 1) * per_page + i}@example.com",
                    "created_at": "2025-06-17T21:54:21.364504"
                })
            
            return success_response(
                data={
                    "users": users,
                    "pagination": {
                        "page": page,
                        "per_page": per_page,
                        "total": 100,
                        "pages": 10
                    }
                },
                message="获取用户列表成功"
            )
    
    def post(self):
        """创建用户"""
        data = request.get_json()
        
        # 简单验证
        if not data or not data.get('username'):
            return error_response(message="用户名不能为空", code=400)
        
        # 模拟创建用户
        new_user = {
            "id": 999,
            "username": data.get('username'),
            "email": data.get('email'),
            "created_at": "2025-06-17T21:54:21.364504"
        }
        
        return success_response(
            data=new_user,
            message="创建用户成功",
            code=201
        )
    
    def put(self, user_id):
        """更新用户"""
        data = request.get_json()
        
        # 模拟更新用户
        updated_user = {
            "id": user_id,
            "username": data.get('username', f'user_{user_id}'),
            "email": data.get('email', f'user_{user_id}@example.com'),
            "updated_at": "2025-06-17T21:54:21.364504"
        }
        
        return success_response(
            data=updated_user,
            message="更新用户成功"
        )
    
    def delete(self, user_id):
        """删除用户"""
        return success_response(
            message=f"删除用户 {user_id} 成功"
        )

# 注册路由
api.add_resource(HealthAPI, '/health')
api.add_resource(DatabaseAPI, '/test-db')
api.add_resource(UserAPI, '/users', '/users/<int:user_id>')

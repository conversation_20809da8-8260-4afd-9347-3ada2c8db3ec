from flask import render_template, redirect, url_for, flash, request, jsonify, current_app
from flask_login import login_required, current_user
from app import db
from app.notification import notification_bp
from app.models import Notification, User, AdministrativeArea
from app.services.notification_service import NotificationService
from datetime import datetime

@notification_bp.route('/')
@login_required
def index():
    """通知中心页面"""
    page = request.args.get('page', 1, type=int)
    per_page = 20

    # 获取筛选参数
    filter_type = request.args.get('type', 'all')

    # 基础查询
    query = Notification.query.filter_by(user_id=current_user.id)

    # 应用筛选
    if filter_type == 'unread':
        query = query.filter_by(is_read=0)
    elif filter_type != 'all':
        query = query.filter_by(notification_type=filter_type)

    # 分页
    pagination = query.order_by(Notification.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=0
    )
    notifications = pagination.items

    # 获取通知类型统计
    notification_types = db.session.query(
        Notification.notification_type,
        db.func.count(Notification.id).label('count')
    ).filter_by(user_id=current_user.id).group_by(Notification.notification_type).all()

    type_counts = {t[0]: t[1] for t in notification_types}
    unread_count = Notification.query.filter_by(user_id=current_user.id, is_read=0).count()

    return render_template(
        'notification/index.html',
        title='通知中心',
        notifications=notifications,
        pagination=pagination,
        filter_type=filter_type,
        type_counts=type_counts,
        unread_count=unread_count,
        now=datetime.now()
    )

@notification_bp.route('/view/<int:id>')
@login_required
def view(id):
    """查看单个通知"""
    notification = Notification.query.get_or_404(id)

    # 检查权限
    if notification.user_id != current_user.id:
        flash('您没有权限查看此通知', 'danger')
        return redirect(url_for('notification.index'))

    # 标记为已读
    if not notification.is_read:
        notification.is_read = 1
        db.session.commit()

    # 根据通知类型和引用类型处理跳转
    if notification.reference_id and notification.reference_type:
        if notification.reference_type == 'health_certificate':
            return redirect(url_for('employee.view_health_certificate', id=notification.reference_id))
        elif notification.reference_type == 'menu':
            return redirect(url_for('menu.view', id=notification.reference_id))
        # 其他类型的引用...

    # 默认返回通知详情页
    return render_template(
        'notification/view.html',
        title='查看通知',
        notification=notification,
        now=datetime.now()
    )

@notification_bp.route('/mark_read/<int:id>')
@login_required
def mark_read(id):
    """标记通知为已读"""
    notification = Notification.query.get_or_404(id)

    # 检查权限
    if notification.user_id != current_user.id:
        flash('您没有权限操作此通知', 'danger')
        return redirect(url_for('notification.index'))

    notification.is_read = 1
    db.session.commit()

    flash('通知已标记为已读', 'success')
    return redirect(url_for('notification.index'))

@notification_bp.route('/mark_all_read')
@login_required
def mark_all_read():
    """标记所有通知为已读"""
    Notification.query.filter_by(user_id=current_user.id, is_read=0).update({'is_read': 1})
    db.session.commit()

    flash('所有通知已标记为已读', 'success')
    return redirect(url_for('notification.index'))

@notification_bp.route('/delete/<int:id>', methods=['POST'])
@login_required
def delete(id):
    """删除通知"""
    notification = Notification.query.get_or_404(id)

    # 检查权限
    if notification.user_id != current_user.id:
        flash('您没有权限删除此通知', 'danger')
        return redirect(url_for('notification.index'))

    db.session.delete(notification)
    db.session.commit()

    flash('通知已删除', 'success')
    return redirect(url_for('notification.index'))

@notification_bp.route('/delete_all', methods=['POST'])
@login_required
def delete_all():
    """删除所有通知"""
    Notification.query.filter_by(user_id=current_user.id).delete()
    db.session.commit()

    flash('所有通知已删除', 'success')
    return redirect(url_for('notification.index'))

@notification_bp.route('/check')
@login_required
def check():
    """检查新通知（用于AJAX请求）"""
    try:
        unread_count = current_user.unread_notifications_count
        recent = current_user.recent_notifications

        notifications = []
        for n in recent:
            try:
                # n 是字典，不是模型对象
                content = n.get('content', '')
                truncated_content = content[:50] + '...' if len(content) > 50 else content

                # 格式化时间
                created_at = n.get('created_at')
                if isinstance(created_at, str):
                    formatted_time = created_at
                else:
                    # 如果是datetime对象，格式化它
                    from datetime import datetime
                    now = datetime.now()
                    delta = now - created_at

                    if delta.days == 0:
                        if delta.seconds < 60:
                            formatted_time = "刚刚"
                        elif delta.seconds < 3600:
                            formatted_time = f"{delta.seconds // 60}分钟前"
                        else:
                            formatted_time = f"{delta.seconds // 3600}小时前"
                    elif delta.days == 1:
                        formatted_time = "昨天"
                    elif delta.days < 7:
                        formatted_time = f"{delta.days}天前"
                    else:
                        formatted_time = created_at.strftime('%Y-%m-%d')

                notifications.append({
                    'id': n.get('id', 0),
                    'title': n.get('title', ''),
                    'content': truncated_content,
                    'is_read': n.get('is_read', False),
                    'created_at': formatted_time,
                    'level': n.get('level', 0)
                })
            except Exception as e:
                # 跳过有问题的通知，继续处理其他通知
                current_app.logger.warning(f"处理通知时出错: {str(e)}")
                continue

        return jsonify({
            'unread_count': unread_count,
            'notifications': notifications
        })

    except Exception as e:
        # 记录错误并返回默认值
        current_app.logger.error(f"检查通知时出错: {str(e)}")
        return jsonify({
            'unread_count': 0,
            'notifications': []
        }), 200  # 返回200状态码而不是500，避免前端报错

@notification_bp.route('/send', methods=['GET', 'POST'])
@login_required
def send():
    """发送通知页面"""
    if request.method == 'POST':
        try:
            # 获取表单数据
            title = request.form.get('title', '').strip()
            content = request.form.get('content', '').strip()
            notification_type = request.form.get('notification_type', 'system')
            level = int(request.form.get('level', 0))
            send_type = request.form.get('send_type', 'user')  # user/area/all

            if not title or not content:
                flash('标题和内容不能为空', 'danger')
                return redirect(url_for('notification.send'))

            notifications = []

            if send_type == 'user':
                # 发送给指定用户
                user_ids = request.form.getlist('user_ids')
                if not user_ids:
                    flash('请选择要发送的用户', 'danger')
                    return redirect(url_for('notification.send'))

                for user_id in user_ids:
                    notification = NotificationService.send_to_user(
                        user_id=int(user_id),
                        title=title,
                        content=content,
                        notification_type=notification_type,
                        level=level
                    )
                    if notification:
                        notifications.append(notification)

            elif send_type == 'area':
                # 发送给指定区域
                area_id = request.form.get('area_id')
                if not area_id:
                    flash('请选择要发送的区域', 'danger')
                    return redirect(url_for('notification.send'))

                include_sub_areas = request.form.get('include_sub_areas') == 'on'
                notifications = NotificationService.send_to_area(
                    area_id=int(area_id),
                    title=title,
                    content=content,
                    notification_type=notification_type,
                    level=level,
                    include_sub_areas=include_sub_areas
                )

            elif send_type == 'all':
                # 发送给所有用户
                notifications = NotificationService.send_to_all(
                    title=title,
                    content=content,
                    notification_type=notification_type,
                    level=level
                )

            if notifications:
                flash(f'通知发送成功，共发送给 {len(notifications)} 个用户', 'success')
            else:
                flash('通知发送失败', 'danger')

        except Exception as e:
            flash(f'发送通知时出错: {str(e)}', 'danger')

        return redirect(url_for('notification.send'))

    # GET请求，显示发送通知页面
    users = User.query.filter_by(status=1).all()
    areas = AdministrativeArea.query.all()

    return render_template(
        'notification/send.html',
        title='发送通知',
        users=users,
        areas=areas,
        notification_types=get_notification_types(),
        notification_levels=get_notification_levels()
    )

@notification_bp.route('/api/users/<int:area_id>')
@login_required
def api_users_by_area(area_id):
    """获取指定区域的用户（API接口）"""
    try:
        users = User.query.filter_by(area_id=area_id, status=1).all()
        return jsonify({
            'success': True,
            'users': [{'id': u.id, 'name': u.real_name or u.username} for u in users]
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# 辅助函数
def get_notification_type_name(type_code):
    """获取通知类型名称"""
    type_names = get_notification_types()
    return type_names.get(type_code, type_code)

def get_notification_types():
    """获取所有通知类型"""
    return {
        'system': '系统通知',
        'health_cert': '健康证提醒',
        'menu': '食谱通知',
        'purchase': '采购通知',
        'inspection': '检查通知',
        'task': '任务通知',
        'inventory': '库存通知',
        'expiry': '过期提醒',
        'stock_in': '入库通知',
        'consumption': '消耗通知'
    }

def get_notification_levels():
    """获取所有通知级别"""
    return {
        0: '普通',
        1: '重要',
        2: '紧急'
    }

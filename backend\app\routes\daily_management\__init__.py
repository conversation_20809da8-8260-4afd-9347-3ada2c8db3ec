from flask import Blueprint

daily_management_bp = Blueprint('daily_management', __name__, url_prefix='/daily-management')

from . import routes, api, photo_routes, db_routes, image_api, api_v2, inspection_qrcode, public_api

# 注册照片管理路由
from .photo_routes import photo_bp
daily_management_bp.register_blueprint(photo_bp, url_prefix='/photos')

# 注册数据库管理路由
from .db_routes import db_bp
daily_management_bp.register_blueprint(db_bp, url_prefix='/db')

# 注册图片API路由
from .image_api import image_api_bp
daily_management_bp.register_blueprint(image_api_bp, url_prefix='/image-api')

# 注册检查记录API路由
from .inspection_api import inspection_api_bp
daily_management_bp.register_blueprint(inspection_api_bp)

# 注册检查模板API路由
from .template_api import template_api_bp
daily_management_bp.register_blueprint(template_api_bp)

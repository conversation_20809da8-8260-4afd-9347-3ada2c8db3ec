"""
SupplierDelivery 序列化模式
"""

from marshmallow import Schema, fields, validate

class SupplierDeliverySchema(Schema):
    """SupplierDelivery 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    delivery_number = fields.String(required=True)
    
    
    
    order_id = fields.Integer(required=True)
    
    
    
    supplier_id = fields.Integer(required=True)
    
    
    
    delivery_date = fields.Date(required=True)
    
    
    
    carrier_name = fields.String()
    
    
    
    carrier_phone = fields.String()
    
    
    
    vehicle_number = fields.String()
    
    
    
    status = fields.String(required=True)
    
    
    
    notes = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
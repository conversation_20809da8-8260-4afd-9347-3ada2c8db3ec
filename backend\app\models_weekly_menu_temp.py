"""
周菜单临时菜品模型
用于存储周菜单编辑过程中的临时菜品数据
"""

import json
from datetime import datetime
from app import db
from app.utils.datetime_helper import format_datetime
from sqlalchemy import text


class WeeklyMenuRecipesTemp(db.Model):
    """周菜单临时菜品表"""
    __tablename__ = 'weekly_menu_recipes_temp'

    id = db.Column(db.Integer, primary_key=True)
    weekly_menu_id = db.Column(db.Integer, db.ForeignKey('weekly_menus.id'), nullable=True)  # 可以为空，用于新建菜单
    day_of_week = db.Column(db.Integer, nullable=False)  # 星期几 (1-7)
    meal_type = db.Column(db.String(20), nullable=False)  # 餐次类型 (早餐、午餐、晚餐)
    recipe_id = db.Column(db.Integer, db.ForeignKey('recipes.id'), nullable=True)  # 菜品ID，可以为空（自定义菜品）
    recipe_name = db.Column(db.String(100), nullable=False)  # 菜品名称
    is_custom = db.Column(db.<PERSON>, default=False, nullable=False)  # 是否为自定义菜品
    temp_data = db.Column(db.Text)  # 临时数据，JSON格式
    created_at = db.Column(db.DateTime, default=datetime.now, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now, nullable=False)

    # 关联关系
    weekly_menu = db.relationship('WeeklyMenu', backref='temp_recipes')
    recipe = db.relationship('Recipe', backref='temp_weekly_menus')

    def __repr__(self):
        return f'<WeeklyMenuRecipesTemp {self.id}: {self.recipe_name}>'

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'weekly_menu_id': self.weekly_menu_id,
            'day_of_week': self.day_of_week,
            'meal_type': self.meal_type,
            'recipe_id': self.recipe_id,
            'recipe_name': self.recipe_name,
            'is_custom': self.is_custom,
            'temp_data': json.loads(self.temp_data) if self.temp_data else {},
            'created_at': format_datetime(self.created_at, '%Y-%m-%d %H:%M:%S'),
            'updated_at': format_datetime(self.updated_at, '%Y-%m-%d %H:%M:%S')
        }

    @classmethod
    def get_by_weekly_menu(cls, weekly_menu_id):
        """根据周菜单ID获取所有临时菜品"""
        return cls.query.filter_by(weekly_menu_id=weekly_menu_id).all()

    @classmethod
    def get_by_day_meal(cls, weekly_menu_id, day_of_week, meal_type):
        """根据周菜单ID、星期几和餐次获取临时菜品"""
        return cls.query.filter_by(
            weekly_menu_id=weekly_menu_id,
            day_of_week=day_of_week,
            meal_type=meal_type
        ).all()

    @classmethod
    def delete_by_day_meal(cls, weekly_menu_id, day_of_week, meal_type):
        """删除指定周菜单、星期几和餐次的所有临时菜品"""
        cls.query.filter_by(
            weekly_menu_id=weekly_menu_id,
            day_of_week=day_of_week,
            meal_type=meal_type
        ).delete()
        db.session.commit()

    @classmethod
    def delete_by_weekly_menu(cls, weekly_menu_id):
        """删除指定周菜单的所有临时菜品"""
        cls.query.filter_by(weekly_menu_id=weekly_menu_id).delete()
        db.session.commit()

    @classmethod
    def sync_to_weekly_menu_recipes(cls, weekly_menu_id):
        """将临时菜品数据同步到正式的周菜单菜品表"""
        try:
            # 获取所有临时菜品
            temp_recipes = cls.get_by_weekly_menu(weekly_menu_id)
            
            if not temp_recipes:
                return 0

            # 删除现有的周菜单菜品
            from app.models import WeeklyMenuRecipe
            WeeklyMenuRecipe.query.filter_by(weekly_menu_id=weekly_menu_id).delete()

            # 添加新的周菜单菜品
            count = 0
            for temp_recipe in temp_recipes:
                weekly_menu_recipe = WeeklyMenuRecipe(
                    weekly_menu_id=weekly_menu_id,
                    day_of_week=temp_recipe.day_of_week,
                    meal_type=temp_recipe.meal_type,
                    recipe_id=temp_recipe.recipe_id,
                    recipe_name=temp_recipe.recipe_name
                )
                db.session.add(weekly_menu_recipe)
                count += 1

            # 提交事务
            db.session.commit()

            # 清理临时数据
            cls.delete_by_weekly_menu(weekly_menu_id)

            return count

        except Exception as e:
            db.session.rollback()
            raise e

    @classmethod
    def create_from_data(cls, weekly_menu_id, day_of_week, meal_type, recipe_data):
        """从数据创建临时菜品记录"""
        temp_recipe = cls(
            weekly_menu_id=weekly_menu_id,
            day_of_week=day_of_week,
            meal_type=meal_type,
            recipe_id=recipe_data.get('recipe_id'),
            recipe_name=recipe_data.get('recipe_name', ''),
            is_custom=recipe_data.get('is_custom', False),
            temp_data=json.dumps(recipe_data)
        )
        db.session.add(temp_recipe)
        return temp_recipe

    @classmethod
    def bulk_create_from_menu_data(cls, weekly_menu_id, menu_data):
        """批量创建临时菜品记录"""
        try:
            # 先清理现有的临时数据
            cls.delete_by_weekly_menu(weekly_menu_id)

            count = 0
            for day_data in menu_data:
                day_of_week = day_data.get('day_of_week')
                for meal_type, recipes in day_data.get('meals', {}).items():
                    for recipe_data in recipes:
                        cls.create_from_data(weekly_menu_id, day_of_week, meal_type, recipe_data)
                        count += 1

            db.session.commit()
            return count

        except Exception as e:
            db.session.rollback()
            raise e

    def update_data(self, recipe_data):
        """更新临时菜品数据"""
        self.recipe_id = recipe_data.get('recipe_id', self.recipe_id)
        self.recipe_name = recipe_data.get('recipe_name', self.recipe_name)
        self.is_custom = recipe_data.get('is_custom', self.is_custom)
        self.temp_data = json.dumps(recipe_data)
        self.updated_at = datetime.now()
        db.session.commit()

    @classmethod
    def get_menu_structure(cls, weekly_menu_id):
        """获取周菜单的结构化数据"""
        temp_recipes = cls.get_by_weekly_menu(weekly_menu_id)
        
        # 组织数据结构
        menu_structure = {}
        for temp_recipe in temp_recipes:
            day = temp_recipe.day_of_week
            meal = temp_recipe.meal_type
            
            if day not in menu_structure:
                menu_structure[day] = {}
            if meal not in menu_structure[day]:
                menu_structure[day][meal] = []
                
            menu_structure[day][meal].append(temp_recipe.to_dict())
        
        return menu_structure

    @classmethod
    def has_temp_data(cls, weekly_menu_id):
        """检查是否有临时数据"""
        return cls.query.filter_by(weekly_menu_id=weekly_menu_id).count() > 0

"""
HomepageCarousel 序列化模式
"""

from marshmallow import Schema, fields, validate

class HomepageCarouselSchema(Schema):
    """HomepageCarousel 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    title = fields.String(required=True)
    
    
    
    description = fields.String()
    
    
    
    image_path = fields.String(required=True)
    
    
    
    link_url = fields.String()
    
    
    
    sort_order = fields.Integer(required=True)
    
    
    
    is_active = fields.Boolean(required=True)
    
    
    
    created_by = fields.Integer()
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
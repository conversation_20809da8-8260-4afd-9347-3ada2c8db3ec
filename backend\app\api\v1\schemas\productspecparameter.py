"""
ProductSpecParameter 序列化模式
"""

from marshmallow import Schema, fields, validate

class ProductSpecParameterSchema(Schema):
    """ProductSpecParameter 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    product_id = fields.Integer(required=True)
    
    
    
    param_name = fields.String(required=True)
    
    
    
    param_value = fields.String(required=True)
    
    
    
    param_unit = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
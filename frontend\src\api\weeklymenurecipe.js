import request from '@/utils/request'

const weeklymenurecipeAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/weeklymenurecipe',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/weeklymenurecipe/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/weeklymenurecipe',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/weeklymenurecipe/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/weeklymenurecipe/${id}`,
      method: 'delete'
    })
  }
}

export default weeklymenurecipeAPI
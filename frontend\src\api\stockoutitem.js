import request from '@/utils/request'

const stockoutitemAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/stockoutitem',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/stockoutitem/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/stockoutitem',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/stockoutitem/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/stockoutitem/${id}`,
      method: 'delete'
    })
  }
}

export default stockoutitemAPI
"""
TraceDocument 服务层
"""

from typing import Optional, Dict, Any
from sqlalchemy.orm import Session
from app.models.models_ingredient_traceability import TraceDocument
from app.utils.database import get_db_session

class TraceDocumentService:
    """TraceDocument 服务"""

    @staticmethod
    def get_list(page: int = 1, per_page: int = 10, **filters):
        """获取TraceDocument列表"""
        with get_db_session() as session:
            query = session.query(TraceDocument)

            # 应用过滤条件
            for key, value in filters.items():
                if hasattr(TraceDocument, key) and value is not None:
                    query = query.filter(getattr(TraceDocument, key) == value)

            return query.paginate(
                page=page,
                per_page=per_page,
                error_out=False
            )

    @staticmethod
    def get_by_id(id: int) -> Optional[TraceDocument]:
        """根据ID获取TraceDocument"""
        with get_db_session() as session:
            return session.query(TraceDocument).filter(TraceDocument.id == id).first()

    @staticmethod
    def create(data: Dict[str, Any]) -> TraceDocument:
        """创建TraceDocument"""
        with get_db_session() as session:
            item = TraceDocument(**data)
            session.add(item)
            session.commit()
            session.refresh(item)
            return item

    @staticmethod
    def update(id: int, data: Dict[str, Any]) -> Optional[TraceDocument]:
        """更新TraceDocument"""
        with get_db_session() as session:
            item = session.query(TraceDocument).filter(TraceDocument.id == id).first()
            if not item:
                return None

            for key, value in data.items():
                if hasattr(item, key):
                    setattr(item, key, value)

            session.commit()
            session.refresh(item)
            return item

    @staticmethod
    def delete(id: int) -> bool:
        """删除TraceDocument"""
        with get_db_session() as session:
            item = session.query(TraceDocument).filter(TraceDocument.id == id).first()
            if not item:
                return False

            session.delete(item)
            session.commit()
            return True
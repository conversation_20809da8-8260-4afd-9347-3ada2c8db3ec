"""
DeliveryItemInspection 序列化模式
"""

from marshmallow import Schema, fields, validate

class DeliveryItemInspectionSchema(Schema):
    """DeliveryItemInspection 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    inspection_id = fields.Integer(required=True)
    
    
    
    delivery_item_id = fields.Integer(required=True)
    
    
    
    accepted_quantity = fields.Float(required=True)
    
    
    
    rejected_quantity = fields.Float(required=True)
    
    
    
    rejection_reason = fields.String()
    
    
    
    notes = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
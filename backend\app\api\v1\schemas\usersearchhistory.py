"""
UserSearchHistory 序列化模式
"""

from marshmallow import Schema, fields, validate

class UserSearchHistorySchema(Schema):
    """UserSearchHistory 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    user_id = fields.Integer(required=True)
    
    
    
    search_query = fields.String(required=True)
    
    
    
    search_filters = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
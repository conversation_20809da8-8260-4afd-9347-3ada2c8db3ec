"""
SpecialEvent 序列化模式
"""

from marshmallow import Schema, fields, validate

class SpecialEventSchema(Schema):
    """SpecialEvent 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    daily_log_id = fields.Integer(required=True)
    
    
    
    event_type = fields.String(required=True)
    
    
    
    event_time = fields.String(required=True)
    
    
    
    description = fields.String(required=True)
    
    
    
    participants = fields.String()
    
    
    
    handling_measures = fields.String()
    
    
    
    event_summary = fields.String()
    
    
    
    photo_paths = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
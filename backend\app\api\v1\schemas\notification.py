"""
Notification 序列化模式
"""

from marshmallow import Schema, fields, validate

class NotificationSchema(Schema):
    """Notification 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    user_id = fields.Integer(required=True)
    
    
    
    title = fields.String(required=True)
    
    
    
    content = fields.String(required=True)
    
    
    
    notification_type = fields.String(required=True)
    
    
    
    level = fields.Integer()
    
    
    
    reference_id = fields.Integer()
    
    
    
    reference_type = fields.String()
    
    
    
    is_read = fields.Boolean()
    
    
    
    created_at = fields.String()
    
    

    class Meta:
        ordered = True
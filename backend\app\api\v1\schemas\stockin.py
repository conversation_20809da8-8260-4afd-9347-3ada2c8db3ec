"""
StockIn 序列化模式
"""

from marshmallow import Schema, fields, validate

class StockInSchema(Schema):
    """StockIn 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    stock_in_number = fields.String(required=True)
    
    
    
    warehouse_id = fields.Integer(required=True)
    
    
    
    delivery_id = fields.Integer()
    
    
    
    stock_in_date = fields.String(required=True)
    
    
    
    stock_in_type = fields.String(required=True)
    
    
    
    operator_id = fields.Integer(required=True)
    
    
    
    inspector_id = fields.Integer()
    
    
    
    status = fields.String(required=True)
    
    
    
    notes = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
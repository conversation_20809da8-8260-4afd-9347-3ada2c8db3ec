"""
在线咨询路由处理
处理首页在线咨询表单的提交和管理功能
"""

from flask import Blueprint, request, jsonify, render_template, flash, redirect, url_for, current_app
from flask_login import login_required, current_user
from app import db
from app.models_consultation import OnlineConsultation
from app.forms_consultation import OnlineConsultationForm, ConsultationReplyForm, ConsultationSearchForm
from app.utils.permissions import check_permission
import json

bp = Blueprint('consultation', __name__)


@bp.route('/api/submit', methods=['POST'])
def submit_consultation():
    """提交在线咨询（API接口）"""
    try:
        # 获取表单数据
        data = request.get_json() if request.is_json else request.form

        name = data.get('name', '').strip()
        contact_type = data.get('contact_type', '微信').strip()
        contact_value = data.get('contact_value', '').strip()
        content = data.get('content', '').strip()

        # 基本验证
        if not name:
            return jsonify({'success': False, 'message': '请输入您的姓名'})

        if len(name) < 2 or len(name) > 50:
            return jsonify({'success': False, 'message': '姓名长度应在2-50个字符之间'})

        if not contact_value:
            return jsonify({'success': False, 'message': '请输入您的联系方式'})

        if len(contact_value) < 3 or len(contact_value) > 100:
            return jsonify({'success': False, 'message': '联系方式长度应在3-100个字符之间'})

        if not content:
            return jsonify({'success': False, 'message': '请输入咨询内容'})

        if len(content) < 10 or len(content) > 1000:
            return jsonify({'success': False, 'message': '咨询内容长度应在10-1000个字符之间'})

        # 联系方式格式验证
        import re
        if contact_type == '电话':
            phone_pattern = r'^1[3-9]\d{9}$'
            if not re.match(phone_pattern, contact_value):
                return jsonify({'success': False, 'message': '请输入正确的手机号码格式'})
        elif contact_type == '邮箱':
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, contact_value):
                return jsonify({'success': False, 'message': '请输入正确的邮箱格式'})
        elif contact_type == '微信':
            wechat_pattern = r'^[a-zA-Z0-9_-]{6,20}$'
            if not re.match(wechat_pattern, contact_value):
                return jsonify({'success': False, 'message': '微信号应为6-20位字母、数字、下划线或减号组合'})

        # 获取客户端信息
        ip_address = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR'))
        user_agent = request.headers.get('User-Agent', '')[:500]  # 限制长度

        # 创建咨询记录
        consultation = OnlineConsultation.create_consultation(
            name=name,
            contact_type=contact_type,
            contact_value=contact_value,
            content=content,
            ip_address=ip_address,
            user_agent=user_agent
        )

        # 记录日志
        current_app.logger.info(f"新的在线咨询提交: ID={consultation.id}, 姓名={name}, 联系方式={contact_type}:{contact_value}")

        return jsonify({
            'success': True,
            'message': '咨询提交成功！我们会尽快与您联系。',
            'consultation_id': consultation.id
        })

    except Exception as e:
        current_app.logger.error(f"提交在线咨询失败: {str(e)}")
        db.session.rollback()
        return jsonify({'success': False, 'message': '提交失败，请稍后重试'})


@bp.route('/list')
@login_required
@check_permission('consultation', 'view')
def consultation_list():
    """咨询列表页面"""
    form = ConsultationSearchForm()
    page = request.args.get('page', 1, type=int)
    per_page = 20

    # 构建查询
    query = OnlineConsultation.query

    # 搜索条件
    keyword = request.args.get('keyword', '').strip()
    status = request.args.get('status', '').strip()
    contact_type = request.args.get('contact_type', '').strip()

    if keyword:
        query = query.filter(
            db.or_(
                OnlineConsultation.name.contains(keyword),
                OnlineConsultation.content.contains(keyword),
                OnlineConsultation.contact_value.contains(keyword)
            )
        )
        form.keyword.data = keyword

    if status:
        query = query.filter(OnlineConsultation.status == status)
        form.status.data = status

    if contact_type:
        query = query.filter(OnlineConsultation.contact_type == contact_type)
        form.contact_type.data = contact_type

    # 分页查询
    consultations = query.order_by(OnlineConsultation.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )

    # 统计信息
    stats = OnlineConsultation.get_statistics()

    return render_template('consultation/list.html',
                         consultations=consultations,
                         form=form,
                         stats=stats)


@bp.route('/<int:id>')
@login_required
@check_permission('consultation', 'view')
def consultation_detail(id):
    """咨询详情页面"""
    consultation = OnlineConsultation.query.get_or_404(id)
    reply_form = ConsultationReplyForm()

    return render_template('consultation/detail.html',
                         consultation=consultation,
                         reply_form=reply_form)


@bp.route('/<int:id>/reply', methods=['POST'])
@login_required
@check_permission('consultation', 'reply')
def reply_consultation(id):
    """回复咨询"""
    consultation = OnlineConsultation.query.get_or_404(id)
    form = ConsultationReplyForm()

    if form.validate_on_submit():
        try:
            consultation.reply(form.reply_content.data, current_user.id)
            flash('回复发送成功！', 'success')

            # 记录日志
            current_app.logger.info(f"咨询回复成功: ID={consultation.id}, 回复人={current_user.real_name}")

        except Exception as e:
            current_app.logger.error(f"回复咨询失败: {str(e)}")
            flash('回复发送失败，请稍后重试', 'danger')
    else:
        for field, errors in form.errors.items():
            for error in errors:
                flash(f'{error}', 'danger')

    return redirect(url_for('consultation.consultation_detail', id=id))


@bp.route('/<int:id>/close', methods=['POST'])
@login_required
@check_permission('consultation', 'close')
def close_consultation(id):
    """关闭咨询"""
    consultation = OnlineConsultation.query.get_or_404(id)

    try:
        consultation.close()
        flash('咨询已关闭', 'success')

        # 记录日志
        current_app.logger.info(f"咨询关闭: ID={consultation.id}, 操作人={current_user.real_name}")

    except Exception as e:
        current_app.logger.error(f"关闭咨询失败: {str(e)}")
        flash('操作失败，请稍后重试', 'danger')

    return redirect(url_for('consultation.consultation_detail', id=id))


@bp.route('/api/stats')
@login_required
@check_permission('consultation', 'view')
def consultation_stats():
    """获取咨询统计数据（API）"""
    try:
        stats = OnlineConsultation.get_statistics()
        pending_count = OnlineConsultation.get_pending_count()

        return jsonify({
            'success': True,
            'data': {
                'stats': stats,
                'pending_count': pending_count
            }
        })
    except Exception as e:
        current_app.logger.error(f"获取咨询统计失败: {str(e)}")
        return jsonify({'success': False, 'message': '获取统计数据失败'})


@bp.route('/export')
@login_required
@check_permission('consultation', 'export')
def export_consultations():
    """导出咨询数据"""
    try:
        import pandas as pd
        from io import BytesIO
        from flask import make_response

        # 获取所有咨询数据
        consultations = OnlineConsultation.query.order_by(OnlineConsultation.created_at.desc()).all()

        # 转换为DataFrame
        data = []
        for consultation in consultations:
            data.append({
                'ID': consultation.id,
                '姓名': consultation.name,
                '联系方式类型': consultation.contact_type,
                '联系方式': consultation.contact_value,
                '咨询内容': consultation.content,
                '状态': consultation.status,
                '回复内容': consultation.reply_content or '',
                '回复时间': consultation.reply_time.strftime('%Y-%m-%d %H:%M') if consultation.reply_time else '',
                '回复人': consultation.reply_user.real_name if consultation.reply_user else '',
                '来源': consultation.source,
                '提交时间': consultation.created_at.strftime('%Y-%m-%d %H:%M')
            })

        df = pd.DataFrame(data)

        # 创建Excel文件
        output = BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='在线咨询数据', index=False)

        output.seek(0)

        # 创建响应
        response = make_response(output.read())
        response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        response.headers['Content-Disposition'] = 'attachment; filename=consultations.xlsx'

        return response

    except Exception as e:
        current_app.logger.error(f"导出咨询数据失败: {str(e)}")
        flash('导出失败，请稍后重试', 'danger')
        return redirect(url_for('consultation.consultation_list'))

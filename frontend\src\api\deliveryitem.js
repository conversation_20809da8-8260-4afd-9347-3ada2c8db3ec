import request from '@/utils/request'

const deliveryitemAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/deliveryitem',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/deliveryitem/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/deliveryitem',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/deliveryitem/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/deliveryitem/${id}`,
      method: 'delete'
    })
  }
}

export default deliveryitemAPI
import request from '@/utils/request'

const diningcompanionAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/diningcompanion',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/diningcompanion/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/diningcompanion',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/diningcompanion/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/diningcompanion/${id}`,
      method: 'delete'
    })
  }
}

export default diningcompanionAPI
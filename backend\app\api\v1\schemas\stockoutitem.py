"""
StockOutItem 序列化模式
"""

from marshmallow import Schema, fields, validate

class StockOutItemSchema(Schema):
    """StockOutItem 序列化模式"""

    
    
    id = fields.Integer()
    
    
    
    stock_out_id = fields.Integer(required=True)
    
    
    
    inventory_id = fields.Integer(required=True)
    
    
    
    ingredient_id = fields.Integer(required=True)
    
    
    
    batch_number = fields.String(required=True)
    
    
    
    quantity = fields.Float(required=True)
    
    
    
    unit = fields.String(required=True)
    
    
    
    consumption_detail_id = fields.Integer()
    
    
    
    notes = fields.String()
    
    
    
    created_at = fields.String(required=True)
    
    
    
    updated_at = fields.String(required=True)
    
    

    class Meta:
        ordered = True
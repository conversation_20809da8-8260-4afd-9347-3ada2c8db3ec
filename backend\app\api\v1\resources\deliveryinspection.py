"""
DeliveryInspection API 资源
"""

from flask import request
from flask_restful import Resource
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.services.deliveryinspection_service import DeliveryInspectionService
from app.api.v1.schemas.deliveryinspection import DeliveryInspectionSchema
from app.utils.response import success_response, error_response

class DeliveryInspectionListAPI(Resource):
    """DeliveryInspection 列表 API"""

    @jwt_required()
    def get(self):
        """获取DeliveryInspection列表"""
        try:
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 10, type=int)

            result = DeliveryInspectionService.get_list(page=page, per_page=per_page)

            schema = DeliveryInspectionSchema(many=True)
            return success_response(
                data={
                    'items': schema.dump(result.items),
                    'total': result.total,
                    'page': result.page,
                    'per_page': result.per_page
                }
            )
        except Exception as e:
            return error_response(message=str(e))

    @jwt_required()
    def post(self):
        """创建DeliveryInspection"""
        try:
            data = request.get_json()
            schema = DeliveryInspectionSchema()

            # 验证数据
            validated_data = schema.load(data)

            # 创建记录
            item = DeliveryInspectionService.create(validated_data)

            return success_response(
                data=schema.dump(item),
                message='创建成功'
            )
        except Exception as e:
            return error_response(message=str(e))

class DeliveryInspectionAPI(Resource):
    """DeliveryInspection 详情 API"""

    @jwt_required()
    def get(self, id):
        """获取DeliveryInspection详情"""
        try:
            item = DeliveryInspectionService.get_by_id(id)
            if not item:
                return error_response(message='记录不存在', code=404)

            schema = DeliveryInspectionSchema()
            return success_response(data=schema.dump(item))
        except Exception as e:
            return error_response(message=str(e))

    @jwt_required()
    def put(self, id):
        """更新DeliveryInspection"""
        try:
            data = request.get_json()
            schema = DeliveryInspectionSchema()

            # 验证数据
            validated_data = schema.load(data, partial=True)

            # 更新记录
            item = DeliveryInspectionService.update(id, validated_data)
            if not item:
                return error_response(message='记录不存在', code=404)

            return success_response(
                data=schema.dump(item),
                message='更新成功'
            )
        except Exception as e:
            return error_response(message=str(e))

    @jwt_required()
    def delete(self, id):
        """删除DeliveryInspection"""
        try:
            success = DeliveryInspectionService.delete(id)
            if not success:
                return error_response(message='记录不存在', code=404)

            return success_response(message='删除成功')
        except Exception as e:
            return error_response(message=str(e))
import request from '@/utils/request'

const healthcertificateAPI = {
  // 获取列表
  getList(params) {
    return request({
      url: '/healthcertificate',
      method: 'get',
      params
    })
  },

  // 获取详情
  getById(id) {
    return request({
      url: `/healthcertificate/${id}`,
      method: 'get'
    })
  },

  // 创建
  create(data) {
    return request({
      url: '/healthcertificate',
      method: 'post',
      data
    })
  },

  // 更新
  update(id, data) {
    return request({
      url: `/healthcertificate/${id}`,
      method: 'put',
      data
    })
  },

  // 删除
  delete(id) {
    return request({
      url: `/healthcertificate/${id}`,
      method: 'delete'
    })
  }
}

export default healthcertificateAPI